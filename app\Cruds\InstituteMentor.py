"""
Institute-Mentor Relationship CRUD Operations

This module handles the relationship between institutes and mentors,
including exam/competition assignments and mentor access control.
"""

import uuid
from datetime import datetime, timezone
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import func, and_, or_
from fastapi import HTTPException, status
from typing import List, Optional, Dict, Any

# Import Models
from Models.users import User, UserTypeEnum
from Models.Exam import Exam
from Models.Events import Event
from Models.Competitions import CompetitionMentorAssignment, MentorAssignmentStatusEnum

# Import Schemas
from Schemas.InstituteMentor import (
    InstituteMentorAssignmentCreate, InstituteMentorAssignmentOut,
    InstituteMentorAssignmentUpdate, MentorExamAccessOut,
    MentorCompetitionAccessOut
)


# ==================== INSTITUTE-MENTOR ASSIGNMENT ====================

def assign_mentor_to_institute(db: Session, assignment_data: InstituteMentorAssignmentCreate, institute_id: uuid.UUID) -> InstituteMentorAssignmentOut:
    """Assign a mentor to an institute for exam/competition access"""
    try:
        # Validate mentor exists
        mentor = db.query(User).filter(
            User.id == assignment_data.mentor_id,
            User.user_type == UserTypeEnum.mentor,
            User.is_active == True
        ).first()
        
        if not mentor:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Mentor not found or not active"
            )
        
        # Check if assignment already exists
        existing_assignment = db.query(CompetitionMentorAssignment).filter(
            CompetitionMentorAssignment.mentor_id == assignment_data.mentor_id,
            CompetitionMentorAssignment.institute_id == institute_id
        ).first()
        
        if existing_assignment:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Mentor already assigned to this institute"
            )
        
        # Create assignment record
        assignment = CompetitionMentorAssignment(
            mentor_id=assignment_data.mentor_id,
            institute_id=institute_id,
            assigned_by=institute_id,
            assignment_type="institute_mentor",
            status=MentorAssignmentStatusEnum.ASSIGNED,
            assigned_at=datetime.now(timezone.utc),
            workload_capacity=assignment_data.workload_capacity or 10,
            specialization_areas=assignment_data.specialization_areas,
            hourly_rate=assignment_data.hourly_rate,
            assignment_notes=assignment_data.assignment_notes
        )
        
        db.add(assignment)
        db.commit()
        db.refresh(assignment)
        
        return InstituteMentorAssignmentOut.model_validate(assignment)
        
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error assigning mentor: {str(e)}"
        )


def get_institute_mentors(db: Session, institute_id: uuid.UUID) -> List[InstituteMentorAssignmentOut]:
    """Get all mentors assigned to an institute"""
    try:
        assignments = db.query(CompetitionMentorAssignment).filter(
            CompetitionMentorAssignment.institute_id == institute_id,
            CompetitionMentorAssignment.assignment_type == "institute_mentor"
        ).options(
            joinedload(CompetitionMentorAssignment.mentor)
        ).all()
        
        return [InstituteMentorAssignmentOut.model_validate(assignment) for assignment in assignments]
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting institute mentors: {str(e)}"
        )


def update_mentor_assignment(db: Session, assignment_id: uuid.UUID, update_data: InstituteMentorAssignmentUpdate, institute_id: uuid.UUID) -> InstituteMentorAssignmentOut:
    """Update mentor assignment details"""
    try:
        assignment = db.query(CompetitionMentorAssignment).filter(
            CompetitionMentorAssignment.id == assignment_id,
            CompetitionMentorAssignment.institute_id == institute_id
        ).first()
        
        if not assignment:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Assignment not found"
            )
        
        # Update fields
        update_dict = update_data.model_dump(exclude_unset=True)
        for field, value in update_dict.items():
            setattr(assignment, field, value)
        
        assignment.updated_at = datetime.now(timezone.utc)
        db.commit()
        db.refresh(assignment)
        
        return InstituteMentorAssignmentOut.model_validate(assignment)
        
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error updating assignment: {str(e)}"
        )


def remove_mentor_from_institute(db: Session, mentor_id: uuid.UUID, institute_id: uuid.UUID) -> Dict[str, str]:
    """Remove mentor assignment from institute"""
    try:
        assignment = db.query(CompetitionMentorAssignment).filter(
            CompetitionMentorAssignment.mentor_id == mentor_id,
            CompetitionMentorAssignment.institute_id == institute_id,
            CompetitionMentorAssignment.assignment_type == "institute_mentor"
        ).first()
        
        if not assignment:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Mentor assignment not found"
            )
        
        # Check if mentor has active competition assignments
        active_competitions = db.query(CompetitionMentorAssignment).filter(
            CompetitionMentorAssignment.mentor_id == mentor_id,
            CompetitionMentorAssignment.institute_id == institute_id,
            CompetitionMentorAssignment.status.in_([
                MentorAssignmentStatusEnum.ASSIGNED,
                MentorAssignmentStatusEnum.ACCEPTED
            ])
        ).count()
        
        if active_competitions > 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Cannot remove mentor with {active_competitions} active competition assignments"
            )
        
        db.delete(assignment)
        db.commit()
        
        return {"message": "Mentor removed from institute successfully"}
        
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error removing mentor: {str(e)}"
        )


# ==================== EXAM ASSIGNMENT TO MENTORS ====================

def assign_exam_to_mentor(db: Session, exam_id: uuid.UUID, mentor_id: uuid.UUID, institute_id: uuid.UUID, access_type: str = "review") -> Dict[str, Any]:
    """Assign an exam to a mentor for review or competition preparation"""
    try:
        # Validate exam belongs to institute
        exam = db.query(Exam).filter(
            Exam.id == exam_id,
            Exam.created_by == institute_id  # Assuming institute creates exams
        ).first()
        
        if not exam:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Exam not found or access denied"
            )
        
        # Validate mentor is assigned to institute
        mentor_assignment = db.query(CompetitionMentorAssignment).filter(
            CompetitionMentorAssignment.mentor_id == mentor_id,
            CompetitionMentorAssignment.institute_id == institute_id,
            CompetitionMentorAssignment.assignment_type == "institute_mentor"
        ).first()
        
        if not mentor_assignment:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Mentor not assigned to this institute"
            )
        
        # Create exam access record (you might need a separate table for this)
        # For now, we'll use the existing assignment and add metadata
        if not mentor_assignment.assigned_exams:
            mentor_assignment.assigned_exams = []
        
        exam_access = {
            "exam_id": str(exam_id),
            "access_type": access_type,
            "assigned_at": datetime.now(timezone.utc).isoformat(),
            "status": "active"
        }
        
        mentor_assignment.assigned_exams.append(exam_access)
        db.commit()
        
        return {
            "message": "Exam assigned to mentor successfully",
            "exam_id": exam_id,
            "mentor_id": mentor_id,
            "access_type": access_type
        }
        
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error assigning exam to mentor: {str(e)}"
        )


def get_mentor_assigned_exams(db: Session, mentor_id: uuid.UUID, institute_id: uuid.UUID = None) -> List[MentorExamAccessOut]:
    """Get exams assigned to a mentor"""
    try:
        query = db.query(CompetitionMentorAssignment).filter(
            CompetitionMentorAssignment.mentor_id == mentor_id,
            CompetitionMentorAssignment.assignment_type == "institute_mentor"
        )
        
        if institute_id:
            query = query.filter(CompetitionMentorAssignment.institute_id == institute_id)
        
        assignments = query.all()
        
        exam_accesses = []
        for assignment in assignments:
            if assignment.assigned_exams:
                for exam_access in assignment.assigned_exams:
                    exam = db.query(Exam).filter(Exam.id == exam_access["exam_id"]).first()
                    if exam:
                        exam_accesses.append(MentorExamAccessOut(
                            exam_id=exam.id,
                            exam_title=exam.title,
                            exam_description=exam.description,
                            access_type=exam_access["access_type"],
                            assigned_at=exam_access["assigned_at"],
                            status=exam_access["status"],
                            institute_id=assignment.institute_id
                        ))
        
        return exam_accesses
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting mentor exams: {str(e)}"
        )


def get_mentor_assigned_competitions(db: Session, mentor_id: uuid.UUID, institute_id: uuid.UUID = None) -> List[MentorCompetitionAccessOut]:
    """Get competitions assigned to a mentor"""
    try:
        query = db.query(CompetitionMentorAssignment).filter(
            CompetitionMentorAssignment.mentor_id == mentor_id,
            CompetitionMentorAssignment.competition_id.isnot(None)
        )
        
        if institute_id:
            query = query.filter(CompetitionMentorAssignment.institute_id == institute_id)
        
        assignments = query.options(
            joinedload(CompetitionMentorAssignment.competition)
        ).all()
        
        return [MentorCompetitionAccessOut.model_validate(assignment) for assignment in assignments]
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting mentor competitions: {str(e)}"
        )


# ==================== MENTOR ACCESS VALIDATION ====================

def validate_mentor_exam_access(db: Session, mentor_id: uuid.UUID, exam_id: uuid.UUID) -> bool:
    """Validate if mentor has access to a specific exam"""
    try:
        # Check if mentor is assigned to any institute that has this exam
        assignments = db.query(CompetitionMentorAssignment).filter(
            CompetitionMentorAssignment.mentor_id == mentor_id,
            CompetitionMentorAssignment.assignment_type == "institute_mentor"
        ).all()
        
        for assignment in assignments:
            if assignment.assigned_exams:
                for exam_access in assignment.assigned_exams:
                    if exam_access["exam_id"] == str(exam_id) and exam_access["status"] == "active":
                        return True
        
        return False
        
    except Exception as e:
        return False


def validate_mentor_competition_access(db: Session, mentor_id: uuid.UUID, competition_id: uuid.UUID) -> bool:
    """Validate if mentor has access to a specific competition"""
    try:
        assignment = db.query(CompetitionMentorAssignment).filter(
            CompetitionMentorAssignment.mentor_id == mentor_id,
            CompetitionMentorAssignment.competition_id == competition_id,
            CompetitionMentorAssignment.status.in_([
                MentorAssignmentStatusEnum.ASSIGNED,
                MentorAssignmentStatusEnum.ACCEPTED
            ])
        ).first()
        
        return assignment is not None
        
    except Exception as e:
        return False
