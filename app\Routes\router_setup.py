"""
Router setup module for organizing all API routes.

This module centralizes the configuration of all API routers and their prefixes.
"""

from fastapi import FastAPI

# Import all routers
from Routes.users import router as user_router
from Routes.health import router as health_router
from Routes.TeacherModule.Classroom import router as classroom_router
from Routes.Exams.Subjects import router as subject_router
from Routes.Exams.Chapters import router as chapter_router
from Routes.Exams.Topics import router as topic_router
from Routes.Exams.Subtopics import router as subtopic_router
from Routes.TeacherModule.Tasks import router as task_router
from Routes.TeacherModule.TeacherProfile import router as teacher_profile_router
from Routes.Exams.Plan import router as plan_router
from Routes.TeacherModule.announcement import router as announcement_router
from Routes.TeacherModule.Class import router as class_router
from Routes.Exams.Questions import router as question_router
from Routes.Exams.Exam import router as exam_router
from Routes.Exams.ExamSession import router as exam_session_router
from Routes.Exams.ExamChecking import router as exam_checking_router
from Routes.StudentDashboard import router as student_dashboard_router
from Routes.StudentStatistics import router as student_statistics_router
from Routes.file_upload import router as file_upload_router
from Routes.file_admin import router as file_admin_router
# Event routes
from Routes.Events.Events import router as events_router
from Routes.Events.EventSpeakers import router as event_speakers_router
from Routes.Events.EventTickets import router as event_tickets_router
from Routes.Events.EventRegistrations import router as event_registrations_router
from Routes.Events.EventBooking import router as event_booking_router
from Routes.Events.SimpleTicketing import router as simple_ticketing_router
from Routes.Events.TicketManagement import router as ticket_management_router
from Routes.Events.Competitions import router as competitions_router

# Institute-Mentor relationship routes
from Routes.InstituteMentor import router as institute_mentor_router

from Routes.Admin.EventManagement import router as admin_event_management_router
from Routes.Universal.EventAccess import router as universal_event_access_router
from Routes.Institute.Institute import router as institute_router
from Routes.Institute.Mentor import router as mentor_router
from Routes.Institute.Dashboard import router as institute_dashboard_router
from Routes.Institute.MentorInstitute import router as mentor_institutes_router
from Routes.Subscriptions import router as subscriptions_router
# PayFast removed for demo purposes
from Routes.Auth.email_verification import router as email_verification_router
from Routes.Mentors.Collaborations import router as mentor_collaborations_router
# Import admin routes
from Routes.admin_routes import admin_institute_router

# Import social feature routes
from Routes.Social.UserFollow import router as user_follow_router
from Routes.Social.Chat import router as chat_router
from Routes.Social.WebSocketChat import router as websocket_chat_router
from Routes.Notifications import router as notifications_router
from Routes.AdminLog import router as admin_log_router

# Import competition exam routes
from Routes.Events.CompetitionExam import router as competition_exam_router

# Import student analytics routes
from Routes.StudentAnalytics.Analytics import router as student_analytics_router


def setup_routers(app: FastAPI):
    """
    Configure all API routers for the FastAPI application.
    
    Args:
        app (FastAPI): The FastAPI application instance
    """
    # Health check router (no authentication required)
    app.include_router(health_router, prefix="/api/health", tags=["health"])
    
    # Authentication and user management
    app.include_router(user_router, prefix="/api/users", tags=["users"])
    app.include_router(email_verification_router, prefix="/api", tags=["email-verification"])
    
    # Teacher module routes
    app.include_router(classroom_router, prefix="/api/classrooms", tags=["classrooms"])
    app.include_router(task_router, prefix="/api/tasks", tags=["tasks"])
    app.include_router(teacher_profile_router, prefix="/api/teachers", tags=["teacherProfiles"])
    app.include_router(announcement_router, prefix="/api/announcements", tags=["announcements"])
    app.include_router(class_router, prefix="/api/classes", tags=["classes"])
    
    # Exam system routes
    app.include_router(subject_router, prefix="/api/subjects", tags=["subjects"])
    app.include_router(chapter_router, prefix="/api/chapters", tags=["chapters"])
    app.include_router(topic_router, prefix="/api/topics", tags=["topics"])
    app.include_router(subtopic_router, prefix="/api/subtopics", tags=["subtopics"])
    app.include_router(plan_router, prefix="/api/plans", tags=["plans"])
    app.include_router(question_router, prefix="/api/questions", tags=["questions"])
    app.include_router(exam_router, prefix="/api/exams", tags=["exams"])
    app.include_router(exam_session_router, prefix="/api/exams/session", tags=["examSession"])
    app.include_router(exam_session_router, tags=["exam-websocket"]) 
    app.include_router(exam_checking_router, prefix="/api/exams/checking", tags=["examChecking"])
    
    # Student routes
    app.include_router(student_dashboard_router, prefix="/api/student", tags=["studentDashboard"])
    app.include_router(student_statistics_router, prefix="/api/student/statistics", tags=["studentStatistics"])
    app.include_router(student_analytics_router,prefix="/api/student/analytics" ,tags=["studentAnalytics"]) 
    
    # File management
    app.include_router(file_upload_router, prefix="/api/files", tags=["fileUpload"])
    app.include_router(file_admin_router, prefix="/api/admin/files", tags=["fileAdmin"])

    # Event management
    app.include_router(events_router, prefix="/api/events", tags=["events"])
    app.include_router(event_speakers_router, prefix="/api/events/speakers", tags=["eventSpeakers"])
    app.include_router(event_tickets_router, prefix="/api/events", tags=["eventTickets"])
    app.include_router(event_registrations_router, prefix="/api/events/registrations", tags=["eventRegistrations"])
    app.include_router(event_booking_router, prefix="/api/events/booking", tags=["eventBooking"])
    app.include_router(simple_ticketing_router, prefix="/api/tickets", tags=["simple-ticketing"])
    app.include_router(ticket_management_router, prefix="/api/tickets", tags=["ticketManagement"])
    app.include_router(universal_event_access_router, prefix="/api/events", tags=["universalEventAccess"])
    app.include_router(competitions_router, prefix="/api/competitions", tags=["competitions"])
    app.include_router(competition_exam_router, prefix="/api/competitions", tags=["competitionExams"])

    # Institute-Mentor relationship routes
    app.include_router(institute_mentor_router, prefix="/api/institute-mentor", tags=["institute-mentor"])

    # Admin management
    app.include_router(admin_event_management_router, prefix="/api/admin", tags=["adminEventManagement"])
    
    # Institute management
    app.include_router(institute_router, prefix="/api/institutes", tags=["institutes"])
    app.include_router(mentor_router, prefix="/api/mentors", tags=["mentors"])
    app.include_router(mentor_institutes_router, prefix="/api/institute/mentors", tags=["institute-mentors"])
    app.include_router(mentor_collaborations_router, prefix="/api/mentor/collaborations", tags=["mentor-collaborations"])
    app.include_router(institute_dashboard_router, prefix="/api/institute/dashboard", tags=["institute-dashboard"])
    
    # Subscriptions
    app.include_router(subscriptions_router, prefix="/api/subscriptions", tags=["subscriptions"])

    # Payment gateways removed for demo purposes

    # Admin routes
    app.include_router(admin_institute_router, prefix="/api/admin", tags=["admin-institutes"])

    # Social features
    app.include_router(user_follow_router, prefix="/api/social/follow", tags=["user-follow"])
    app.include_router(chat_router, prefix="/api/social/chat", tags=["chat"])
    app.include_router(websocket_chat_router, prefix="/api/social/chat", tags=["websocket-chat"])
    app.include_router(notifications_router, prefix="/api/notifications", tags=["notifications"])

    # Admin logging
    app.include_router(admin_log_router, prefix="/api/admin/logs", tags=["admin-logs"])
