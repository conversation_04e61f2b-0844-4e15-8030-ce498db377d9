from pydantic import BaseModel

class ChapterBase(BaseModel):
    name: str
    description: str
    subject_id: str

class TopicBase(BaseModel):
    name: str
    description: str
    chapter_id: str

class SubTopicBase(BaseModel):
    name: str
    description: str
    topic_id: str

class ChapterCreate(ChapterBase):
    pass

class TopicCreate(TopicBase):
    pass

class SubTopicCreate(SubTopicBase):
    pass

