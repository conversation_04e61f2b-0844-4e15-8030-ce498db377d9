import uuid
import random
import time
from typing import Dict, Any, Optional
from decimal import Decimal
from datetime import datetime, timezone
from sqlalchemy.orm import Session
from fastapi import HTTPException

from Models.Events import EventPayment, EventRegistration, PaymentStatusEnum, PaymentGatewayEnum
from Schemas.Events.Events import EventPaymentOut


class DummyPaymentService:
    """
    DEPRECATED: Dummy payment service for testing and development.
    This service is no longer used. The system now operates in demo mode.
    Simulates payment processing without actual payment gateway integration.

    NOTE: This file is kept for reference only. The system now auto-confirms all payments.
    """
    
    def __init__(self):
        self.simulated_failures = False  # Set to True to simulate payment failures
        self.failure_rate = 0.1  # 10% failure rate when simulated_failures is True
        
    def create_payment_intent(
        self, 
        amount: Decimal, 
        currency: str = "USD",
        metadata: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Create a dummy payment intent"""
        
        # Simulate processing delay
        time.sleep(0.1)
        
        # Generate dummy payment intent ID
        payment_intent_id = f"pi_dummy_{uuid.uuid4().hex[:16]}"
        client_secret = f"{payment_intent_id}_secret_{uuid.uuid4().hex[:8]}"
        
        # Simulate occasional failures
        if self.simulated_failures and random.random() < self.failure_rate:
            return {
                "success": False,
                "error": "Simulated payment intent creation failure"
            }
        
        return {
            "success": True,
            "payment_intent_id": payment_intent_id,
            "client_secret": client_secret,
            "status": "requires_payment_method",
            "amount": float(amount),
            "currency": currency,
            "metadata": metadata or {}
        }

    def confirm_payment_intent(self, payment_intent_id: str) -> Dict[str, Any]:
        """Confirm a dummy payment intent"""
        
        # Simulate processing delay
        time.sleep(0.2)
        
        # Simulate occasional failures
        if self.simulated_failures and random.random() < self.failure_rate:
            return {
                "success": False,
                "error": "Simulated payment confirmation failure"
            }
        
        # Generate dummy charge ID
        charge_id = f"ch_dummy_{uuid.uuid4().hex[:16]}"
        
        return {
            "success": True,
            "status": "succeeded",
            "payment_method": "card",
            "amount_received": random.randint(1000, 50000) / 100,  # Random amount in dollars
            "charges": [
                {
                    "id": charge_id,
                    "amount": random.randint(1000, 50000),
                    "currency": "usd",
                    "status": "succeeded",
                    "created": int(datetime.now(timezone.utc).timestamp())
                }
            ]
        }

    def create_refund(
        self, 
        payment_intent_id: str, 
        amount: Optional[Decimal] = None,
        reason: str = "requested_by_customer"
    ) -> Dict[str, Any]:
        """Create a dummy refund"""
        
        # Simulate processing delay
        time.sleep(0.15)
        
        # Simulate occasional failures
        if self.simulated_failures and random.random() < self.failure_rate:
            return {
                "success": False,
                "error": "Simulated refund failure"
            }
        
        # Generate dummy refund ID
        refund_id = f"re_dummy_{uuid.uuid4().hex[:16]}"
        refund_amount = float(amount) if amount else random.randint(1000, 50000) / 100
        
        return {
            "success": True,
            "refund_id": refund_id,
            "status": "succeeded",
            "amount": refund_amount,
            "reason": reason
        }

    def handle_webhook(self, payload: bytes, sig_header: str) -> Dict[str, Any]:
        """Handle dummy webhook events"""
        
        # For dummy service, we'll just validate that we have payload and signature
        if not payload:
            return {
                "success": False,
                "error": "Invalid payload"
            }
        
        if not sig_header:
            return {
                "success": False,
                "error": "Invalid signature"
            }
        
        # Generate dummy event
        dummy_event = {
            "id": f"evt_dummy_{uuid.uuid4().hex[:16]}",
            "type": "payment_intent.succeeded",
            "data": {
                "object": {
                    "id": f"pi_dummy_{uuid.uuid4().hex[:16]}",
                    "status": "succeeded",
                    "amount_received": random.randint(1000, 50000),
                    "latest_charge": f"ch_dummy_{uuid.uuid4().hex[:16]}"
                }
            }
        }
        
        return {
            "success": True,
            "event": dummy_event
        }

    def process_event_payment(
        self,
        db: Session,
        registration_id: uuid.UUID,
        payment_method: str,
        amount: Decimal,
        currency: str = "USD"
    ) -> EventPaymentOut:
        """Process dummy payment for event registration"""
        
        # Get registration
        registration = db.query(EventRegistration).filter(
            EventRegistration.id == registration_id
        ).first()
        
        if not registration:
            raise HTTPException(status_code=404, detail="Registration not found")
        
        if registration.payment_status == PaymentStatusEnum.COMPLETED:
            raise HTTPException(status_code=400, detail="Payment already completed")
        
        # Create dummy payment intent
        metadata = {
            "registration_id": str(registration_id),
            "event_id": str(registration.event_id),
            "user_id": str(registration.user_id)
        }
        
        payment_result = self.create_payment_intent(amount, currency, metadata)
        
        if not payment_result["success"]:
            raise HTTPException(status_code=400, detail=f"Payment failed: {payment_result['error']}")

        # Convert string payment_method to enum if needed
        if isinstance(payment_method, str):
            try:
                payment_method_enum = PaymentGatewayEnum(payment_method.upper())
            except ValueError:
                # If invalid enum value, default to CASH
                payment_method_enum = PaymentGatewayEnum.CASH
        else:
            payment_method_enum = payment_method

        # Create payment record
        payment = EventPayment(
            registration_id=registration_id,
            amount=amount,
            currency=currency,
            payment_method=payment_method_enum,
            payment_gateway="dummy",
            gateway_payment_intent_id=payment_result["payment_intent_id"],
            status=PaymentStatusEnum.PENDING,
            gateway_response=payment_result
        )
        
        db.add(payment)
        db.commit()
        db.refresh(payment)
        
        return EventPaymentOut.model_validate(payment)

    def confirm_event_payment(
        self,
        db: Session,
        payment_id: uuid.UUID,
        payment_intent_id: str
    ) -> EventPaymentOut:
        """Confirm dummy event payment"""
        
        # Get payment record
        payment = db.query(EventPayment).filter(
            EventPayment.id == payment_id,
            EventPayment.gateway_payment_intent_id == payment_intent_id
        ).first()
        
        if not payment:
            raise HTTPException(status_code=404, detail="Payment not found")
        
        # Confirm with dummy service
        confirmation_result = self.confirm_payment_intent(payment_intent_id)
        
        if not confirmation_result["success"]:
            payment.status = PaymentStatusEnum.FAILED
            payment.failed_at = datetime.now(timezone.utc)
            payment.failure_reason = confirmation_result["error"]
            db.commit()
            raise HTTPException(status_code=400, detail=f"Payment confirmation failed: {confirmation_result['error']}")
        
        # Update payment status
        if confirmation_result["status"] == "succeeded":
            payment.status = PaymentStatusEnum.COMPLETED
            payment.processed_at = datetime.now(timezone.utc)
            payment.gateway_transaction_id = confirmation_result["charges"][0]["id"] if confirmation_result["charges"] else None
            
            # Update registration payment status
            registration = db.query(EventRegistration).filter(
                EventRegistration.id == payment.registration_id
            ).first()
            if registration:
                registration.payment_status = PaymentStatusEnum.COMPLETED
                registration.payment_reference = payment.gateway_transaction_id
                registration.payment_method = payment.payment_method
        else:
            payment.status = PaymentStatusEnum.FAILED
            payment.failed_at = datetime.now(timezone.utc)
            payment.failure_reason = f"Payment status: {confirmation_result['status']}"
        
        db.commit()
        db.refresh(payment)
        
        return EventPaymentOut.model_validate(payment)

    def refund_event_payment(
        self,
        db: Session,
        payment_id: uuid.UUID,
        refund_amount: Optional[Decimal] = None,
        refund_reason: str = "requested_by_customer"
    ) -> EventPaymentOut:
        """Refund dummy event payment"""
        
        # Get payment record
        payment = db.query(EventPayment).filter(
            EventPayment.id == payment_id,
            EventPayment.status == PaymentStatusEnum.COMPLETED
        ).first()
        
        if not payment:
            raise HTTPException(status_code=404, detail="Completed payment not found")
        
        if not payment.gateway_payment_intent_id:
            raise HTTPException(status_code=400, detail="No payment intent ID found")
        
        # Create dummy refund
        refund_result = self.create_refund(
            payment.gateway_payment_intent_id,
            refund_amount,
            refund_reason
        )
        
        if not refund_result["success"]:
            raise HTTPException(status_code=400, detail=f"Refund failed: {refund_result['error']}")
        
        # Update payment record
        payment.status = PaymentStatusEnum.REFUNDED
        payment.refunded_at = datetime.now(timezone.utc)
        payment.refund_amount = Decimal(str(refund_result["amount"]))
        payment.refund_reason = refund_reason
        
        # Update registration payment status
        registration = db.query(EventRegistration).filter(
            EventRegistration.id == payment.registration_id
        ).first()
        if registration:
            registration.payment_status = PaymentStatusEnum.REFUNDED
        
        db.commit()
        db.refresh(payment)
        
        return EventPaymentOut.model_validate(payment)

    def enable_failure_simulation(self, failure_rate: float = 0.1):
        """Enable payment failure simulation for testing"""
        self.simulated_failures = True
        self.failure_rate = max(0.0, min(1.0, failure_rate))  # Clamp between 0 and 1
        
    def disable_failure_simulation(self):
        """Disable payment failure simulation"""
        self.simulated_failures = False


# DEPRECATED: Global dummy payment service instances
# These are no longer used. The system now operates in demo mode.
# dummy_payment_service = DummyPaymentService()
# payment_service = dummy_payment_service
