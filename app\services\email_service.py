"""
Email service for sending verification emails and other notifications
"""

import smtplib
import ssl
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.mime.base import MIMEBase
from email import encoders
import logging
from typing import Optional, List
from config.email_config import email_from, email_password, email_host, email_port

# Configure logging
logger = logging.getLogger(__name__)

class EmailService:
    """Service for sending emails"""
    
    def __init__(self):
        self.smtp_server = email_host
        self.port = email_port
        self.sender_email = email_from
        self.password = email_password
    
    def send_email(
        self,
        to_email: str,
        subject: str,
        html_content: str,
        text_content: Optional[str] = None,
        attachments: Optional[List[str]] = None
    ) -> bool:
        """
        Send an email
        
        Args:
            to_email: Recipient email address
            subject: Email subject
            html_content: HTML content of the email
            text_content: Plain text content (optional)
            attachments: List of file paths to attach (optional)
            
        Returns:
            bool: True if email sent successfully, False otherwise
        """
        try:
            # Create message container
            message = MIMEMultipart("alternative")
            message["Subject"] = subject
            message["From"] = self.sender_email
            message["To"] = to_email
            
            # Add text content if provided
            if text_content:
                text_part = MIMEText(text_content, "plain")
                message.attach(text_part)
            
            # Add HTML content
            html_part = MIMEText(html_content, "html")
            message.attach(html_part)
            
            # Add attachments if provided
            if attachments:
                for file_path in attachments:
                    try:
                        with open(file_path, "rb") as attachment:
                            part = MIMEBase('application', 'octet-stream')
                            part.set_payload(attachment.read())
                        
                        encoders.encode_base64(part)
                        part.add_header(
                            'Content-Disposition',
                            f'attachment; filename= {file_path.split("/")[-1]}'
                        )
                        message.attach(part)
                    except Exception as e:
                        logger.warning(f"Failed to attach file {file_path}: {e}")
            
            # Create secure connection and send email
            context = ssl.create_default_context()
            with smtplib.SMTP(self.smtp_server, self.port) as server:
                server.starttls(context=context)
                server.login(self.sender_email, self.password)
                server.sendmail(self.sender_email, to_email, message.as_string())
            
            logger.info(f"Email sent successfully to {to_email}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to send email to {to_email}: {e}")
            return False
    
    def send_verification_email(self, to_email: str, username: str, verification_code: str) -> bool:
        """
        Send email verification email

        Args:
            to_email: Recipient email address
            username: User's username
            verification_code: 6-digit verification code

        Returns:
            bool: True if email sent successfully
        """
        subject = "Verify Your EduFair Account"
        
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Verify Your Email</title>
            <style>
                body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
                .header {{ background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }}
                .content {{ background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }}
                .verification-code {{ display: inline-block; background: #667eea; color: white; padding: 20px 40px; font-size: 32px; font-weight: bold; letter-spacing: 8px; border-radius: 10px; margin: 20px 0; font-family: 'Courier New', monospace; }}
                .code-container {{ text-align: center; background: #e8f4fd; padding: 30px; border-radius: 10px; margin: 20px 0; border: 2px dashed #667eea; }}
                .footer {{ text-align: center; margin-top: 30px; color: #666; font-size: 14px; }}
                .warning {{ background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0; }}
                .instructions {{ background: #e8f5e8; border: 1px solid #4caf50; padding: 15px; border-radius: 5px; margin: 20px 0; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>🎓 Welcome to EduFair!</h1>
                    <p>Verify your email to get started</p>
                </div>
                <div class="content">
                    <h2>Hello {username}!</h2>
                    <p>Thank you for joining EduFair! To complete your registration and start using our platform, please verify your email address by entering the verification code below.</p>

                    <div class="code-container">
                        <p style="margin: 0 0 10px 0; font-weight: bold; color: #333;">Your Verification Code:</p>
                        <div class="verification-code">{verification_code}</div>
                        <p style="margin: 10px 0 0 0; font-size: 14px; color: #666;">Enter this code in the verification form</p>
                    </div>

                    <div class="instructions">
                        <strong>📝 How to verify:</strong>
                        <ol style="margin: 10px 0; padding-left: 20px;">
                            <li>Go to the verification page in your EduFair account</li>
                            <li>Enter the 6-digit code shown above</li>
                            <li>Click "Verify Email" to complete the process</li>
                        </ol>
                    </div>

                    <div class="warning">
                        <strong>⏰ Important:</strong> This verification code will expire in 30 minutes for security reasons.
                    </div>

                    <p>If you didn't create an account with EduFair, please ignore this email.</p>
                </div>
                <div class="footer">
                    <p>© 2025 EduFair. All rights reserved.</p>
                    <p>This is an automated email. Please do not reply to this message.</p>
                </div>
            </div>
        </body>
        </html>
        """
        
        text_content = f"""
        Welcome to EduFair!

        Hello {username}!

        Thank you for joining EduFair! To complete your registration, please verify your email address by entering the verification code below:

        VERIFICATION CODE: {verification_code}

        How to verify:
        1. Go to the verification page in your EduFair account
        2. Enter the 6-digit code: {verification_code}
        3. Click "Verify Email" to complete the process

        This verification code will expire in 30 minutes for security reasons.

        If you didn't create an account with EduFair, please ignore this email.

        © 2025 EduFair. All rights reserved.
        """
        
        return self.send_email(to_email, subject, html_content, text_content)
    
    def send_password_reset_email(self, to_email: str, username: str, reset_code: str) -> bool:
        """
        Send password reset email

        Args:
            to_email: Recipient email address
            username: User's username
            reset_code: 6-digit reset code

        Returns:
            bool: True if email sent successfully
        """
        subject = "Reset Your EduFair Password"
        
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Reset Your Password</title>
            <style>
                body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
                .header {{ background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }}
                .content {{ background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }}
                .button {{ display: inline-block; background: #ff6b6b; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; }}
                .footer {{ text-align: center; margin-top: 30px; color: #666; font-size: 14px; }}
                .warning {{ background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>🔐 Password Reset</h1>
                    <p>Reset your EduFair password</p>
                </div>
                <div class="content">
                    <h2>Hello {username}!</h2>
                    <p>We received a request to reset your password for your EduFair account. Use the verification code below to reset your password:</p>

                    <div class="code-container">
                        <p style="margin: 0 0 10px 0; font-weight: bold; color: #333;">Your Password Reset Code:</p>
                        <div class="verification-code" style="background: #ff6b6b;">{reset_code}</div>
                        <p style="margin: 10px 0 0 0; font-size: 14px; color: #666;">Enter this code in the password reset form</p>
                    </div>

                    <div class="instructions">
                        <strong>🔐 How to reset your password:</strong>
                        <ol style="margin: 10px 0; padding-left: 20px;">
                            <li>Go to the password reset page</li>
                            <li>Enter the 6-digit code shown above</li>
                            <li>Create your new password</li>
                            <li>Click "Reset Password" to complete</li>
                        </ol>
                    </div>

                    <div class="warning">
                        <strong>⏰ Important:</strong> This reset code will expire in 30 minutes for security reasons.
                    </div>

                    <p>If you didn't request a password reset, please ignore this email. Your password will remain unchanged.</p>
                </div>
                <div class="footer">
                    <p>© 2025 EduFair. All rights reserved.</p>
                    <p>This is an automated email. Please do not reply to this message.</p>
                </div>
            </div>
        </body>
        </html>
        """
        
        text_content = f"""
        Password Reset - EduFair

        Hello {username}!

        We received a request to reset your password for your EduFair account. Use the verification code below to reset your password:

        PASSWORD RESET CODE: {reset_code}

        How to reset your password:
        1. Go to the password reset page
        2. Enter the 6-digit code: {reset_code}
        3. Create your new password
        4. Click "Reset Password" to complete

        This reset code will expire in 30 minutes for security reasons.

        If you didn't request a password reset, please ignore this email. Your password will remain unchanged.

        © 2025 EduFair. All rights reserved.
        """
        
        return self.send_email(to_email, subject, html_content, text_content)

# Create global email service instance
email_service = EmailService()
