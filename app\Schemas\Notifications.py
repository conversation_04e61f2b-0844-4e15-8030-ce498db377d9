"""
Notification Schemas for EduFair Platform

This module contains Pydantic schemas for user notifications.
"""

from pydantic import BaseModel, Field
from uuid import UUID
from datetime import datetime
from typing import Optional, List, Dict, Any
from enum import Enum


# ==================== NOTIFICATION TYPES ====================

class NotificationType(str, Enum):
    """Notification types"""
    FOLLOW = "follow"
    MESSAGE = "message"
    EVENT_REGISTRATION = "event_registration"
    EVENT_REMINDER = "event_reminder"
    EVENT_CANCELLED = "event_cancelled"
    TASK_ASSIGNED = "task_assigned"
    TASK_COMPLETED = "task_completed"
    EXAM_SCHEDULED = "exam_scheduled"
    EXAM_RESULT = "exam_result"
    SUBSCRIPTION_EXPIRY = "subscription_expiry"
    SYSTEM_ANNOUNCEMENT = "system_announcement"
    MENTOR_INVITE = "mentor_invite"
    INSTITUTE_INVITE = "institute_invite"
    PROFILE_VERIFICATION = "profile_verification"


# ==================== BASE SCHEMAS ====================

class NotificationBase(BaseModel):
    """Base schema for notifications"""
    title: str = Field(..., min_length=1, max_length=255)
    message: str = Field(..., min_length=1, max_length=1000)
    notification_type: NotificationType
    related_id: Optional[UUID] = None


class NotificationCreate(NotificationBase):
    """Schema for creating a notification"""
    user_id: UUID = Field(..., description="ID of user to notify")


class NotificationUpdate(BaseModel):
    """Schema for updating a notification"""
    is_read: Optional[bool] = None
    read_at: Optional[datetime] = None


# ==================== RESPONSE SCHEMAS ====================

class NotificationResponse(BaseModel):
    """Schema for notification response"""
    id: UUID
    user_id: UUID
    title: str
    message: str
    notification_type: str
    related_id: Optional[UUID] = None
    is_read: bool = False
    created_at: datetime
    read_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True


class NotificationWithDetails(NotificationResponse):
    """Notification with additional details"""
    related_object: Optional[Dict[str, Any]] = None  # Details about related object
    action_url: Optional[str] = None  # URL for notification action
    priority: str = "normal"  # low, normal, high, urgent


# ==================== LIST RESPONSES ====================

class NotificationListResponse(BaseModel):
    """Response for notification list"""
    notifications: List[NotificationResponse]
    total_count: int
    unread_count: int
    page: int
    page_size: int
    has_next: bool
    has_previous: bool


class NotificationSummary(BaseModel):
    """Summary of notifications by type"""
    notification_type: str
    count: int
    latest_notification: Optional[NotificationResponse] = None


class NotificationDashboard(BaseModel):
    """Notification dashboard data"""
    total_notifications: int = 0
    unread_notifications: int = 0
    notifications_by_type: List[NotificationSummary] = []
    recent_notifications: List[NotificationResponse] = []


# ==================== BULK OPERATIONS ====================

class BulkNotificationCreate(BaseModel):
    """Schema for creating bulk notifications"""
    user_ids: List[UUID] = Field(..., max_items=1000)
    title: str = Field(..., min_length=1, max_length=255)
    message: str = Field(..., min_length=1, max_length=1000)
    notification_type: NotificationType
    related_id: Optional[UUID] = None


class BulkNotificationRead(BaseModel):
    """Schema for marking multiple notifications as read"""
    notification_ids: List[UUID] = Field(..., max_items=100)


class BulkNotificationDelete(BaseModel):
    """Schema for deleting multiple notifications"""
    notification_ids: List[UUID] = Field(..., max_items=100)


class BulkOperationResponse(BaseModel):
    """Response for bulk operations"""
    successful_operations: List[UUID]
    failed_operations: List[dict]  # [{"id": UUID, "error": str}]
    total_processed: int
    success_count: int
    failure_count: int


# ==================== NOTIFICATION PREFERENCES ====================

class NotificationPreferences(BaseModel):
    """User notification preferences"""
    user_id: UUID
    email_notifications: bool = True
    push_notifications: bool = True
    follow_notifications: bool = True
    message_notifications: bool = True
    event_notifications: bool = True
    task_notifications: bool = True
    exam_notifications: bool = True
    system_notifications: bool = True
    
    # Frequency settings
    digest_frequency: str = "daily"  # immediate, daily, weekly, never
    quiet_hours_start: Optional[str] = None  # "22:00"
    quiet_hours_end: Optional[str] = None    # "08:00"


class NotificationPreferencesUpdate(BaseModel):
    """Schema for updating notification preferences"""
    email_notifications: Optional[bool] = None
    push_notifications: Optional[bool] = None
    follow_notifications: Optional[bool] = None
    message_notifications: Optional[bool] = None
    event_notifications: Optional[bool] = None
    task_notifications: Optional[bool] = None
    exam_notifications: Optional[bool] = None
    system_notifications: Optional[bool] = None
    digest_frequency: Optional[str] = None
    quiet_hours_start: Optional[str] = None
    quiet_hours_end: Optional[str] = None


# ==================== NOTIFICATION TEMPLATES ====================

class NotificationTemplate(BaseModel):
    """Template for creating notifications"""
    template_name: str
    title_template: str
    message_template: str
    notification_type: NotificationType
    variables: List[str] = []  # List of variable names used in templates


class TemplatedNotificationCreate(BaseModel):
    """Schema for creating notification from template"""
    template_name: str
    user_ids: List[UUID]
    variables: Dict[str, Any] = {}  # Variable values for template
    related_id: Optional[UUID] = None


# ==================== NOTIFICATION STATISTICS ====================

class NotificationStats(BaseModel):
    """Notification statistics"""
    user_id: UUID
    total_notifications: int = 0
    unread_notifications: int = 0
    notifications_today: int = 0
    notifications_this_week: int = 0
    notifications_this_month: int = 0
    most_common_type: Optional[str] = None
    last_notification_date: Optional[datetime] = None


class SystemNotificationStats(BaseModel):
    """System-wide notification statistics"""
    total_notifications_sent: int = 0
    notifications_sent_today: int = 0
    notifications_sent_this_week: int = 0
    notifications_sent_this_month: int = 0
    average_read_rate: float = 0.0
    most_active_notification_type: Optional[str] = None
    notifications_by_type: Dict[str, int] = {}


# ==================== REAL-TIME UPDATES ====================

class NotificationUpdate(BaseModel):
    """Real-time notification update"""
    action: str  # "created", "read", "deleted"
    notification: NotificationResponse
    unread_count: int
