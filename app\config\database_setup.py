"""
Database setup and initialization module.

This module handles database table creation and initialization logic.
"""

from sqlalchemy import inspect
from config.session import Base, engine


def setup_database():
    """
    Set up and initialize the database tables.
    
    This function creates database tables if they don't exist, with proper
    error handling for existing enums and tables.
    """
    try:
        # Only create tables if they don't exist
        inspector = inspect(engine)
        existing_tables = inspector.get_table_names()

        if not existing_tables or len(existing_tables) < 5:  # If database is mostly empty
            Base.metadata.create_all(bind=engine)
            print("Database tables created successfully")
        else:
            print(f"Database already has {len(existing_tables)} tables - skipping creation")

    except Exception as e:
        if "already exists" in str(e) or "duplicate key" in str(e):
            print(f"Database tables already exist (this is normal): {e}")
        else:
            print(f"Error creating database tables: {e}")
            raise
