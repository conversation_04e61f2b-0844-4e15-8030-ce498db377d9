"""
Institute-Mentor Relationship Routes

API endpoints for managing relationships between institutes and mentors,
including exam/competition assignments and mentor access control.
"""

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session
from typing import List, Optional
from uuid import UUID

# Import dependencies
from config.session import get_db
from config.deps import oauth2_scheme, get_current_user
from config.permission import require_type

# Import CRUD operations
from Cruds.InstituteMentor import (
    assign_mentor_to_institute, get_institute_mentors, update_mentor_assignment,
    remove_mentor_from_institute, assign_exam_to_mentor, get_mentor_assigned_exams,
    get_mentor_assigned_competitions, validate_mentor_exam_access,
    validate_mentor_competition_access
)

# Import schemas
from Schemas.InstituteMentor import (
    InstituteMentorAssignmentCreate, InstituteMentorAssignmentOut,
    InstituteMentorAssignmentUpdate, MentorExamAssignmentCreate,
    MentorExamAccessOut, MentorCompetitionAccessOut, MentorDashboardSummary
)

# Import models for type checking
from Models.users import User

router = APIRouter()


# ==================== INSTITUTE MENTOR MANAGEMENT ====================

@router.post("/assign-mentor", response_model=InstituteMentorAssignmentOut)
def assign_mentor_to_institute_endpoint(
    assignment: InstituteMentorAssignmentCreate,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """
    Assign a mentor to the institute.
    
    Creates a relationship between institute and mentor for exam/competition access.
    """
    current_user = get_current_user(token, db)
    return assign_mentor_to_institute(db, assignment, current_user.id)


@router.get("/my-mentors", response_model=List[InstituteMentorAssignmentOut])
def get_my_institute_mentors_endpoint(
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """
    Get all mentors assigned to the current institute.
    
    Returns list of mentor assignments with details and current workload.
    """
    current_user = get_current_user(token, db)
    return get_institute_mentors(db, current_user.id)


@router.put("/mentor-assignment/{assignment_id}", response_model=InstituteMentorAssignmentOut)
def update_mentor_assignment_endpoint(
    assignment_id: UUID,
    assignment_update: InstituteMentorAssignmentUpdate,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """
    Update mentor assignment details.
    
    Can update workload capacity, compensation, and assignment status.
    """
    current_user = get_current_user(token, db)
    return update_mentor_assignment(db, assignment_id, assignment_update, current_user.id)


@router.delete("/mentor/{mentor_id}")
def remove_mentor_from_institute_endpoint(
    mentor_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """
    Remove mentor assignment from institute.
    
    Cannot remove mentors with active competition assignments.
    """
    current_user = get_current_user(token, db)
    return remove_mentor_from_institute(db, mentor_id, current_user.id)


# ==================== EXAM ASSIGNMENT TO MENTORS ====================

@router.post("/assign-exam-to-mentor")
def assign_exam_to_mentor_endpoint(
    assignment: MentorExamAssignmentCreate,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """
    Assign an exam to a mentor for review or evaluation.
    
    Mentor must be assigned to the institute first.
    """
    current_user = get_current_user(token, db)
    return assign_exam_to_mentor(
        db, 
        assignment.exam_id, 
        assignment.mentor_id, 
        current_user.id, 
        assignment.access_type
    )


@router.get("/mentor/{mentor_id}/exams", response_model=List[MentorExamAccessOut])
def get_mentor_exams_endpoint(
    mentor_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """
    Get exams assigned to a specific mentor by the institute.
    
    Returns list of exams the mentor has access to.
    """
    current_user = get_current_user(token, db)
    return get_mentor_assigned_exams(db, mentor_id, current_user.id)


@router.get("/mentor/{mentor_id}/competitions", response_model=List[MentorCompetitionAccessOut])
def get_mentor_competitions_endpoint(
    mentor_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """
    Get competitions assigned to a specific mentor by the institute.
    
    Returns list of competitions the mentor is evaluating.
    """
    current_user = get_current_user(token, db)
    return get_mentor_assigned_competitions(db, mentor_id, current_user.id)


# ==================== MENTOR ACCESS ENDPOINTS ====================

@router.get("/my-assigned-exams", response_model=List[MentorExamAccessOut])
def get_my_assigned_exams_endpoint(
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("mentor"))
):
    """
    Get exams assigned to the current mentor.
    
    Returns all exams the mentor has access to across all institutes.
    """
    current_user = get_current_user(token, db)
    return get_mentor_assigned_exams(db, current_user.id)


@router.get("/my-assigned-competitions", response_model=List[MentorCompetitionAccessOut])
def get_my_assigned_competitions_endpoint(
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("mentor"))
):
    """
    Get competitions assigned to the current mentor.
    
    Returns all competitions the mentor is evaluating across all institutes.
    """
    current_user = get_current_user(token, db)
    return get_mentor_assigned_competitions(db, current_user.id)


@router.get("/exam/{exam_id}/access-check")
def check_mentor_exam_access_endpoint(
    exam_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("mentor"))
):
    """
    Check if mentor has access to a specific exam.
    
    Returns access status and details.
    """
    current_user = get_current_user(token, db)
    has_access = validate_mentor_exam_access(db, current_user.id, exam_id)
    
    return {
        "exam_id": exam_id,
        "mentor_id": current_user.id,
        "has_access": has_access,
        "access_type": "assigned" if has_access else "denied"
    }


@router.get("/competition/{competition_id}/access-check")
def check_mentor_competition_access_endpoint(
    competition_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("mentor"))
):
    """
    Check if mentor has access to a specific competition.
    
    Returns access status and details.
    """
    current_user = get_current_user(token, db)
    has_access = validate_mentor_competition_access(db, current_user.id, competition_id)
    
    return {
        "competition_id": competition_id,
        "mentor_id": current_user.id,
        "has_access": has_access,
        "access_type": "assigned" if has_access else "denied"
    }


# ==================== MENTOR DASHBOARD ====================

@router.get("/mentor/dashboard")
def get_mentor_dashboard_endpoint(
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("mentor"))
):
    """
    Get mentor dashboard with summary of assignments and workload.
    
    Returns comprehensive overview of mentor's current status.
    """
    current_user = get_current_user(token, db)
    
    # Get mentor's assignments
    exams = get_mentor_assigned_exams(db, current_user.id)
    competitions = get_mentor_assigned_competitions(db, current_user.id)
    
    # Calculate summary metrics
    total_institutes = len(set([exam.institute_id for exam in exams] + [comp.institute_id for comp in competitions]))
    active_assignments = len([exam for exam in exams if exam.status == "active"]) + len([comp for comp in competitions if comp.status in ["assigned", "accepted"]])
    
    dashboard_summary = {
        "mentor_id": current_user.id,
        "mentor_name": f"{current_user.first_name} {current_user.last_name}",
        "total_institutes": total_institutes,
        "active_assignments": active_assignments,
        "pending_evaluations": len([exam for exam in exams if exam.status == "pending"]),
        "completed_evaluations": len([exam for exam in exams if exam.status == "completed"]),
        "current_workload_percentage": min(100, (active_assignments / 10) * 100),  # Assuming max 10 assignments
        "recent_exam_assignments": exams[:5],  # Last 5 exams
        "recent_competition_assignments": competitions[:5]  # Last 5 competitions
    }
    
    return dashboard_summary


# ==================== EXAM ACCESS FOR MENTORS ====================

@router.get("/exam/{exam_id}/details")
def get_exam_details_for_mentor_endpoint(
    exam_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("mentor"))
):
    """
    Get exam details for assigned mentor.
    
    Returns exam information if mentor has access.
    """
    current_user = get_current_user(token, db)
    
    # Validate access
    if not validate_mentor_exam_access(db, current_user.id, exam_id):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied to this exam"
        )
    
    # Import exam CRUD to get details
    from Cruds.Exams.Exam import get_exam_by_id
    return get_exam_by_id(db, exam_id)


@router.get("/competition/{competition_id}/details")
def get_competition_details_for_mentor_endpoint(
    competition_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("mentor"))
):
    """
    Get competition details for assigned mentor.
    
    Returns competition information if mentor has access.
    """
    current_user = get_current_user(token, db)
    
    # Validate access
    if not validate_mentor_competition_access(db, current_user.id, competition_id):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied to this competition"
        )
    
    # Import competition CRUD to get details
    from Cruds.Events.Competitions import get_competition_by_id
    return get_competition_by_id(db, competition_id)
