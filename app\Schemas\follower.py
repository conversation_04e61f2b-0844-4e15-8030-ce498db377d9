from pydantic import BaseModel
from uuid import UUID

class FollowerSchema(BaseModel):
    user_id: UUID
    follower_id: UUID

    class Config:
        from_attributes = True

class FollowRequestSchema(BaseModel):
    user_id: UUID
    follower_id: UUID

    class Config:
        from_attributes = True

class FollowerCreateSchema(BaseModel):
    user_id: UUID
    follower_id: UUID

    class Config:
        from_attributes = True
class FollowRequestCreateSchema(BaseModel):
    user_id: UUID
    follower_id: UUID

    class Config:
        from_attributes = True

class FollowerRemoveSchema(BaseModel):
    user_id: UUID
    follower_id: UUID

    class Config:
        from_attributes = True
class FollowRequestRemoveSchema(BaseModel):
    user_id: UUID
    follower_id: UUID

    class Config:
        from_attributes = True

class FollowerOutputSchema(BaseModel):
    user_id: UUID
    follower_id: UUID

    class Config:
        from_attributes = True

class FollowRequestOutputSchema(BaseModel):
    user_id: UUID
    follower_id: UUID

    class Config:
        from_attributes = True

