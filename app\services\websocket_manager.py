"""
WebSocket Connection Manager for EduFair Platform

This module provides a centralized WebSocket connection manager for real-time messaging
and other real-time features across the platform.
"""

import json
import asyncio
from typing import Dict, List, Set, Optional, Any
from uuid import UUID
from datetime import datetime, timezone
from fastapi import WebSocket, WebSocketDisconnect
from sqlalchemy.orm import Session

from Models.users import User
from config.logging import get_logger

logger = get_logger(__name__)


class ConnectionManager:
    """
    Manages WebSocket connections for real-time messaging.
    
    Features:
    - User-based connection tracking
    - Message broadcasting
    - Connection lifecycle management
    - Error handling and logging
    """
    
    def __init__(self):
        # Active connections: {user_id: {connection_id: websocket}}
        self.active_connections: Dict[str, Dict[str, WebSocket]] = {}
        # Connection metadata: {connection_id: {user_id, connected_at, last_activity}}
        self.connection_metadata: Dict[str, Dict[str, Any]] = {}
        # User online status: {user_id: last_seen}
        self.user_status: Dict[str, datetime] = {}
        
    async def connect(self, websocket: WebSocket, user_id: str, connection_id: str) -> bool:
        """
        Accept a new WebSocket connection.
        
        Args:
            websocket: The WebSocket connection
            user_id: ID of the connecting user
            connection_id: Unique identifier for this connection
            
        Returns:
            bool: True if connection was successful
        """
        try:
            await websocket.accept()
            
            # Initialize user connections if not exists
            if user_id not in self.active_connections:
                self.active_connections[user_id] = {}
            
            # Store connection
            self.active_connections[user_id][connection_id] = websocket
            
            # Store metadata
            self.connection_metadata[connection_id] = {
                "user_id": user_id,
                "connected_at": datetime.now(timezone.utc),
                "last_activity": datetime.now(timezone.utc)
            }
            
            # Update user status
            self.user_status[user_id] = datetime.now(timezone.utc)
            
            logger.info(f"WebSocket connection established for user {user_id}, connection {connection_id}")
            
            # Notify user about successful connection
            await self.send_personal_message({
                "type": "connection_established",
                "message": "Connected to real-time messaging",
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "connection_id": connection_id
            }, user_id)
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to establish WebSocket connection for user {user_id}: {e}")
            return False
    
    def disconnect(self, user_id: str, connection_id: str):
        """
        Remove a WebSocket connection.
        
        Args:
            user_id: ID of the disconnecting user
            connection_id: Unique identifier for the connection
        """
        try:
            # Remove from active connections
            if user_id in self.active_connections:
                if connection_id in self.active_connections[user_id]:
                    del self.active_connections[user_id][connection_id]
                
                # Remove user entry if no more connections
                if not self.active_connections[user_id]:
                    del self.active_connections[user_id]
            
            # Remove metadata
            if connection_id in self.connection_metadata:
                del self.connection_metadata[connection_id]
            
            # Update user status if no more connections
            if user_id not in self.active_connections:
                self.user_status[user_id] = datetime.now(timezone.utc)
            
            logger.info(f"WebSocket connection closed for user {user_id}, connection {connection_id}")
            
        except Exception as e:
            logger.error(f"Error during disconnect for user {user_id}: {e}")
    
    async def send_personal_message(self, message: dict, user_id: str) -> bool:
        """
        Send a message to a specific user across all their connections.
        
        Args:
            message: Message data to send
            user_id: Target user ID
            
        Returns:
            bool: True if message was sent to at least one connection
        """
        if user_id not in self.active_connections:
            return False
        
        message_json = json.dumps(message)
        sent_count = 0
        failed_connections = []
        
        # Send to all user's connections
        for connection_id, websocket in self.active_connections[user_id].items():
            try:
                await websocket.send_text(message_json)
                sent_count += 1
                
                # Update last activity
                if connection_id in self.connection_metadata:
                    self.connection_metadata[connection_id]["last_activity"] = datetime.now(timezone.utc)
                    
            except Exception as e:
                logger.warning(f"Failed to send message to user {user_id}, connection {connection_id}: {e}")
                failed_connections.append(connection_id)
        
        # Clean up failed connections
        for connection_id in failed_connections:
            self.disconnect(user_id, connection_id)
        
        return sent_count > 0
    
    async def send_message_to_conversation(self, message: dict, sender_id: str, receiver_id: str):
        """
        Send a message to both participants in a conversation.
        
        Args:
            message: Message data to send
            sender_id: ID of the message sender
            receiver_id: ID of the message receiver
        """
        # Send to receiver
        await self.send_personal_message(message, receiver_id)
        
        # Send confirmation to sender
        sender_message = message.copy()
        sender_message["type"] = "message_sent"
        await self.send_personal_message(sender_message, sender_id)
    
    def is_user_online(self, user_id: str) -> bool:
        """
        Check if a user is currently online.
        
        Args:
            user_id: User ID to check
            
        Returns:
            bool: True if user has active connections
        """
        return user_id in self.active_connections and len(self.active_connections[user_id]) > 0
    
    def get_online_users(self) -> List[str]:
        """
        Get list of currently online user IDs.
        
        Returns:
            List[str]: List of online user IDs
        """
        return list(self.active_connections.keys())
    
    def get_connection_count(self, user_id: str = None) -> int:
        """
        Get connection count for a user or total connections.
        
        Args:
            user_id: Optional user ID to get specific count
            
        Returns:
            int: Number of connections
        """
        if user_id:
            return len(self.active_connections.get(user_id, {}))
        
        return sum(len(connections) for connections in self.active_connections.values())
    
    async def broadcast_to_online_users(self, message: dict, exclude_user: str = None):
        """
        Broadcast a message to all online users.
        
        Args:
            message: Message to broadcast
            exclude_user: Optional user ID to exclude from broadcast
        """
        for user_id in self.active_connections:
            if exclude_user and user_id == exclude_user:
                continue
            await self.send_personal_message(message, user_id)
    
    def get_user_last_seen(self, user_id: str) -> Optional[datetime]:
        """
        Get the last seen timestamp for a user.
        
        Args:
            user_id: User ID to check
            
        Returns:
            Optional[datetime]: Last seen timestamp or None
        """
        return self.user_status.get(user_id)


# Global connection manager instance
connection_manager = ConnectionManager()
