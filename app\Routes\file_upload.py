"""
File Upload Routes

This module provides FastAPI routes for handling file uploads including
profile pictures and task attachments with proper authentication and validation.
"""

from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Form, logger
from sqlalchemy.orm import Session, joinedload
from typing import List, Optional
from uuid import UUID
import uuid
from datetime import datetime

from config.session import get_db
from config.security import oauth2_scheme
from config.permission import require_type
from config.deps import get_current_user
from config.config import settings
from services.file_storage import file_storage
from Models.users import User
from Models.Tasks import Task, TaskAttachment, StudentTaskAttachment
from Schemas.users import UserOut
from Schemas.TeacherModule.tasks import TaskAttachmentOut, StudentTaskAttachmentOut
# Using uvicorn default logging instead of custom logging
router = APIRouter()

# ===== PROFILE PICTURE UPLOAD ROUTES =====

@router.post("/profile-picture")
async def upload_profile_picture(
    file: UploadFile = File(...),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    current_user: User = Depends(get_current_user)
):
    """
    Upload a profile picture for the current user.

    Args:
        file: The image file to upload
        db: Database session
        token: OAuth2 token
        current_user: Current authenticated user

    Returns:
        Simple success response with profile picture URL
    """
    try:
        # Delete old profile picture if exists
        if current_user.profile_picture:
            file_storage.delete_file(current_user.profile_picture)
            # Also delete thumbnail
            thumbnail_path = current_user.profile_picture.replace(
                "profile_pictures/", "profile_pictures/thumbnails/"
            )
            file_storage.delete_file(thumbnail_path)

        # Save the profile picture
        file_path, thumbnail_path = await file_storage.save_profile_picture(
            file, str(current_user.id)
        )

        # Update user's profile picture in database (store the file path)
        current_user.profile_picture = file_path

        # If user is a mentor, also update mentor_profile.profile_image_url for consistency
        if hasattr(current_user, 'user_type') and current_user.user_type.value == "mentor":
            # Load mentor profile if not already loaded
            if not hasattr(current_user, 'mentor_profile') or current_user.mentor_profile is None:
                from Models.users import MentorProfile
                mentor_profile = db.query(MentorProfile).filter(MentorProfile.user_id == current_user.id).first()
                if mentor_profile:
                    mentor_profile.profile_image_url = file_path
            elif current_user.mentor_profile:
                current_user.mentor_profile.profile_image_url = file_path

        db.commit()

        # Return simple response with the full URL
        profile_picture_url = f"{settings.STATIC_FILES_URL}/{file_path}"
        thumbnail_url = f"{settings.STATIC_FILES_URL}/{thumbnail_path}"

        # Profile picture uploaded successfully

        return {
            "message": "Profile picture uploaded successfully",
            "profile_picture_url": profile_picture_url,
            "thumbnail_url": thumbnail_url
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail="Failed to upload profile picture")

@router.put("/profile-picture", response_model=UserOut)
async def update_profile_picture(
    file: UploadFile = File(...),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    current_user: User = Depends(get_current_user)
):
    """
    Update the profile picture for the current user.
    Deletes the old profile picture if it exists.
    
    Args:
        file: The new image file to upload
        db: Database session
        token: OAuth2 token
        current_user: Current authenticated user
    
    Returns:
        UserOut: Updated user information with new profile picture
    """
    try:
        # Delete old profile picture if it exists
        if current_user.profile_picture:
            file_storage.delete_file(current_user.profile_picture)
            # Also delete thumbnail
            thumbnail_path = current_user.profile_picture.replace(
                "profile_pictures/", "profile_pictures/thumbnails/"
            )
            file_storage.delete_file(thumbnail_path)
        
        # Save the new profile picture
        file_path, thumbnail_path = await file_storage.save_profile_picture(
            file, str(current_user.id)
        )
        
        # Update user's profile picture in database
        current_user.profile_picture = file_path

        # If user is a mentor, also update mentor_profile.profile_image_url for consistency
        if hasattr(current_user, 'user_type') and current_user.user_type.value == "mentor":
            # Load mentor profile if not already loaded
            if not hasattr(current_user, 'mentor_profile') or current_user.mentor_profile is None:
                from Models.users import MentorProfile
                mentor_profile = db.query(MentorProfile).filter(MentorProfile.user_id == current_user.id).first()
                if mentor_profile:
                    mentor_profile.profile_image_url = file_path
            elif current_user.mentor_profile:
                current_user.mentor_profile.profile_image_url = file_path

        db.commit()
        db.refresh(current_user)
        
        # Profile picture updated successfully
        
        return UserOut.from_orm(current_user)
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail="Failed to update profile picture")

@router.delete("/profile-picture", response_model=UserOut)
async def delete_profile_picture(
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    current_user: User = Depends(get_current_user)
):
    """
    Delete the current user's profile picture.
    
    Args:
        db: Database session
        token: OAuth2 token
        current_user: Current authenticated user
    
    Returns:
        UserOut: Updated user information without profile picture
    """
    try:
        if not current_user.profile_picture:
            raise HTTPException(status_code=404, detail="No profile picture to delete")
        
        # Delete the file from storage
        file_storage.delete_file(current_user.profile_picture)
        
        # Also delete thumbnail
        thumbnail_path = current_user.profile_picture.replace(
            "profile_pictures/", "profile_pictures/thumbnails/"
        )
        file_storage.delete_file(thumbnail_path)
        
        # Update database
        current_user.profile_picture = None
        db.commit()
        db.refresh(current_user)
        
        # Profile picture deleted successfully
        
        return UserOut.from_orm(current_user)
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail="Failed to delete profile picture")

# ===== TASK ATTACHMENT UPLOAD ROUTES =====

@router.post("/task-attachment")
async def upload_task_attachment(
    task_id: UUID = Form(...),
    file: UploadFile = File(...),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    current_user: User = Depends(require_type("teacher"))
):
    """
    Upload an attachment for a task (teacher only).

    Args:
        task_id: ID of the task to attach file to
        file: The file to upload
        db: Database session
        token: OAuth2 token
        current_user: Current authenticated user (must be teacher)

    Returns:
        Simple success response with attachment info
    """
    try:
        # Verify task exists and user has permission
        task = db.query(Task).filter(Task.id == task_id).first()
        if not task:
            raise HTTPException(status_code=404, detail="Task not found")

        # Save the file
        file_path = await file_storage.save_task_attachment(
            file, str(current_user.id), str(task_id)
        )

        # Create database record
        attachment = TaskAttachment(
            id=UUID(str(uuid.uuid4())),
            file_url=file_path,
            file_name=file.filename,
            task_id=task_id,
            student_id=None  # Teacher attachment
        )

        db.add(attachment)
        db.commit()

        # Return simple response with the full URL
        download_url = f"{settings.STATIC_FILES_URL}/{file_path}"

        # Task attachment uploaded successfully

        return {
            "message": "Task attachment uploaded successfully",
            "attachment_id": str(attachment.id),
            "file_name": file.filename,
            "download_url": download_url,
            "task_id": str(task_id)
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail="Failed to upload task attachment")

@router.post("/student-task-attachment")
async def upload_student_task_attachment(
    task_id: UUID = Form(...),
    file: UploadFile = File(...),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    current_user: User = Depends(require_type("student"))
):
    """
    Upload an attachment for a task submission (student only).

    Args:
        task_id: ID of the task to submit attachment for
        file: The file to upload
        db: Database session
        token: OAuth2 token
        current_user: Current authenticated user (must be student)

    Returns:
        Simple success response with attachment info
    """
    try:
        # Verify task exists
        task = db.query(Task).filter(Task.id == task_id).first()
        if not task:
            raise HTTPException(status_code=404, detail="Task not found")

        # Save the file
        file_path = await file_storage.save_task_attachment(
            file, str(current_user.id), str(task_id)
        )

        # Create database record
        attachment = StudentTaskAttachment(
            id=UUID(str(uuid.uuid4())),
            file_url=file_path,
            file_name=file.filename,
            task_id=task_id,
            student_id=current_user.id,
            submission_date=datetime.utcnow()
        )

        db.add(attachment)
        db.commit()

        # Return simple response with the full URL
        download_url = f"{settings.STATIC_FILES_URL}/{file_path}"

        # Student task attachment uploaded successfully

        return {
            "message": "Student submission uploaded successfully",
            "attachment_id": str(attachment.id),
            "file_name": file.filename,
            "download_url": download_url,
            "task_id": str(task_id),
            "submission_date": attachment.submission_date.isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail="Failed to upload student task attachment")

@router.delete("/task-attachment/{attachment_id}")
async def delete_task_attachment(
    attachment_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    current_user: User = Depends(require_type("teacher"))
):
    """
    Delete a task attachment (teacher only).
    
    Args:
        attachment_id: ID of the attachment to delete
        db: Database session
        token: OAuth2 token
        current_user: Current authenticated user (must be teacher)
    """
    try:
        # Find the attachment
        attachment = db.query(TaskAttachment).filter(
            TaskAttachment.id == attachment_id
        ).first()
        
        if not attachment:
            raise HTTPException(status_code=404, detail="Attachment not found")
        
        # Delete file from storage
        file_storage.delete_file(attachment.file_url)
        
        # Delete from database
        db.delete(attachment)
        db.commit()
        
        # Task attachment deleted successfully
        
        return {"message": "Attachment deleted successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail="Failed to delete task attachment")

@router.delete("/student-task-attachment/{attachment_id}")
async def delete_student_task_attachment(
    attachment_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    current_user: User = Depends(get_current_user)
):
    """
    Delete a student task attachment.
    Students can only delete their own attachments.
    
    Args:
        attachment_id: ID of the attachment to delete
        db: Database session
        token: OAuth2 token
        current_user: Current authenticated user
    """
    try:
        # Find the attachment
        attachment = db.query(StudentTaskAttachment).filter(
            StudentTaskAttachment.id == attachment_id
        ).first()
        
        if not attachment:
            raise HTTPException(status_code=404, detail="Attachment not found")
        
        # Check permission (students can only delete their own attachments)
        if current_user.user_type.value == "student" and attachment.student_id != current_user.id:
            raise HTTPException(status_code=403, detail="Permission denied")
        
        # Delete file from storage
        file_storage.delete_file(attachment.file_url)
        
        # Delete from database
        db.delete(attachment)
        db.commit()
        
        # Student task attachment deleted successfully
        
        return {"message": "Attachment deleted successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail="Failed to delete student task attachment")


# ===== FILE COMPRESSION MANAGEMENT ROUTES =====

@router.post("/compress/{file_path:path}")
async def compress_file_endpoint(
    file_path: str,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    current_user: User = Depends(get_current_user)
):
    """
    Compress an existing file.

    Args:
        file_path: Relative path to the file to compress
        db: Database session
        token: OAuth2 token
        current_user: Current authenticated user

    Returns:
        Compression result with file information
    """
    try:
        # Compress the file
        compressed_path = file_storage.compress_file(file_path)

        if compressed_path:
            # Get file information
            original_info = file_storage.get_file_info(file_path)
            compressed_info = file_storage.get_file_info(compressed_path)

            if original_info.get("exists") and compressed_info.get("exists"):
                compression_ratio = (1 - compressed_info["size"] / original_info["size"]) * 100

                return {
                    "message": "File compressed successfully",
                    "original_file": {
                        "path": file_path,
                        "size": original_info["size"],
                        "size_mb": original_info["size_mb"]
                    },
                    "compressed_file": {
                        "path": compressed_path,
                        "size": compressed_info["size"],
                        "size_mb": compressed_info["size_mb"]
                    },
                    "compression_ratio": f"{compression_ratio:.1f}%",
                    "space_saved_mb": round((original_info["size"] - compressed_info["size"]) / (1024 * 1024), 2)
                }

        return {"message": "File compression not needed or failed", "file_path": file_path}

    except Exception as e:
        logger.error(f"Failed to compress file {file_path}: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to compress file")


@router.post("/decompress/{compressed_file_path:path}")
async def decompress_file_endpoint(
    compressed_file_path: str,
    output_path: str = None,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    current_user: User = Depends(get_current_user)
):
    """
    Decompress a compressed file.

    Args:
        compressed_file_path: Path to the compressed file
        output_path: Optional output path for decompressed file
        db: Database session
        token: OAuth2 token
        current_user: Current authenticated user

    Returns:
        Decompression result with file information
    """
    try:
        # Decompress the file
        decompressed_path = file_storage.decompress_file(compressed_file_path, output_path)

        if decompressed_path:
            # Get file information
            compressed_info = file_storage.get_file_info(compressed_file_path)
            decompressed_info = file_storage.get_file_info(decompressed_path)

            return {
                "message": "File decompressed successfully",
                "compressed_file": {
                    "path": compressed_file_path,
                    "size": compressed_info.get("size", 0),
                    "size_mb": compressed_info.get("size_mb", 0)
                },
                "decompressed_file": {
                    "path": decompressed_path,
                    "size": decompressed_info.get("size", 0),
                    "size_mb": decompressed_info.get("size_mb", 0)
                }
            }
        else:
            raise HTTPException(status_code=400, detail="Failed to decompress file")

    except Exception as e:
        logger.error(f"Failed to decompress file {compressed_file_path}: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to decompress file")


@router.get("/info/{file_path:path}")
async def get_file_info_endpoint(
    file_path: str,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    current_user: User = Depends(get_current_user)
):
    """
    Get comprehensive information about a file including compression status.

    Args:
        file_path: Relative path to the file
        db: Database session
        token: OAuth2 token
        current_user: Current authenticated user

    Returns:
        Detailed file information including compression status
    """
    try:
        file_info = file_storage.get_file_info(file_path)

        if not file_info.get("exists"):
            raise HTTPException(status_code=404, detail="File not found")

        return {
            "message": "File information retrieved successfully",
            "file_info": file_info
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get file info for {file_path}: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to retrieve file information")


# ===== INSTITUTE DOCUMENT UPLOAD ROUTES =====

@router.post("/institute-document")
async def upload_institute_document(
    document_type: str = Form(...),
    description: Optional[str] = Form(None),
    file: UploadFile = File(...),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    current_user: User = Depends(require_type("institute"))
):
    """
    Upload a document for institute verification (institute only).

    Args:
        document_type: Type of document (accreditation, license, certificate, other)
        description: Optional description of the document
        file: The file to upload
        db: Database session
        token: OAuth2 token
        current_user: Current authenticated user (must be institute)

    Returns:
        Simple success response with document info
    """
    try:
        # Import here to avoid circular imports
        from Models.users import InstituteDocument

        # Validate document type
        valid_types = ["accreditation", "license", "certificate", "other"]
        if document_type.lower() not in valid_types:
            raise HTTPException(
                status_code=400,
                detail=f"Invalid document type. Must be one of: {', '.join(valid_types)}"
            )

        # Check maximum 5 documents limit
        existing_documents_count = db.query(InstituteDocument).filter(
            InstituteDocument.institute_id == current_user.id
        ).count()

        if existing_documents_count >= 5:
            raise HTTPException(
                status_code=400,
                detail="Maximum 5 documents allowed per institute. Please delete an existing document before uploading a new one."
            )

        # Save the file
        file_path = await file_storage.save_institute_document(
            file, str(current_user.id), document_type
        )

        # Create database record
        document = InstituteDocument(
            id=UUID(str(uuid.uuid4())),
            institute_id=current_user.id,
            document_type=document_type.lower(),
            document_url=file_path,
            document_name=file.filename,
            description=description,
            verified=False
        )

        db.add(document)
        db.commit()

        # Return simple response with the full URL
        download_url = f"{settings.STATIC_FILES_URL}/{file_path}"

        return {
            "message": "Institute document uploaded successfully",
            "document_id": str(document.id),
            "file_name": file.filename,
            "document_type": document_type,
            "download_url": download_url,
            "description": description
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail="Failed to upload institute document")


@router.post("/institute-logo")
async def upload_institute_logo(
    file: UploadFile = File(...),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    current_user: User = Depends(require_type("institute"))
):
    """
    Upload a logo for institute (institute only).

    Args:
        file: The logo image file to upload
        db: Database session
        token: OAuth2 token
        current_user: Current authenticated user (must be institute)

    Returns:
        Simple success response with logo info
    """
    try:
        from Models.users import InstituteProfile

        # Delete old logo if exists
        institute_profile = db.query(InstituteProfile).filter(
            InstituteProfile.user_id == current_user.id
        ).first()

        if institute_profile and institute_profile.logo_url:
            file_storage.delete_file(institute_profile.logo_url)
            # Also delete thumbnail
            thumbnail_path = institute_profile.logo_url.replace(
                "institute_logos/", "institute_logos/thumbnails/"
            )
            file_storage.delete_file(thumbnail_path)

        # Save the logo
        file_path, thumbnail_path = await file_storage.save_institute_logo(
            file, str(current_user.id)
        )

        # Update institute profile with new logo path
        if institute_profile:
            institute_profile.logo_url = file_path
        else:
            # Create profile if it doesn't exist
            institute_profile = InstituteProfile(
                user_id=current_user.id,
                institute_name="",
                logo_url=file_path,
                is_verified=False,
                verification_status="pending"
            )
            db.add(institute_profile)

        db.commit()

        # Return simple response with the full URL
        download_url = f"{settings.STATIC_FILES_URL}/{file_path}"
        thumbnail_url = f"{settings.STATIC_FILES_URL}/{thumbnail_path}"

        return {
            "message": "Institute logo uploaded successfully",
            "file_name": file.filename,
            "logo_url": download_url,
            "thumbnail_url": thumbnail_url
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail="Failed to upload institute logo")


@router.post("/institute-banner")
async def upload_institute_banner(
    file: UploadFile = File(...),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    current_user: User = Depends(require_type("institute"))
):
    """
    Upload a banner for institute (institute only).

    Args:
        file: The banner image file to upload
        db: Database session
        token: OAuth2 token
        current_user: Current authenticated user (must be institute)

    Returns:
        Simple success response with banner info
    """
    try:
        from Models.users import InstituteProfile

        # Delete old banner if exists
        institute_profile = db.query(InstituteProfile).filter(
            InstituteProfile.user_id == current_user.id
        ).first()

        if institute_profile and institute_profile.banner_url:
            file_storage.delete_file(institute_profile.banner_url)
            # Also delete thumbnail
            thumbnail_path = institute_profile.banner_url.replace(
                "institute_banners/", "institute_banners/thumbnails/"
            )
            file_storage.delete_file(thumbnail_path)

        # Save the banner
        file_path, thumbnail_path = await file_storage.save_institute_banner(
            file, str(current_user.id)
        )

        # Update institute profile with new banner path
        if institute_profile:
            institute_profile.banner_url = file_path
        else:
            # Create profile if it doesn't exist
            institute_profile = InstituteProfile(
                user_id=current_user.id,
                institute_name="",
                banner_url=file_path,
                is_verified=False,
                verification_status="pending"
            )
            db.add(institute_profile)

        db.commit()

        # Return simple response with the full URL
        download_url = f"{settings.STATIC_FILES_URL}/{file_path}"
        thumbnail_url = f"{settings.STATIC_FILES_URL}/{thumbnail_path}"

        return {
            "message": "Institute banner uploaded successfully",
            "file_name": file.filename,
            "banner_url": download_url,
            "thumbnail_url": thumbnail_url
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail="Failed to upload institute banner")
