"""
WebSocket Chat Schemas for EduFair Platform

This module contains Pydantic schemas for WebSocket-based real-time messaging.
"""

from pydantic import BaseModel, <PERSON>, validator
from uuid import UUID
from datetime import datetime
from typing import Optional, Dict, Any, Literal
from enum import Enum


# ==================== MESSAGE TYPE ENUMS ====================

class WebSocketMessageType(str, Enum):
    """Types of WebSocket messages"""
    # Connection management
    CONNECTION_ESTABLISHED = "connection_established"
    HEARTBEAT = "heartbeat"
    HEARTBEAT_RESPONSE = "heartbeat_response"
    
    # Messaging
    NEW_MESSAGE = "new_message"
    MESSAGE_SENT = "message_sent"
    MESSAGE_DELIVERED = "message_delivered"
    MESSAGE_READ = "message_read"
    MESSAGE_DELETED = "message_deleted"
    
    # Typing indicators
    TYPING_START = "typing_start"
    TYPING_STOP = "typing_stop"
    
    # User status
    USER_ONLINE = "user_online"
    USER_OFFLINE = "user_offline"
    
    # Errors
    ERROR = "error"
    AUTHENTICATION_ERROR = "authentication_error"


# ==================== BASE WEBSOCKET MESSAGE ====================

class WebSocketMessage(BaseModel):
    """Base WebSocket message schema"""
    type: WebSocketMessageType
    timestamp: datetime = Field(default_factory=lambda: datetime.now())
    data: Optional[Dict[str, Any]] = None
    
    class Config:
        use_enum_values = True


# ==================== INCOMING MESSAGE SCHEMAS ====================

class IncomingChatMessage(BaseModel):
    """Schema for incoming chat messages via WebSocket"""
    type: Literal["send_message"] = "send_message"
    receiver_id: UUID = Field(..., description="ID of message receiver")
    message: str = Field(..., min_length=1, max_length=2000, description="Message content")
    
    @validator('message')
    def validate_message(cls, v):
        if not v or not v.strip():
            raise ValueError('Message cannot be empty')
        return v.strip()


class IncomingTypingIndicator(BaseModel):
    """Schema for typing indicator messages"""
    type: Literal["typing_start", "typing_stop"]
    receiver_id: UUID = Field(..., description="ID of user to notify about typing")


class IncomingHeartbeat(BaseModel):
    """Schema for heartbeat messages"""
    type: Literal["heartbeat"] = "heartbeat"


class IncomingMessageRead(BaseModel):
    """Schema for message read notifications"""
    type: Literal["message_read"] = "message_read"
    message_id: UUID = Field(..., description="ID of message being marked as read")


# ==================== OUTGOING MESSAGE SCHEMAS ====================

class OutgoingChatMessage(BaseModel):
    """Schema for outgoing chat messages via WebSocket"""
    type: Literal["new_message"] = "new_message"
    message_id: UUID
    sender_id: UUID
    sender_username: str
    sender_profile_picture: Optional[str] = None
    message: str
    sent_at: datetime
    conversation_id: str  # Derived from sender/receiver pair
    
    class Config:
        from_attributes = True


class OutgoingMessageStatus(BaseModel):
    """Schema for message status updates"""
    type: Literal["message_sent", "message_delivered", "message_read"]
    message_id: UUID
    status: str
    timestamp: datetime
    
    class Config:
        from_attributes = True


class OutgoingTypingIndicator(BaseModel):
    """Schema for outgoing typing indicators"""
    type: Literal["typing_start", "typing_stop"]
    user_id: UUID
    username: str
    timestamp: datetime


class OutgoingUserStatus(BaseModel):
    """Schema for user online/offline status"""
    type: Literal["user_online", "user_offline"]
    user_id: UUID
    username: str
    timestamp: datetime


class OutgoingHeartbeatResponse(BaseModel):
    """Schema for heartbeat responses"""
    type: Literal["heartbeat_response"] = "heartbeat_response"
    timestamp: datetime
    server_time: datetime


class OutgoingConnectionEstablished(BaseModel):
    """Schema for connection establishment confirmation"""
    type: Literal["connection_established"] = "connection_established"
    message: str = "Connected to real-time messaging"
    timestamp: datetime
    connection_id: str
    user_id: UUID


class OutgoingError(BaseModel):
    """Schema for error messages"""
    type: Literal["error", "authentication_error"]
    error_code: str
    message: str
    timestamp: datetime
    details: Optional[Dict[str, Any]] = None


# ==================== WEBSOCKET AUTHENTICATION ====================

class WebSocketAuthMessage(BaseModel):
    """Schema for WebSocket authentication"""
    type: Literal["authenticate"] = "authenticate"
    token: str = Field(..., description="JWT authentication token")


# ==================== WEBSOCKET STATISTICS ====================

class WebSocketConnectionStats(BaseModel):
    """Schema for WebSocket connection statistics"""
    total_connections: int = 0
    online_users: int = 0
    active_conversations: int = 0
    messages_per_minute: float = 0.0
    average_response_time: float = 0.0


class UserConnectionInfo(BaseModel):
    """Schema for user connection information"""
    user_id: UUID
    username: str
    is_online: bool
    last_seen: Optional[datetime] = None
    connection_count: int = 0
    last_activity: Optional[datetime] = None


# ==================== CONVERSATION MANAGEMENT ====================

class ConversationParticipants(BaseModel):
    """Schema for conversation participants"""
    user1_id: UUID
    user2_id: UUID
    conversation_id: str
    
    @validator('conversation_id', pre=True, always=True)
    def generate_conversation_id(cls, v, values):
        if v:
            return v
        user1 = values.get('user1_id')
        user2 = values.get('user2_id')
        if user1 and user2:
            # Create consistent conversation ID
            sorted_ids = sorted([str(user1), str(user2)])
            return f"{sorted_ids[0]}_{sorted_ids[1]}"
        return v


# ==================== BULK OPERATIONS ====================

class BulkMessageDelivery(BaseModel):
    """Schema for bulk message delivery status"""
    message_ids: list[UUID]
    delivery_status: Dict[str, str]  # {message_id: status}
    timestamp: datetime


# ==================== WEBSOCKET EVENTS ====================

class WebSocketEvent(BaseModel):
    """Schema for WebSocket events"""
    event_type: str
    user_id: UUID
    data: Dict[str, Any]
    timestamp: datetime = Field(default_factory=lambda: datetime.now())
    
    class Config:
        use_enum_values = True
