import uuid
import secrets
import j<PERSON>
from typing import Dict, Any, Optional, List
from datetime import datetime, timezone, timedelta
from sqlalchemy.orm import Session
from fastapi import HTTPException
import hashlib

# Import Models
from Models.Events import Event, EventRegistration, RegistrationStatusEnum
from Models.Competitions import CompetitionSession
from Models.users import User


class CompetitionSecurityService:
    """Service for handling competition exam security"""
    
    def __init__(self):
        self.max_violations_before_flag = 5
        self.session_timeout_minutes = 30
        
    def create_secure_session(
        self,
        db: Session,
        competition_id: uuid.UUID,
        participant_id: uuid.UUID,
        ip_address: str,
        user_agent: str
    ) -> Dict[str, Any]:
        """Create a secure competition session"""
        
        # Verify competition exists and is active
        competition = db.query(Event).filter(
            Event.id == competition_id,
            Event.is_competition == True
        ).first()
        
        if not competition:
            raise HTTPException(status_code=404, detail="Competition not found")
        
        # Check if competition is currently active
        now = datetime.now(timezone.utc)
        if now < competition.start_datetime:
            raise HTTPException(status_code=400, detail="Competition has not started yet")
        
        if now > competition.end_datetime:
            raise HTTPException(status_code=400, detail="Competition has ended")
        
        # Verify participant is registered
        registration = db.query(EventRegistration).filter(
            EventRegistration.event_id == competition_id,
            EventRegistration.user_id == participant_id,
            EventRegistration.status.in_([RegistrationStatusEnum.CONFIRMED, RegistrationStatusEnum.ATTENDED])
        ).first()
        
        if not registration:
            raise HTTPException(status_code=403, detail="Participant not registered for this competition")
        
        # Check if participant already has an active session
        existing_session = db.query(CompetitionSession).filter(
            CompetitionSession.competition_id == competition_id,
            CompetitionSession.participant_id == participant_id,
            CompetitionSession.is_active == True
        ).first()
        
        if existing_session:
            # Update existing session
            existing_session.ip_address = ip_address
            existing_session.user_agent = user_agent
            db.commit()
            
            return {
                "session_token": existing_session.session_token,
                "session_id": str(existing_session.id),
                "started_at": existing_session.started_at,
                "remaining_time_seconds": existing_session.remaining_time_seconds,
                "security_settings": self._get_security_settings(competition)
            }
        
        # Create new session
        session_token = self._generate_session_token()
        browser_fingerprint = self._generate_browser_fingerprint(user_agent, ip_address)
        
        # Calculate remaining time
        competition_duration = competition.competition_duration_minutes or 120  # Default 2 hours
        remaining_time = competition_duration * 60  # Convert to seconds
        
        # Adjust for competition end time
        time_until_end = int((competition.end_datetime - now).total_seconds())
        remaining_time = min(remaining_time, time_until_end)
        
        session = CompetitionSession(
            competition_id=competition_id,
            participant_id=participant_id,
            session_token=session_token,
            started_at=now,
            ip_address=ip_address,
            user_agent=user_agent,
            browser_fingerprint=browser_fingerprint,
            is_active=True,
            remaining_time_seconds=remaining_time
        )
        
        db.add(session)
        db.commit()
        db.refresh(session)
        
        return {
            "session_token": session_token,
            "session_id": str(session.id),
            "started_at": session.started_at,
            "remaining_time_seconds": remaining_time,
            "security_settings": self._get_security_settings(competition)
        }
    
    def validate_session(
        self,
        db: Session,
        session_token: str,
        ip_address: str,
        user_agent: str
    ) -> Dict[str, Any]:
        """Validate and update competition session"""
        
        session = db.query(CompetitionSession).filter(
            CompetitionSession.session_token == session_token,
            CompetitionSession.is_active == True
        ).first()
        
        if not session:
            raise HTTPException(status_code=401, detail="Invalid or expired session")
        
        # Check if session has timed out
        now = datetime.now(timezone.utc)
        session_duration = int((now - session.started_at).total_seconds())
        
        if session_duration > session.remaining_time_seconds:
            session.is_active = False
            session.ended_at = now
            db.commit()
            raise HTTPException(status_code=401, detail="Session has expired")
        
        # Security checks
        violations = []
        
        # Check IP address consistency
        if session.ip_address != ip_address:
            violations.append({
                "type": "ip_change",
                "message": "IP address changed during session",
                "timestamp": now.isoformat(),
                "old_ip": session.ip_address,
                "new_ip": ip_address
            })
        
        # Check user agent consistency
        if session.user_agent != user_agent:
            violations.append({
                "type": "user_agent_change",
                "message": "User agent changed during session",
                "timestamp": now.isoformat()
            })
        
        # Update session activity
        session.total_time_seconds = session_duration
        session.remaining_time_seconds = max(0, session.remaining_time_seconds - session_duration)
        
        # Record violations if any
        if violations:
            existing_violations = session.violations or []
            existing_violations.extend(violations)
            session.violations = existing_violations
            session.violation_count = len(existing_violations)
            
            # Flag session if too many violations
            if session.violation_count >= self.max_violations_before_flag:
                session.is_flagged = True
        
        db.commit()
        
        return {
            "session_id": str(session.id),
            "participant_id": str(session.participant_id),
            "competition_id": str(session.competition_id),
            "remaining_time_seconds": session.remaining_time_seconds,
            "is_flagged": session.is_flagged,
            "violation_count": session.violation_count,
            "violations": violations
        }
    
    def record_security_event(
        self,
        db: Session,
        session_token: str,
        event_type: str,
        event_data: Dict[str, Any]
    ) -> bool:
        """Record a security event during the competition"""
        
        session = db.query(CompetitionSession).filter(
            CompetitionSession.session_token == session_token,
            CompetitionSession.is_active == True
        ).first()
        
        if not session:
            return False
        
        # Update specific event counters
        if event_type == "tab_switch":
            session.tab_switches += 1
        elif event_type == "window_blur":
            session.window_blur_events += 1
        elif event_type == "copy_paste":
            session.copy_paste_events += 1
        elif event_type == "right_click":
            session.right_click_events += 1
        
        # Record violation
        violation = {
            "type": event_type,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "data": event_data
        }
        
        existing_violations = session.violations or []
        existing_violations.append(violation)
        session.violations = existing_violations
        session.violation_count = len(existing_violations)
        
        # Flag session if too many violations
        if session.violation_count >= self.max_violations_before_flag:
            session.is_flagged = True
        
        db.commit()
        return True
    
    def end_session(
        self,
        db: Session,
        session_token: str,
        submission_ip: str
    ) -> Dict[str, Any]:
        """End competition session"""
        
        session = db.query(CompetitionSession).filter(
            CompetitionSession.session_token == session_token
        ).first()
        
        if not session:
            raise HTTPException(status_code=404, detail="Session not found")
        
        now = datetime.now(timezone.utc)
        session.is_active = False
        session.is_submitted = True
        session.ended_at = now
        session.submitted_at = now
        session.submission_ip = submission_ip
        session.total_time_seconds = int((now - session.started_at).total_seconds())
        
        db.commit()
        
        return {
            "session_id": str(session.id),
            "total_time_seconds": session.total_time_seconds,
            "violation_count": session.violation_count,
            "is_flagged": session.is_flagged,
            "submitted_at": session.submitted_at
        }
    
    def get_session_statistics(
        self,
        db: Session,
        competition_id: uuid.UUID,
        organizer_id: uuid.UUID
    ) -> Dict[str, Any]:
        """Get security statistics for a competition"""
        
        # Verify organizer has access
        competition = db.query(Event).filter(
            Event.id == competition_id,
            Event.is_competition == True
        ).first()
        
        if not competition:
            raise HTTPException(status_code=404, detail="Competition not found")
        
        if competition.organizer_id != organizer_id and competition.institute_id != organizer_id:
            raise HTTPException(status_code=403, detail="Access denied")
        
        # Get session statistics
        from sqlalchemy import func
        
        total_sessions = db.query(CompetitionSession).filter(
            CompetitionSession.competition_id == competition_id
        ).count()
        
        active_sessions = db.query(CompetitionSession).filter(
            CompetitionSession.competition_id == competition_id,
            CompetitionSession.is_active == True
        ).count()
        
        flagged_sessions = db.query(CompetitionSession).filter(
            CompetitionSession.competition_id == competition_id,
            CompetitionSession.is_flagged == True
        ).count()
        
        submitted_sessions = db.query(CompetitionSession).filter(
            CompetitionSession.competition_id == competition_id,
            CompetitionSession.is_submitted == True
        ).count()
        
        # Violation statistics
        avg_violations = db.query(func.avg(CompetitionSession.violation_count)).filter(
            CompetitionSession.competition_id == competition_id
        ).scalar() or 0
        
        total_tab_switches = db.query(func.sum(CompetitionSession.tab_switches)).filter(
            CompetitionSession.competition_id == competition_id
        ).scalar() or 0
        
        total_window_blurs = db.query(func.sum(CompetitionSession.window_blur_events)).filter(
            CompetitionSession.competition_id == competition_id
        ).scalar() or 0
        
        return {
            "total_sessions": total_sessions,
            "active_sessions": active_sessions,
            "flagged_sessions": flagged_sessions,
            "submitted_sessions": submitted_sessions,
            "flagged_percentage": (flagged_sessions / total_sessions * 100) if total_sessions > 0 else 0,
            "average_violations_per_session": float(avg_violations),
            "total_tab_switches": total_tab_switches,
            "total_window_blur_events": total_window_blurs,
            "security_level": "high" if flagged_sessions / total_sessions > 0.1 else "normal" if total_sessions > 0 else "unknown"
        }
    
    def _generate_session_token(self) -> str:
        """Generate a secure session token"""
        return secrets.token_urlsafe(32)
    
    def _generate_browser_fingerprint(self, user_agent: str, ip_address: str) -> str:
        """Generate browser fingerprint for additional security"""
        fingerprint_data = f"{user_agent}:{ip_address}:{datetime.now().strftime('%Y%m%d')}"
        return hashlib.sha256(fingerprint_data.encode()).hexdigest()
    
    def _get_security_settings(self, competition: Event) -> Dict[str, Any]:
        """Get security settings for the competition"""
        return {
            "enable_proctoring": competition.enable_proctoring,
            "enable_screen_recording": competition.enable_screen_recording,
            "enable_webcam": competition.enable_webcam,
            "disable_copy_paste": competition.disable_copy_paste,
            "randomize_questions": competition.randomize_questions,
            "max_violations_before_flag": self.max_violations_before_flag
        }


# Global security service instance
competition_security_service = CompetitionSecurityService()
