"""
Admin Revenue Dashboard Routes for EduFair Platform

This module provides comprehensive APIs for admin revenue management including:
- Platform revenue overview and analytics
- Event revenue breakdown by ticket types
- Commission tracking and calculations
- Institute performance metrics
- Financial reporting and trends
"""

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Any
from uuid import UUID
from decimal import Decimal
from datetime import datetime, timezone, timedelta

# Import dependencies
from config.session import get_db
from config.deps import oauth2_scheme, get_current_user
from config.permission import require_type

# Import Models
from Models.Events import (
    Event, EventRegistration, EventTicket, EventPayment, Payout,
    RegistrationStatusEnum, PaymentStatusEnum, PayoutStatusEnum
)
from Models.users import User, InstituteProfile

# Import CRUD operations
from Cruds.Admin.Payouts import get_admin_dashboard

router = APIRouter()


# ==================== REVENUE OVERVIEW ====================

@router.get("/overview")
def get_revenue_overview(
    days: int = Query(30, ge=1, le=365, description="Number of days for trends"),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("admin"))
):
    """
    Get comprehensive revenue overview for the platform.
    """
    current_user = get_current_user(token, db)
    
    try:
        # Calculate date range
        end_date = datetime.now(timezone.utc)
        start_date = end_date - timedelta(days=days)
        
        # Total platform revenue (all completed payments)
        total_revenue = db.query(db.func.sum(EventPayment.amount)).filter(
            EventPayment.status == PaymentStatusEnum.COMPLETED
        ).scalar() or Decimal('0.00')
        
        # Revenue in the specified period
        period_revenue = db.query(db.func.sum(EventPayment.amount)).filter(
            EventPayment.status == PaymentStatusEnum.COMPLETED,
            EventPayment.processed_at >= start_date,
            EventPayment.processed_at <= end_date
        ).scalar() or Decimal('0.00')
        
        # Total commission earned
        total_commission = db.query(db.func.sum(Payout.commission_amount)).filter(
            Payout.status == PayoutStatusEnum.COMPLETED
        ).scalar() or Decimal('0.00')
        
        # Commission in the specified period
        period_commission = db.query(db.func.sum(Payout.commission_amount)).filter(
            Payout.status == PayoutStatusEnum.COMPLETED,
            Payout.processed_at >= start_date,
            Payout.processed_at <= end_date
        ).scalar() or Decimal('0.00')
        
        # Total payouts made
        total_payouts = db.query(db.func.sum(Payout.amount)).filter(
            Payout.status == PayoutStatusEnum.COMPLETED
        ).scalar() or Decimal('0.00')
        
        # Pending payouts
        pending_payouts = db.query(db.func.sum(Payout.amount)).filter(
            Payout.status == PayoutStatusEnum.PENDING
        ).scalar() or Decimal('0.00')
        
        # Event statistics
        total_events = db.query(Event).count()
        active_events = db.query(Event).filter(
            Event.status == "PUBLISHED",
            Event.start_datetime > datetime.now(timezone.utc)
        ).count()
        
        # Registration statistics
        total_registrations = db.query(EventRegistration).count()
        confirmed_registrations = db.query(EventRegistration).filter(
            EventRegistration.status == RegistrationStatusEnum.CONFIRMED
        ).count()
        
        # Institute statistics
        total_institutes = db.query(User).join(InstituteProfile).count()
        institutes_with_revenue = db.query(Event.organizer_id).join(EventPayment).filter(
            EventPayment.status == PaymentStatusEnum.COMPLETED
        ).distinct().count()
        
        return {
            "period_days": days,
            "start_date": start_date.isoformat(),
            "end_date": end_date.isoformat(),
            "revenue": {
                "total_platform_revenue": float(total_revenue),
                "period_revenue": float(period_revenue),
                "total_commission_earned": float(total_commission),
                "period_commission": float(period_commission),
                "total_payouts_made": float(total_payouts),
                "pending_payouts": float(pending_payouts),
                "platform_profit": float(total_commission)
            },
            "events": {
                "total_events": total_events,
                "active_events": active_events,
                "total_registrations": total_registrations,
                "confirmed_registrations": confirmed_registrations
            },
            "institutes": {
                "total_institutes": total_institutes,
                "institutes_with_revenue": institutes_with_revenue,
                "revenue_generating_rate": round((institutes_with_revenue / total_institutes * 100), 2) if total_institutes > 0 else 0
            }
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get revenue overview: {str(e)}"
        )


# ==================== EVENT REVENUE BREAKDOWN ====================

@router.get("/events/top-revenue")
def get_top_revenue_events(
    limit: int = Query(20, ge=1, le=100, description="Number of events to return"),
    days: Optional[int] = Query(None, ge=1, le=365, description="Filter by days (optional)"),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("admin"))
):
    """
    Get top revenue-generating events.
    """
    current_user = get_current_user(token, db)
    
    try:
        # Build query for event revenue
        query = db.query(
            Event.id,
            Event.title,
            Event.start_datetime,
            Event.organizer_id,
            db.func.sum(EventPayment.amount).label('total_revenue'),
            db.func.count(EventRegistration.id).label('total_registrations')
        ).join(EventPayment).join(EventRegistration).filter(
            EventPayment.status == PaymentStatusEnum.COMPLETED
        )
        
        # Apply date filter if specified
        if days:
            start_date = datetime.now(timezone.utc) - timedelta(days=days)
            query = query.filter(EventPayment.processed_at >= start_date)
        
        # Group and order by revenue
        events = query.group_by(
            Event.id, Event.title, Event.start_datetime, Event.organizer_id
        ).order_by(db.desc('total_revenue')).limit(limit).all()
        
        # Get institute names
        result = []
        for event in events:
            institute = db.query(User).join(InstituteProfile).filter(User.id == event.organizer_id).first()
            institute_name = institute.institute_profile.institute_name if institute and institute.institute_profile else "Unknown"
            
            # Get payout info
            payout = db.query(Payout).filter(Payout.event_id == event.id).first()
            
            result.append({
                "event_id": str(event.id),
                "event_title": event.title,
                "event_date": event.start_datetime.isoformat() if event.start_datetime else None,
                "institute_name": institute_name,
                "total_revenue": float(event.total_revenue),
                "total_registrations": event.total_registrations,
                "payout_status": payout.status.value if payout else "No payout",
                "payout_amount": float(payout.amount) if payout else 0.0,
                "commission_earned": float(payout.commission_amount) if payout else 0.0
            })
        
        return {
            "events": result,
            "period_days": days,
            "total_events": len(result)
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get top revenue events: {str(e)}"
        )


# ==================== TICKET TYPE ANALYTICS ====================

@router.get("/tickets/breakdown")
def get_ticket_type_breakdown(
    days: Optional[int] = Query(None, ge=1, le=365, description="Filter by days (optional)"),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("admin"))
):
    """
    Get breakdown of revenue by ticket types across all events.
    """
    current_user = get_current_user(token, db)
    
    try:
        # Build query for ticket sales
        query = db.query(
            EventTicket.name,
            db.func.count(EventRegistration.id).label('tickets_sold'),
            db.func.sum(EventPayment.amount).label('revenue'),
            db.func.avg(EventTicket.price).label('avg_price')
        ).join(EventRegistration).join(EventPayment).filter(
            EventPayment.status == PaymentStatusEnum.COMPLETED
        )
        
        # Apply date filter if specified
        if days:
            start_date = datetime.now(timezone.utc) - timedelta(days=days)
            query = query.filter(EventPayment.processed_at >= start_date)
        
        # Group by ticket type
        ticket_breakdown = query.group_by(EventTicket.name).order_by(db.desc('revenue')).all()
        
        total_revenue = sum(float(ticket.revenue) for ticket in ticket_breakdown)
        total_tickets = sum(ticket.tickets_sold for ticket in ticket_breakdown)
        
        result = []
        for ticket in ticket_breakdown:
            revenue_percentage = (float(ticket.revenue) / total_revenue * 100) if total_revenue > 0 else 0
            
            result.append({
                "ticket_type": ticket.name,
                "tickets_sold": ticket.tickets_sold,
                "revenue": float(ticket.revenue),
                "avg_price": float(ticket.avg_price),
                "revenue_percentage": round(revenue_percentage, 2)
            })
        
        return {
            "ticket_breakdown": result,
            "summary": {
                "total_revenue": total_revenue,
                "total_tickets_sold": total_tickets,
                "avg_ticket_price": round(total_revenue / total_tickets, 2) if total_tickets > 0 else 0
            },
            "period_days": days
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get ticket breakdown: {str(e)}"
        )


# ==================== INSTITUTE PERFORMANCE ====================

@router.get("/institutes/performance")
def get_institute_performance(
    limit: int = Query(20, ge=1, le=100, description="Number of institutes to return"),
    days: Optional[int] = Query(None, ge=1, le=365, description="Filter by days (optional)"),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("admin"))
):
    """
    Get institute performance metrics ranked by revenue.
    """
    current_user = get_current_user(token, db)
    
    try:
        # Build query for institute performance
        query = db.query(
            Event.organizer_id,
            db.func.count(Event.id.distinct()).label('total_events'),
            db.func.sum(EventPayment.amount).label('total_revenue'),
            db.func.count(EventRegistration.id).label('total_registrations'),
            db.func.sum(Payout.commission_amount).label('commission_generated')
        ).join(EventPayment).join(EventRegistration).outerjoin(Payout).filter(
            EventPayment.status == PaymentStatusEnum.COMPLETED
        )
        
        # Apply date filter if specified
        if days:
            start_date = datetime.now(timezone.utc) - timedelta(days=days)
            query = query.filter(EventPayment.processed_at >= start_date)
        
        # Group and order by revenue
        institutes = query.group_by(Event.organizer_id).order_by(db.desc('total_revenue')).limit(limit).all()
        
        result = []
        for institute_data in institutes:
            institute = db.query(User).join(InstituteProfile).filter(User.id == institute_data.organizer_id).first()
            institute_name = institute.institute_profile.institute_name if institute and institute.institute_profile else "Unknown"
            
            # Get payout statistics
            total_payouts = db.query(db.func.count(Payout.id)).filter(
                Payout.institute_id == institute_data.organizer_id,
                Payout.status == PayoutStatusEnum.COMPLETED
            ).scalar() or 0
            
            pending_payouts = db.query(db.func.count(Payout.id)).filter(
                Payout.institute_id == institute_data.organizer_id,
                Payout.status == PayoutStatusEnum.PENDING
            ).scalar() or 0
            
            result.append({
                "institute_id": str(institute_data.organizer_id),
                "institute_name": institute_name,
                "total_events": institute_data.total_events,
                "total_revenue": float(institute_data.total_revenue or 0),
                "total_registrations": institute_data.total_registrations,
                "commission_generated": float(institute_data.commission_generated or 0),
                "avg_revenue_per_event": round(float(institute_data.total_revenue or 0) / institute_data.total_events, 2),
                "total_payouts_completed": total_payouts,
                "pending_payouts": pending_payouts
            })
        
        return {
            "institutes": result,
            "period_days": days,
            "total_institutes": len(result)
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get institute performance: {str(e)}"
        )


# ==================== FINANCIAL TRENDS ====================

@router.get("/trends/monthly")
def get_monthly_trends(
    months: int = Query(12, ge=1, le=24, description="Number of months to include"),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("admin"))
):
    """
    Get monthly revenue and commission trends.
    """
    current_user = get_current_user(token, db)
    
    try:
        trends = {}
        
        for i in range(months):
            # Calculate month boundaries
            current_date = datetime.now(timezone.utc)
            month_start = current_date.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            month_start = month_start - timedelta(days=30 * i)
            
            if month_start.month == 12:
                month_end = month_start.replace(year=month_start.year + 1, month=1)
            else:
                month_end = month_start.replace(month=month_start.month + 1)
            
            month_key = month_start.strftime('%Y-%m')
            
            # Revenue for this month
            month_revenue = db.query(db.func.sum(EventPayment.amount)).filter(
                EventPayment.status == PaymentStatusEnum.COMPLETED,
                EventPayment.processed_at >= month_start,
                EventPayment.processed_at < month_end
            ).scalar() or Decimal('0.00')
            
            # Commission for this month
            month_commission = db.query(db.func.sum(Payout.commission_amount)).filter(
                Payout.status == PayoutStatusEnum.COMPLETED,
                Payout.processed_at >= month_start,
                Payout.processed_at < month_end
            ).scalar() or Decimal('0.00')
            
            # Events and registrations for this month
            month_events = db.query(Event).filter(
                Event.created_at >= month_start,
                Event.created_at < month_end
            ).count()
            
            month_registrations = db.query(EventRegistration).filter(
                EventRegistration.registered_at >= month_start,
                EventRegistration.registered_at < month_end
            ).count()
            
            trends[month_key] = {
                "revenue": float(month_revenue),
                "commission": float(month_commission),
                "events": month_events,
                "registrations": month_registrations,
                "avg_revenue_per_event": round(float(month_revenue) / month_events, 2) if month_events > 0 else 0
            }
        
        return {
            "trends": trends,
            "months_included": months,
            "summary": {
                "total_revenue": sum(month["revenue"] for month in trends.values()),
                "total_commission": sum(month["commission"] for month in trends.values()),
                "total_events": sum(month["events"] for month in trends.values()),
                "total_registrations": sum(month["registrations"] for month in trends.values())
            }
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get monthly trends: {str(e)}"
        )
