"""
Admin Payout CRUD Operations for EduFair Platform

This module contains CRUD operations for admin payout management including:
- Payout creation and calculation
- Revenue distribution tracking
- Payout status management
- Financial reporting
"""

from datetime import datetime, timezone, timedelta
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, or_, func, desc, asc
from fastapi import HTTPException, status
from typing import List, Optional, Dict, Any
from uuid import UUID
from decimal import Decimal

# Import Models
from Models.Events import (
    Event, EventRegistration, EventTicket, EventPayment, Payout,
    RegistrationStatusEnum, PaymentStatusEnum, PayoutStatusEnum, PayoutMethodEnum
)
from Models.users import User, InstituteProfile

# Import Schemas
from Schemas.Payouts import (
    PayoutCreate, PayoutUpdate, PayoutOut, PayoutListFilter, PayoutListResponse,
    EventRevenueBreakdown, RevenueCalculationRequest, AdminPayoutDashboard,
    BulkPayoutCreate, BulkPayoutResponse
)


def calculate_event_revenue(db: Session, event_id: UUID, commission_rate: Decimal = Decimal('10.0')) -> EventRevenueBreakdown:
    """Calculate revenue breakdown for an event"""
    
    # Get event details
    event = db.query(Event).filter(Event.id == event_id).first()
    if not event:
        raise HTTPException(status_code=404, detail="Event not found")
    
    # Get institute details
    institute = db.query(User).join(InstituteProfile).filter(User.id == event.organizer_id).first()
    if not institute:
        raise HTTPException(status_code=404, detail="Event organizer not found")
    
    # Get all registrations for this event
    registrations = db.query(EventRegistration).filter(
        EventRegistration.event_id == event_id
    ).all()
    
    # Calculate registration statistics
    total_registrations = len(registrations)
    confirmed_registrations = len([r for r in registrations if r.status == RegistrationStatusEnum.CONFIRMED])
    
    # Calculate revenue from completed payments
    completed_payments = db.query(EventPayment).filter(
        EventPayment.event_id == event_id,
        EventPayment.status == PaymentStatusEnum.COMPLETED
    ).all()
    
    pending_payments = db.query(EventPayment).filter(
        EventPayment.event_id == event_id,
        EventPayment.status == PaymentStatusEnum.PENDING
    ).all()
    
    refunded_payments = db.query(EventPayment).filter(
        EventPayment.event_id == event_id,
        EventPayment.status == PaymentStatusEnum.REFUNDED
    ).all()
    
    total_revenue = sum(p.amount for p in completed_payments)
    pending_revenue = sum(p.amount for p in pending_payments)
    refunded_revenue = sum(p.amount for p in refunded_payments)
    
    # Calculate commission and net payout
    commission_amount = total_revenue * (commission_rate / 100)
    net_payout_amount = total_revenue - commission_amount
    
    # Get ticket breakdown
    tickets = db.query(EventTicket).filter(EventTicket.event_id == event_id).all()
    ticket_breakdown = {}
    
    for ticket in tickets:
        ticket_registrations = [r for r in registrations if r.ticket_id == ticket.id]
        ticket_revenue = sum(p.amount for p in completed_payments if any(r.id == p.registration_id for r in ticket_registrations))
        
        ticket_breakdown[ticket.name] = {
            "count": len(ticket_registrations),
            "revenue": float(ticket_revenue),
            "price": float(ticket.price)
        }
    
    # Check for existing payout
    existing_payout = db.query(Payout).filter(Payout.event_id == event_id).first()
    
    return EventRevenueBreakdown(
        event_id=event_id,
        event_title=event.title,
        institute_id=event.organizer_id,
        institute_name=institute.institute_profile.institute_name if institute.institute_profile else institute.username,
        total_registrations=total_registrations,
        confirmed_registrations=confirmed_registrations,
        total_revenue=total_revenue,
        commission_rate=commission_rate,
        commission_amount=commission_amount,
        net_payout_amount=net_payout_amount,
        ticket_breakdown=ticket_breakdown,
        pending_payments=pending_revenue,
        completed_payments=total_revenue,
        refunded_payments=refunded_revenue,
        has_existing_payout=existing_payout is not None,
        existing_payout_id=existing_payout.id if existing_payout else None,
        existing_payout_status=existing_payout.status if existing_payout else None
    )


def create_payout(db: Session, payout_data: PayoutCreate, created_by: UUID) -> PayoutOut:
    """Create a new payout"""
    
    # Validate event exists and has revenue
    revenue_breakdown = calculate_event_revenue(db, payout_data.event_id)
    
    if revenue_breakdown.total_revenue <= 0:
        raise HTTPException(
            status_code=400, 
            detail="Cannot create payout for event with no revenue"
        )
    
    # Check if payout already exists for this event
    if revenue_breakdown.has_existing_payout:
        raise HTTPException(
            status_code=400,
            detail="Payout already exists for this event"
        )
    
    # Validate institute exists
    institute = db.query(User).filter(User.id == payout_data.institute_id).first()
    if not institute:
        raise HTTPException(status_code=404, detail="Institute not found")
    
    # Calculate commission if not provided
    commission_rate = payout_data.commission_rate or Decimal('10.0')
    gross_revenue = payout_data.gross_revenue or revenue_breakdown.total_revenue
    commission_amount = payout_data.commission_amount or (gross_revenue * commission_rate / 100)
    
    # Create payout record
    payout = Payout(
        institute_id=payout_data.institute_id,
        event_id=payout_data.event_id,
        amount=payout_data.amount,
        currency=payout_data.currency,
        commission_rate=commission_rate,
        commission_amount=commission_amount,
        gross_revenue=gross_revenue,
        method=payout_data.method,
        bank_account_number=payout_data.bank_account_number,
        bank_name=payout_data.bank_name,
        account_holder_name=payout_data.account_holder_name,
        mobile_wallet_number=payout_data.mobile_wallet_number,
        created_by=created_by,
        notes=payout_data.notes,
        status=PayoutStatusEnum.PENDING
    )
    
    db.add(payout)
    db.commit()
    db.refresh(payout)
    
    return get_payout_by_id(db, payout.id)


def get_payout_by_id(db: Session, payout_id: UUID) -> PayoutOut:
    """Get payout by ID with related data"""
    
    payout = db.query(Payout).options(
        joinedload(Payout.institute).joinedload(User.institute_profile),
        joinedload(Payout.event),
        joinedload(Payout.creator),
        joinedload(Payout.processor)
    ).filter(Payout.id == payout_id).first()
    
    if not payout:
        raise HTTPException(status_code=404, detail="Payout not found")
    
    # Build response with related data
    payout_dict = payout.__dict__.copy()
    payout_dict['institute_name'] = payout.institute.institute_profile.institute_name if payout.institute.institute_profile else payout.institute.username
    payout_dict['event_title'] = payout.event.title
    payout_dict['creator_name'] = payout.creator.username
    payout_dict['processor_name'] = payout.processor.username if payout.processor else None
    
    return PayoutOut(**payout_dict)


def get_payouts(
    db: Session, 
    filters: PayoutListFilter, 
    skip: int = 0, 
    limit: int = 20
) -> PayoutListResponse:
    """Get paginated list of payouts with filters"""
    
    query = db.query(Payout).options(
        joinedload(Payout.institute).joinedload(User.institute_profile),
        joinedload(Payout.event),
        joinedload(Payout.creator),
        joinedload(Payout.processor)
    )
    
    # Apply filters
    if filters.institute_id:
        query = query.filter(Payout.institute_id == filters.institute_id)
    
    if filters.event_id:
        query = query.filter(Payout.event_id == filters.event_id)
    
    if filters.status:
        query = query.filter(Payout.status.in_(filters.status))
    
    if filters.method:
        query = query.filter(Payout.method.in_(filters.method))
    
    if filters.date_from:
        query = query.filter(Payout.created_at >= filters.date_from)
    
    if filters.date_to:
        query = query.filter(Payout.created_at <= filters.date_to)
    
    if filters.min_amount:
        query = query.filter(Payout.amount >= filters.min_amount)
    
    if filters.max_amount:
        query = query.filter(Payout.amount <= filters.max_amount)
    
    # Get total count
    total_count = query.count()
    
    # Get paginated results
    payouts = query.order_by(desc(Payout.created_at)).offset(skip).limit(limit).all()
    
    # Convert to response format
    payout_list = []
    for payout in payouts:
        payout_dict = payout.__dict__.copy()
        payout_dict['institute_name'] = payout.institute.institute_profile.institute_name if payout.institute.institute_profile else payout.institute.username
        payout_dict['event_title'] = payout.event.title
        payout_dict['creator_name'] = payout.creator.username
        payout_dict['processor_name'] = payout.processor.username if payout.processor else None
        payout_list.append(PayoutOut(**payout_dict))
    
    return PayoutListResponse(
        payouts=payout_list,
        total_count=total_count,
        page=skip // limit + 1,
        per_page=limit,
        has_next=skip + limit < total_count,
        has_prev=skip > 0,
        filters_applied=filters
    )


def update_payout(db: Session, payout_id: UUID, payout_update: PayoutUpdate, updated_by: UUID) -> PayoutOut:
    """Update payout status and details"""

    payout = db.query(Payout).filter(Payout.id == payout_id).first()
    if not payout:
        raise HTTPException(status_code=404, detail="Payout not found")

    # Update fields
    if payout_update.status is not None:
        old_status = payout.status
        payout.status = payout_update.status

        # Set timestamps based on status change
        if payout_update.status == PayoutStatusEnum.COMPLETED and old_status != PayoutStatusEnum.COMPLETED:
            payout.processed_at = datetime.now(timezone.utc)
            payout.processed_by = updated_by
            payout.payout_date = datetime.now(timezone.utc)
        elif payout_update.status == PayoutStatusEnum.FAILED and old_status != PayoutStatusEnum.FAILED:
            payout.failed_at = datetime.now(timezone.utc)
        elif payout_update.status == PayoutStatusEnum.CANCELLED and old_status != PayoutStatusEnum.CANCELLED:
            payout.cancelled_at = datetime.now(timezone.utc)

    if payout_update.transaction_reference is not None:
        payout.transaction_reference = payout_update.transaction_reference

    if payout_update.payment_proof_url is not None:
        payout.payment_proof_url = payout_update.payment_proof_url

    if payout_update.notes is not None:
        payout.notes = payout_update.notes

    if payout_update.failure_reason is not None:
        payout.failure_reason = payout_update.failure_reason

    db.commit()
    db.refresh(payout)

    return get_payout_by_id(db, payout_id)


def get_admin_dashboard(db: Session) -> AdminPayoutDashboard:
    """Get admin payout dashboard data"""

    # Get payout statistics
    total_payouts = db.query(Payout).count()
    pending_payouts = db.query(Payout).filter(Payout.status == PayoutStatusEnum.PENDING).count()
    completed_payouts = db.query(Payout).filter(Payout.status == PayoutStatusEnum.COMPLETED).count()
    failed_payouts = db.query(Payout).filter(Payout.status == PayoutStatusEnum.FAILED).count()

    # Get financial summary
    total_payout_amount = db.query(func.sum(Payout.amount)).filter(
        Payout.status == PayoutStatusEnum.COMPLETED
    ).scalar() or Decimal('0.00')

    total_commission_earned = db.query(func.sum(Payout.commission_amount)).filter(
        Payout.status == PayoutStatusEnum.COMPLETED
    ).scalar() or Decimal('0.00')

    pending_payout_amount = db.query(func.sum(Payout.amount)).filter(
        Payout.status == PayoutStatusEnum.PENDING
    ).scalar() or Decimal('0.00')

    # Get recent payouts
    recent_payouts_query = db.query(Payout).options(
        joinedload(Payout.institute).joinedload(User.institute_profile),
        joinedload(Payout.event),
        joinedload(Payout.creator)
    ).order_by(desc(Payout.created_at)).limit(10)

    recent_payouts = []
    for payout in recent_payouts_query.all():
        payout_dict = payout.__dict__.copy()
        payout_dict['institute_name'] = payout.institute.institute_profile.institute_name if payout.institute.institute_profile else payout.institute.username
        payout_dict['event_title'] = payout.event.title
        payout_dict['creator_name'] = payout.creator.username
        payout_dict['processor_name'] = payout.processor.username if payout.processor else None
        recent_payouts.append(PayoutOut(**payout_dict))

    # Get events requiring payout (events with revenue but no payout)
    events_with_revenue = db.query(Event).join(EventPayment).filter(
        EventPayment.status == PaymentStatusEnum.COMPLETED
    ).distinct().all()

    events_requiring_payout = []
    for event in events_with_revenue:
        existing_payout = db.query(Payout).filter(Payout.event_id == event.id).first()
        if not existing_payout:
            try:
                revenue_breakdown = calculate_event_revenue(db, event.id)
                if revenue_breakdown.total_revenue > 0:
                    events_requiring_payout.append(revenue_breakdown)
            except:
                continue  # Skip events with calculation errors

    # Get monthly trends (last 12 months)
    monthly_payout_trends = {}
    monthly_commission_trends = {}

    for i in range(12):
        month_start = datetime.now(timezone.utc).replace(day=1, hour=0, minute=0, second=0, microsecond=0) - timedelta(days=30*i)
        month_end = month_start.replace(month=month_start.month+1) if month_start.month < 12 else month_start.replace(year=month_start.year+1, month=1)
        month_key = month_start.strftime('%Y-%m')

        month_payouts = db.query(func.sum(Payout.amount)).filter(
            Payout.status == PayoutStatusEnum.COMPLETED,
            Payout.processed_at >= month_start,
            Payout.processed_at < month_end
        ).scalar() or Decimal('0.00')

        month_commission = db.query(func.sum(Payout.commission_amount)).filter(
            Payout.status == PayoutStatusEnum.COMPLETED,
            Payout.processed_at >= month_start,
            Payout.processed_at < month_end
        ).scalar() or Decimal('0.00')

        monthly_payout_trends[month_key] = month_payouts
        monthly_commission_trends[month_key] = month_commission

    return AdminPayoutDashboard(
        total_payouts=total_payouts,
        pending_payouts=pending_payouts,
        completed_payouts=completed_payouts,
        failed_payouts=failed_payouts,
        total_payout_amount=total_payout_amount,
        total_commission_earned=total_commission_earned,
        pending_payout_amount=pending_payout_amount,
        recent_payouts=recent_payouts,
        events_requiring_payout=events_requiring_payout,
        monthly_payout_trends=monthly_payout_trends,
        monthly_commission_trends=monthly_commission_trends
    )


def create_bulk_payouts(db: Session, bulk_request: BulkPayoutCreate, created_by: UUID) -> BulkPayoutResponse:
    """Create payouts for multiple events"""

    created_payouts = []
    failed_events = []

    for event_id in bulk_request.event_ids:
        try:
            # Calculate revenue for this event
            revenue_breakdown = calculate_event_revenue(db, event_id, bulk_request.commission_rate)

            if revenue_breakdown.total_revenue <= 0:
                failed_events.append({
                    "event_id": str(event_id),
                    "reason": "No revenue to payout"
                })
                continue

            if revenue_breakdown.has_existing_payout:
                failed_events.append({
                    "event_id": str(event_id),
                    "reason": "Payout already exists"
                })
                continue

            # Get institute bank details
            institute = db.query(User).join(InstituteProfile).filter(User.id == revenue_breakdown.institute_id).first()
            if not institute or not institute.institute_profile:
                failed_events.append({
                    "event_id": str(event_id),
                    "reason": "Institute profile not found"
                })
                continue

            # Determine bank details based on payout method
            bank_account_number = None
            bank_name = None
            account_holder_name = None
            mobile_wallet_number = None

            if bulk_request.payout_method == PayoutMethodEnum.BANK_TRANSFER:
                bank_account_number = institute.institute_profile.bank_account_number
                bank_name = institute.institute_profile.bank_name
                account_holder_name = institute.institute_profile.account_holder_name

                if not bank_account_number:
                    failed_events.append({
                        "event_id": str(event_id),
                        "reason": "Bank account details not available"
                    })
                    continue

            elif bulk_request.payout_method == PayoutMethodEnum.JAZZCASH:
                mobile_wallet_number = institute.institute_profile.jazzcash_number
                if not mobile_wallet_number:
                    failed_events.append({
                        "event_id": str(event_id),
                        "reason": "JazzCash number not available"
                    })
                    continue

            elif bulk_request.payout_method == PayoutMethodEnum.EASYPAISA:
                mobile_wallet_number = institute.institute_profile.easypaisa_number
                if not mobile_wallet_number:
                    failed_events.append({
                        "event_id": str(event_id),
                        "reason": "Easypaisa number not available"
                    })
                    continue

            # Create payout
            payout_data = PayoutCreate(
                institute_id=revenue_breakdown.institute_id,
                event_id=event_id,
                amount=revenue_breakdown.net_payout_amount,
                currency="PKR",
                commission_rate=bulk_request.commission_rate,
                commission_amount=revenue_breakdown.commission_amount,
                gross_revenue=revenue_breakdown.total_revenue,
                method=bulk_request.payout_method,
                bank_account_number=bank_account_number,
                bank_name=bank_name,
                account_holder_name=account_holder_name,
                mobile_wallet_number=mobile_wallet_number,
                notes=bulk_request.notes
            )

            payout = create_payout(db, payout_data, created_by)
            created_payouts.append(payout)

        except Exception as e:
            failed_events.append({
                "event_id": str(event_id),
                "reason": str(e)
            })

    return BulkPayoutResponse(
        created_payouts=created_payouts,
        failed_events=failed_events,
        total_created=len(created_payouts),
        total_failed=len(failed_events)
    )
