"""
Admin Payout Management Routes for EduFair Platform

This module provides comprehensive APIs for admin payout management including:
- Revenue calculation and payout creation
- Payout status tracking and updates
- Financial reporting and analytics
- Bulk payout operations
"""

from fastapi import APIRouter, Depends, HTTPException, Query, status, BackgroundTasks
from sqlalchemy.orm import Session
from typing import List, Optional
from uuid import UUID
from decimal import Decimal

# Import dependencies
from config.session import get_db
from config.deps import oauth2_scheme, get_current_user
from config.permission import require_type

# Import CRUD operations
from Cruds.Admin.Payouts import (
    calculate_event_revenue, create_payout, get_payout_by_id, get_payouts,
    update_payout, get_admin_dashboard, create_bulk_payouts
)

# Import schemas
from Schemas.Payouts import (
    PayoutCreate, PayoutUpdate, PayoutOut, PayoutListFilter, PayoutListResponse,
    EventRevenueBreakdown, RevenueCalculationRequest, AdminPayoutDashboard,
    BulkPayoutCreate, BulkPayoutResponse
)
from Models.Events import PayoutStatusEnum, PayoutMethodEnum

router = APIRouter()


# ==================== REVENUE CALCULATION ====================

@router.post("/calculate-revenue/{event_id}", response_model=EventRevenueBreakdown)
def calculate_event_revenue_endpoint(
    event_id: UUID,
    calculation_request: RevenueCalculationRequest,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("admin"))
):
    """
    Calculate revenue breakdown for an event.
    
    This endpoint helps admins understand the financial details before creating a payout:
    - Total revenue from ticket sales
    - Commission calculation
    - Net payout amount
    - Ticket type breakdown
    """
    current_user = get_current_user(token, db)
    
    try:
        return calculate_event_revenue(
            db=db, 
            event_id=calculation_request.event_id,
            commission_rate=calculation_request.commission_rate
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to calculate revenue: {str(e)}"
        )


# ==================== PAYOUT MANAGEMENT ====================

@router.post("/payouts", response_model=PayoutOut)
def create_payout_endpoint(
    payout_data: PayoutCreate,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("admin"))
):
    """
    Create a new payout for an institute.
    
    This creates a payout record for manual processing. The admin will need to:
    1. Transfer money manually via the specified method
    2. Update the payout status to 'COMPLETED' with transaction reference
    """
    current_user = get_current_user(token, db)
    
    try:
        return create_payout(db, payout_data, current_user.id)
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create payout: {str(e)}"
        )


@router.get("/payouts", response_model=PayoutListResponse)
def get_payouts_endpoint(
    skip: int = Query(0, ge=0, description="Number of payouts to skip"),
    limit: int = Query(20, ge=1, le=100, description="Number of payouts to return"),
    
    # Filter parameters
    institute_id: Optional[UUID] = Query(None, description="Filter by institute"),
    event_id: Optional[UUID] = Query(None, description="Filter by event"),
    status: Optional[List[PayoutStatusEnum]] = Query(None, description="Filter by status"),
    method: Optional[List[PayoutMethodEnum]] = Query(None, description="Filter by payout method"),
    min_amount: Optional[Decimal] = Query(None, description="Minimum payout amount"),
    max_amount: Optional[Decimal] = Query(None, description="Maximum payout amount"),
    
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("admin"))
):
    """
    Get paginated list of all payouts with filtering options.
    """
    current_user = get_current_user(token, db)
    
    filters = PayoutListFilter(
        institute_id=institute_id,
        event_id=event_id,
        status=status,
        method=method,
        min_amount=min_amount,
        max_amount=max_amount
    )
    
    try:
        return get_payouts(db, filters, skip, limit)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get payouts: {str(e)}"
        )


@router.get("/payouts/{payout_id}", response_model=PayoutOut)
def get_payout_endpoint(
    payout_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("admin"))
):
    """
    Get detailed information about a specific payout.
    """
    current_user = get_current_user(token, db)
    
    try:
        return get_payout_by_id(db, payout_id)
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get payout: {str(e)}"
        )


@router.patch("/payouts/{payout_id}", response_model=PayoutOut)
def update_payout_endpoint(
    payout_id: UUID,
    payout_update: PayoutUpdate,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("admin"))
):
    """
    Update payout status and details.
    
    Use this endpoint to:
    - Mark payout as COMPLETED after manual transfer
    - Add transaction reference and proof
    - Mark as FAILED with failure reason
    - Add administrative notes
    """
    current_user = get_current_user(token, db)
    
    try:
        return update_payout(db, payout_id, payout_update, current_user.id)
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update payout: {str(e)}"
        )


# ==================== BULK OPERATIONS ====================

@router.post("/payouts/bulk", response_model=BulkPayoutResponse)
def create_bulk_payouts_endpoint(
    bulk_request: BulkPayoutCreate,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("admin"))
):
    """
    Create payouts for multiple events at once.
    
    This is useful for processing payouts in batches. The system will:
    - Calculate revenue for each event
    - Create payouts only for events with revenue and no existing payout
    - Use institute's preferred payment method details
    - Return summary of created and failed payouts
    """
    current_user = get_current_user(token, db)
    
    try:
        return create_bulk_payouts(db, bulk_request, current_user.id)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create bulk payouts: {str(e)}"
        )


# ==================== ADMIN DASHBOARD ====================

@router.get("/dashboard", response_model=AdminPayoutDashboard)
def get_admin_payout_dashboard(
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("admin"))
):
    """
    Get comprehensive admin payout dashboard.
    
    Provides overview of:
    - Payout statistics and financial summary
    - Recent payout activity
    - Events requiring payout attention
    - Monthly trends and analytics
    """
    current_user = get_current_user(token, db)
    
    try:
        return get_admin_dashboard(db)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get dashboard: {str(e)}"
        )


# ==================== QUICK ACTIONS ====================

@router.get("/events/requiring-payout")
def get_events_requiring_payout(
    limit: int = Query(50, ge=1, le=100, description="Number of events to return"),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("admin"))
):
    """
    Get list of events that have revenue but no payout created yet.
    """
    current_user = get_current_user(token, db)
    
    try:
        dashboard = get_admin_dashboard(db)
        return {
            "events": dashboard.events_requiring_payout[:limit],
            "total_count": len(dashboard.events_requiring_payout),
            "total_pending_amount": sum(e.net_payout_amount for e in dashboard.events_requiring_payout)
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get events requiring payout: {str(e)}"
        )


@router.get("/statistics/summary")
def get_payout_statistics_summary(
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("admin"))
):
    """
    Get quick payout statistics summary.
    """
    current_user = get_current_user(token, db)
    
    try:
        dashboard = get_admin_dashboard(db)
        return {
            "total_payouts": dashboard.total_payouts,
            "pending_payouts": dashboard.pending_payouts,
            "completed_payouts": dashboard.completed_payouts,
            "failed_payouts": dashboard.failed_payouts,
            "total_payout_amount": float(dashboard.total_payout_amount),
            "total_commission_earned": float(dashboard.total_commission_earned),
            "pending_payout_amount": float(dashboard.pending_payout_amount),
            "events_requiring_payout": len(dashboard.events_requiring_payout)
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get statistics: {str(e)}"
        )
