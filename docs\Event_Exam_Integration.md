# Event-Exam Integration for Competitions
## Optional Exam Assignment to Events

---

## 🎯 **Overview**

The Event model has been enhanced with an optional `exam_id` field to support competitions that require exams. This allows institutes to create competitions and assign existing exams to them.

---

## 🔧 **Changes Made**

### **1. Database Model Updates** ✅

#### **Event Model** (`app/Models/Events.py`):
```python
class Event(BaseModel):
    # ... existing fields ...
    
    # Competition-specific field
    exam_id = Column(UUID(as_uuid=True), ForeignKey('exams.id'), nullable=True)
    
    # ... existing fields ...
    
    # Relationships
    exam = relationship('Exam', foreign_keys=[exam_id], backref='competition_events')
```

#### **Database Migration**:
- ✅ Added `exam_id` column to `events` table
- ✅ Added foreign key constraint to `exams` table
- ✅ Created index for performance optimization

### **2. Schema Updates** ✅

#### **EventBase Schema** (`app/Schemas/Events/Events.py`):
```python
class EventBase(BaseModel):
    # ... existing fields ...
    exam_id: Optional[UUID] = Field(None, description="Optional exam ID for competitions")
    # ... existing fields ...
```

#### **EventUpdate Schema**:
```python
class EventUpdate(BaseModel):
    # ... existing fields ...
    exam_id: Optional[UUID] = Field(None, description="Optional exam ID for competitions")
    # ... existing fields ...
```

### **3. CRUD Validation** ✅

#### **Event Creation Validation** (`app/Cruds/Events/Events.py`):
```python
def create_event(db: Session, event_data: EventCreate, organizer_id: UUID) -> EventOut:
    # ... existing validation ...
    
    # Validate exam_id if provided (for competitions)
    if event_data.exam_id:
        # Check if exam exists
        exam = db.query(Exam).filter(Exam.id == event_data.exam_id).first()
        if not exam:
            raise HTTPException(status_code=404, detail="Exam not found")
        
        # Verify the exam belongs to the institute
        institute_questions = db.query(Question).filter(
            Question.teacher_id == organizer_id,
            Question.exams.any(id=event_data.exam_id)
        ).first()
        
        if not institute_questions:
            raise HTTPException(
                status_code=403, 
                detail="You can only use exams that belong to your institute"
            )
    
    # ... rest of creation logic ...
```

---

## 📋 **API Usage Examples**

### **1. Create Competition with Exam**

#### **Request**:
```json
POST /api/events/
{
  "title": "Mathematics Competition 2024",
  "description": "Annual mathematics competition for students",
  "category": "COMPETITION",
  "start_datetime": "2024-02-15T10:00:00Z",
  "end_datetime": "2024-02-15T12:00:00Z",
  "location": "Main Auditorium",
  "exam_id": "123e4567-e89b-12d3-a456-************",
  "max_attendees": 100,
  "is_public": true
}
```

#### **Response**:
```json
{
  "id": "987fcdeb-51a2-43d1-9f4e-123456789abc",
  "title": "Mathematics Competition 2024",
  "category": "COMPETITION",
  "exam_id": "123e4567-e89b-12d3-a456-************",
  "organizer_id": "institute-uuid",
  "institute_id": "institute-uuid",
  "status": "DRAFT",
  "created_at": "2024-01-10T10:00:00Z",
  "updated_at": "2024-01-10T10:00:00Z"
}
```

### **2. Create Regular Event (No Exam)**

#### **Request**:
```json
POST /api/events/
{
  "title": "Tech Workshop 2024",
  "description": "Introduction to AI and Machine Learning",
  "category": "WORKSHOP",
  "start_datetime": "2024-02-20T14:00:00Z",
  "end_datetime": "2024-02-20T17:00:00Z",
  "location": "Computer Lab",
  "exam_id": null,
  "max_attendees": 50
}
```

### **3. Update Event to Add/Remove Exam**

#### **Add Exam to Existing Event**:
```json
PUT /api/events/{event_id}
{
  "exam_id": "123e4567-e89b-12d3-a456-************"
}
```

#### **Remove Exam from Event**:
```json
PUT /api/events/{event_id}
{
  "exam_id": null
}
```

---

## 🔒 **Security & Validation**

### **Exam Ownership Validation**:
1. ✅ **Exam Exists**: Verifies the exam ID exists in the database
2. ✅ **Institute Ownership**: Ensures the exam belongs to the creating institute
3. ✅ **Question Verification**: Checks that institute has questions in the exam

### **Error Responses**:

#### **Exam Not Found**:
```json
HTTP 404 Not Found
{
  "detail": "Exam not found"
}
```

#### **Unauthorized Exam Usage**:
```json
HTTP 403 Forbidden
{
  "detail": "You can only use exams that belong to your institute"
}
```

---

## 🎨 **Frontend Integration**

### **Event Creation Form**:
```jsx
function EventCreationForm() {
  const [eventData, setEventData] = useState({
    title: '',
    category: 'WORKSHOP',
    exam_id: null, // Optional exam selection
    // ... other fields
  });

  const [availableExams, setAvailableExams] = useState([]);

  // Load institute's exams for selection
  useEffect(() => {
    if (eventData.category === 'COMPETITION') {
      fetchInstituteExams().then(setAvailableExams);
    }
  }, [eventData.category]);

  return (
    <form>
      {/* Basic event fields */}
      <input 
        value={eventData.title}
        onChange={(e) => setEventData({...eventData, title: e.target.value})}
        placeholder="Event Title"
      />
      
      <select 
        value={eventData.category}
        onChange={(e) => setEventData({...eventData, category: e.target.value})}
      >
        <option value="WORKSHOP">Workshop</option>
        <option value="CONFERENCE">Conference</option>
        <option value="WEBINAR">Webinar</option>
        <option value="COMPETITION">Competition</option>
      </select>

      {/* Show exam selection only for competitions */}
      {eventData.category === 'COMPETITION' && (
        <div>
          <label>Select Exam (Optional):</label>
          <select 
            value={eventData.exam_id || ''}
            onChange={(e) => setEventData({
              ...eventData, 
              exam_id: e.target.value || null
            })}
          >
            <option value="">No Exam</option>
            {availableExams.map(exam => (
              <option key={exam.id} value={exam.id}>
                {exam.title} ({exam.questions_count} questions)
              </option>
            ))}
          </select>
        </div>
      )}

      {/* Other event fields */}
    </form>
  );
}
```

### **Event Display**:
```jsx
function EventCard({ event }) {
  return (
    <div className="event-card">
      <h3>{event.title}</h3>
      <p>{event.description}</p>
      <span className="category">{event.category}</span>
      
      {/* Show exam info for competitions */}
      {event.category === 'COMPETITION' && event.exam_id && (
        <div className="exam-info">
          <span>📝 Exam Required</span>
          <button onClick={() => viewExamDetails(event.exam_id)}>
            View Exam Details
          </button>
        </div>
      )}
      
      <div className="event-actions">
        <button>Register</button>
        {event.category === 'COMPETITION' && event.exam_id && (
          <button>Take Exam</button>
        )}
      </div>
    </div>
  );
}
```

---

## 🔄 **Workflow Examples**

### **Competition Creation Workflow**:
1. **Institute creates exam** using existing exam creation system
2. **Institute creates competition event** with optional exam assignment
3. **Students register** for the competition
4. **Students take the assigned exam** (if any)
5. **Mentors evaluate** exam submissions
6. **Results are published** for the competition

### **Event Types**:

| Event Type | Exam Required | Use Case |
|------------|---------------|----------|
| **Workshop** | ❌ No | Educational sessions, training |
| **Conference** | ❌ No | Professional gatherings, presentations |
| **Webinar** | ❌ No | Online educational content |
| **Competition** | ✅ Optional | Academic competitions with/without exams |

---

## 🚀 **Benefits**

### **For Institutes**:
- ✅ **Flexible Competition Creation**: Can create competitions with or without exams
- ✅ **Exam Reusability**: Use existing exams for multiple competitions
- ✅ **Centralized Management**: Manage events and exams from one place

### **For Students**:
- ✅ **Clear Requirements**: Know if an exam is required for competition
- ✅ **Integrated Experience**: Seamless flow from registration to exam taking
- ✅ **Transparent Process**: Clear understanding of competition structure

### **For System**:
- ✅ **Data Integrity**: Proper foreign key relationships
- ✅ **Performance**: Indexed exam_id for fast queries
- ✅ **Flexibility**: Optional field allows various event types

---

## 🧪 **Testing**

### **Test Scenarios**:
1. ✅ **Create competition with valid exam** → Success
2. ✅ **Create competition with invalid exam ID** → 404 Error
3. ✅ **Create competition with exam from different institute** → 403 Error
4. ✅ **Create workshop without exam** → Success
5. ✅ **Update event to add exam** → Success
6. ✅ **Update event to remove exam** → Success

---

The Event model now supports optional exam assignment for competitions while maintaining backward compatibility with existing events! 🎉
