"""
Migration: Remove PayFast and enable demo mode
Date: 2024-01-10
Description: Updates payment gateway enum to remove PayFast and other gateways, keeping only DEMO and CASH for demo purposes
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from sqlalchemy import text
from sqlalchemy.orm import Session
from config.session import get_db

def run_migration():
    """Update payment gateway enum for demo mode"""
    db = next(get_db())
    
    try:
        print("🔄 Starting migration: Remove PayFast and enable demo mode...")
        
        # Step 1: Create the new enum type first
        print("📝 Creating new PaymentGatewayEnum...")
        db.execute(text("""
            CREATE TYPE paymentgatewayenum_new AS ENUM ('DEMO', 'CASH')
        """))

        # Step 2: Update the column to use the new enum, converting PayFast to DEMO
        print("📝 Updating payment_method column...")
        db.execute(text("""
            ALTER TABLE event_payments
            ALTER COLUMN payment_method TYPE paymentgatewayenum_new
            USING
                CASE
                    WHEN payment_method = 'PAYFAST' THEN 'DEMO'::paymentgatewayenum_new
                    WHEN payment_method = 'CASH' THEN 'CASH'::paymentgatewayenum_new
                    ELSE 'DEMO'::paymentgatewayenum_new
                END
        """))

        # Step 3: Drop the old enum and rename the new one
        print("📝 Replacing old enum...")
        db.execute(text("""
            DROP TYPE IF EXISTS paymentgatewayenum CASCADE
        """))

        db.execute(text("""
            ALTER TYPE paymentgatewayenum_new RENAME TO paymentgatewayenum
        """))

        # Step 4: Update existing registrations with PayFast payment method
        print("📝 Updating existing registrations with PayFast payment method...")
        db.execute(text("""
            UPDATE event_registrations
            SET payment_method = 'demo'
            WHERE payment_method = 'payfast'
        """))
        
        # Step 5: Update any pending registrations to confirmed (demo mode)
        print("📝 Auto-confirming pending registrations for demo mode...")
        db.execute(text("""
            UPDATE event_registrations
            SET status = 'CONFIRMED',
                payment_status = 'COMPLETED',
                payment_method = 'demo',
                payment_reference = CONCAT('DEMO-', registration_number),
                confirmed_at = NOW()
            WHERE status = 'PENDING' AND total_amount > 0
        """))

        # Step 6: Update pending payments to completed (demo mode)
        print("📝 Auto-completing pending payments for demo mode...")
        db.execute(text("""
            UPDATE event_payments
            SET status = 'COMPLETED',
                payment_method = 'DEMO',
                gateway_transaction_id = CONCAT('DEMO-', id),
                processed_at = NOW(),
                payment_description = 'Demo payment - auto-completed'
            WHERE status = 'PENDING'
        """))
        
        db.commit()
        print("✅ Migration completed successfully!")
        print("   - Updated PayFast payments to DEMO")
        print("   - Updated payment gateway enum to DEMO/CASH only")
        print("   - Auto-confirmed pending registrations")
        print("   - Auto-completed pending payments")
        print("   - System is now in demo mode!")
        
    except Exception as e:
        db.rollback()
        print(f"❌ Migration failed: {str(e)}")
        raise
    finally:
        db.close()

def rollback_migration():
    """Rollback demo mode changes (restore original payment gateways)"""
    db = next(get_db())

    try:
        print("🔄 Starting rollback: Restore original payment gateways...")

        # Recreate the original enum with all payment gateways
        print("📝 Restoring original PaymentGatewayEnum...")

        # Create the original enum
        db.execute(text("""
            CREATE TYPE paymentgatewayenum_original AS ENUM (
                'STRIPE', 'PAYPAL', 'RAZORPAY', 'BANK_TRANSFER', 'CASH'
            )
        """))

        # Update the column to use the original enum
        db.execute(text("""
            ALTER TABLE event_payments
            ALTER COLUMN payment_method TYPE paymentgatewayenum_original
            USING
                CASE
                    WHEN payment_method = 'DEMO' THEN 'CASH'::paymentgatewayenum_original
                    ELSE payment_method::text::paymentgatewayenum_original
                END
        """))

        # Drop the demo enum and rename the original
        db.execute(text("""
            DROP TYPE IF EXISTS paymentgatewayenum CASCADE
        """))

        db.execute(text("""
            ALTER TYPE paymentgatewayenum_original RENAME TO paymentgatewayenum
        """))

        # Revert demo payments back to cash
        print("📝 Reverting demo payments back to cash...")
        db.execute(text("""
            UPDATE event_payments
            SET payment_method = 'CASH'
            WHERE payment_method = 'DEMO'
        """))

        db.execute(text("""
            UPDATE event_registrations
            SET payment_method = 'cash'
            WHERE payment_method = 'demo'
        """))

        db.commit()
        print("✅ Rollback completed successfully!")

    except Exception as e:
        db.rollback()
        print(f"❌ Rollback failed: {str(e)}")
        raise
    finally:
        db.close()

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "rollback":
        rollback_migration()
    else:
        run_migration()
