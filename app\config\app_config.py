"""
Application configuration module for EduFair API.

This module contains the FastAPI application configuration and factory function.
"""

from fastapi import FastAPI


def create_app_config() -> dict:
    """
    Create and return the FastAPI application configuration.
    
    Returns:
        dict: Configuration dictionary for FastAPI app initialization
    """
    return {
        "title": "EduFair API",
        "description": "Educational Platform API with comprehensive monitoring and error handling",
        "version": "1.0.0",
        "docs_url": "/docs",
        "redoc_url": "/redoc"
    }


def create_app() -> FastAPI:
    """
    Create and configure the FastAPI application instance.
    
    Returns:
        FastAPI: Configured FastAPI application instance
    """
    config = create_app_config()
    app = FastAPI(**config)
    
    return app
