"""
Ticket Email Service for EduFair Platform

This service handles sending ticket confirmation emails, ticket attachments,
and event-related notifications to users.
"""

from datetime import datetime, timezone
from typing import Optional, Dict, Any
from decimal import Decimal
from sqlalchemy.orm import Session

from services.email_service import EmailService
from Models.Events import EventRegistration, Event, EventTicket
from Models.users import User


class TicketEmailService:
    """Service for sending ticket-related emails"""
    
    def __init__(self):
        self.email_service = EmailService()
    
    def send_ticket_confirmation(
        self,
        db: Session,
        registration: EventRegistration,
        user: User,
        event: Event,
        ticket: Optional[EventTicket] = None
    ) -> bool:
        """
        Send ticket confirmation email with QR code and event details
        
        Args:
            db: Database session
            registration: Event registration object
            user: User object
            event: Event object
            ticket: Event ticket object (optional for free events)
            
        Returns:
            bool: True if email sent successfully
        """
        try:
            # Prepare email content
            subject = f"🎫 Your Ticket for {event.title} - Confirmed!"

            html_content = self._generate_ticket_html(
                registration, user, event, ticket
            )

            text_content = self._generate_ticket_text(
                registration, user, event, ticket
            )
            
            # Send email
            return self.email_service.send_email(
                to_email=user.email,
                subject=subject,
                html_content=html_content,
                text_content=text_content
            )
            
        except Exception as e:
            print(f"Error sending ticket confirmation email: {e}")
            return False
    

    
    def _generate_ticket_html(
        self,
        registration: EventRegistration,
        user: User,
        event: Event,
        ticket: Optional[EventTicket]
    ) -> str:
        """Generate HTML content for ticket email"""
        
        # Format event date and time
        event_date = event.start_datetime.strftime("%A, %B %d, %Y")
        event_time = event.start_datetime.strftime("%I:%M %p")
        
        # Ticket type and price info
        ticket_info = ""
        if ticket:
            ticket_info = f"""
            <tr>
                <td style="padding: 8px 0; border-bottom: 1px solid #eee;"><strong>Ticket Type:</strong></td>
                <td style="padding: 8px 0; border-bottom: 1px solid #eee;">{ticket.name}</td>
            </tr>
            <tr>
                <td style="padding: 8px 0; border-bottom: 1px solid #eee;"><strong>Price:</strong></td>
                <td style="padding: 8px 0; border-bottom: 1px solid #eee;">{ticket.currency} {registration.total_amount}</td>
            </tr>
            """
        else:
            ticket_info = """
            <tr>
                <td style="padding: 8px 0; border-bottom: 1px solid #eee;"><strong>Ticket Type:</strong></td>
                <td style="padding: 8px 0; border-bottom: 1px solid #eee;">Free Event</td>
            </tr>
            """
        
        # Ticket info section
        ticket_section = f"""
        <div style="text-align: center; margin: 30px 0; padding: 20px; background: #f8f9fa; border-radius: 10px;">
            <h3 style="color: #333; margin-bottom: 15px;">🎫 Your Digital Ticket</h3>
            <div style="background: white; padding: 20px; border-radius: 8px; border: 2px solid #667eea;">
                <h4 style="margin: 0; color: #667eea;">Registration Number</h4>
                <p style="font-size: 24px; font-weight: bold; margin: 10px 0; color: #333;">{registration.registration_number}</p>
                <p style="margin: 0; color: #666; font-size: 14px;">
                    Show this registration number at the event entrance for check-in
                </p>
            </div>
        </div>
        """
        
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Your Event Ticket</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background-color: #f4f4f4;">
            <div style="max-width: 600px; margin: 0 auto; background: white; border-radius: 10px; overflow: hidden; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
                
                <!-- Header -->
                <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center;">
                    <h1 style="margin: 0; font-size: 28px;">🎫 Ticket Confirmed!</h1>
                    <p style="margin: 10px 0 0 0; font-size: 16px; opacity: 0.9;">You're all set for {event.title}</p>
                </div>
                
                <!-- Content -->
                <div style="padding: 30px;">
                    <div style="background: #e8f5e8; border-left: 4px solid #28a745; padding: 15px; margin-bottom: 25px; border-radius: 4px;">
                        <h3 style="margin: 0 0 5px 0; color: #155724;">✅ Registration Confirmed</h3>
                        <p style="margin: 0; color: #155724;">Your registration has been successfully confirmed. See you at the event!</p>
                    </div>
                    
                    <h2 style="color: #333; border-bottom: 2px solid #667eea; padding-bottom: 10px;">Event Details</h2>
                    
                    <table style="width: 100%; margin-bottom: 25px;">
                        <tr>
                            <td style="padding: 8px 0; border-bottom: 1px solid #eee;"><strong>Event:</strong></td>
                            <td style="padding: 8px 0; border-bottom: 1px solid #eee;">{event.title}</td>
                        </tr>
                        <tr>
                            <td style="padding: 8px 0; border-bottom: 1px solid #eee;"><strong>Date:</strong></td>
                            <td style="padding: 8px 0; border-bottom: 1px solid #eee;">{event_date}</td>
                        </tr>
                        <tr>
                            <td style="padding: 8px 0; border-bottom: 1px solid #eee;"><strong>Time:</strong></td>
                            <td style="padding: 8px 0; border-bottom: 1px solid #eee;">{event_time}</td>
                        </tr>
                        <tr>
                            <td style="padding: 8px 0; border-bottom: 1px solid #eee;"><strong>Location:</strong></td>
                            <td style="padding: 8px 0; border-bottom: 1px solid #eee;">{event.location or 'TBA'}</td>
                        </tr>
                        <tr>
                            <td style="padding: 8px 0; border-bottom: 1px solid #eee;"><strong>Registration #:</strong></td>
                            <td style="padding: 8px 0; border-bottom: 1px solid #eee;"><strong>{registration.registration_number}</strong></td>
                        </tr>
                        {ticket_info}
                        <tr>
                            <td style="padding: 8px 0; border-bottom: 1px solid #eee;"><strong>Quantity:</strong></td>
                            <td style="padding: 8px 0; border-bottom: 1px solid #eee;">{registration.quantity} ticket(s)</td>
                        </tr>
                    </table>

                    {ticket_section}
                    
                    <div style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 8px; margin: 25px 0;">
                        <h4 style="margin: 0 0 10px 0; color: #856404;">📋 Important Information</h4>
                        <ul style="margin: 0; padding-left: 20px; color: #856404;">
                            <li>Please arrive 15 minutes before the event starts</li>
                            <li>Bring a valid ID for verification</li>
                            <li>Keep this email handy for easy access to your ticket</li>
                            <li>Contact support if you need to make any changes</li>
                        </ul>
                    </div>
                    
                    <div style="text-align: center; margin-top: 30px;">
                        <a href="#" style="background: #667eea; color: white; padding: 12px 30px; text-decoration: none; border-radius: 25px; display: inline-block; font-weight: bold;">View Full Ticket</a>
                    </div>
                </div>
                
                <!-- Footer -->
                <div style="background: #f8f9fa; padding: 20px; text-align: center; border-top: 1px solid #eee;">
                    <p style="margin: 0; color: #666; font-size: 14px;">© 2025 EduFair. All rights reserved.</p>
                    <p style="margin: 5px 0 0 0; color: #666; font-size: 12px;">This is an automated email. Please do not reply to this message.</p>
                </div>
            </div>
        </body>
        </html>
        """
        
        return html_content
    
    def _generate_ticket_text(
        self,
        registration: EventRegistration,
        user: User,
        event: Event,
        ticket: Optional[EventTicket]
    ) -> str:
        """Generate plain text content for ticket email"""
        
        event_date = event.start_datetime.strftime("%A, %B %d, %Y")
        event_time = event.start_datetime.strftime("%I:%M %p")
        
        ticket_info = ""
        if ticket:
            ticket_info = f"""
Ticket Type: {ticket.name}
Price: {ticket.currency} {registration.total_amount}
"""
        else:
            ticket_info = "Ticket Type: Free Event\n"
        
        text_content = f"""
🎫 TICKET CONFIRMED - {event.title}

Hello {user.username}!

Your registration has been successfully confirmed! Here are your event details:

EVENT DETAILS:
Event: {event.title}
Date: {event_date}
Time: {event_time}
Location: {event.location or 'TBA'}
Registration #: {registration.registration_number}
{ticket_info}Quantity: {registration.quantity} ticket(s)

IMPORTANT INFORMATION:
• Please arrive 15 minutes before the event starts
• Bring a valid ID for verification
• Keep this email handy for easy access to your ticket
• Contact support if you need to make any changes

We're excited to see you at the event!

© 2025 EduFair. All rights reserved.
This is an automated email. Please do not reply to this message.
        """
        
        return text_content


# Create global ticket email service instance
ticket_email_service = TicketEmailService()
