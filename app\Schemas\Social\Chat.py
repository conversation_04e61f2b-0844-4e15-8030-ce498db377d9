"""
Chat Schemas for EduFair Platform

This module contains Pydantic schemas for chat functionality between users.
"""

from pydantic import BaseModel, Field, validator
from uuid import UUID
from datetime import datetime
from typing import Optional, List


# ==================== BASE SCHEMAS ====================

class ChatMessageBase(BaseModel):
    """Base schema for chat message"""
    message: str = Field(..., min_length=1, max_length=2000, description="Message content")


class ChatMessageCreate(ChatMessageBase):
    """Schema for creating a chat message"""
    receiver_id: UUID = Field(..., description="ID of message receiver")


class ChatMessageUpdate(BaseModel):
    """Schema for updating a chat message"""
    message: Optional[str] = Field(None, min_length=1, max_length=2000)
    is_deleted: Optional[bool] = None


# ==================== RESPONSE SCHEMAS ====================

class ChatMessageResponse(BaseModel):
    """Schema for chat message response"""
    id: UUID
    sender_id: UUID
    receiver_id: UUID
    message: str
    sent_at: datetime
    read_at: Optional[datetime] = None
    is_deleted: bool = False
    
    class Config:
        from_attributes = True


class ChatMessageWithUsers(ChatMessageResponse):
    """Chat message with sender/receiver info"""
    sender_username: str
    receiver_username: str
    sender_profile_picture: Optional[str] = None
    receiver_profile_picture: Optional[str] = None


# ==================== CONVERSATION SCHEMAS ====================

class ConversationUser(BaseModel):
    """User info for conversations"""
    id: UUID
    username: str
    profile_picture: Optional[str] = None
    user_type: str
    is_online: bool = False
    last_seen: Optional[datetime] = None
    
    class Config:
        from_attributes = True


class ConversationSummary(BaseModel):
    """Summary of a conversation"""
    other_user: ConversationUser
    last_message: Optional[ChatMessageResponse] = None
    unread_count: int = 0
    last_activity: datetime


class ConversationListResponse(BaseModel):
    """Response for conversation list"""
    conversations: List[ConversationSummary]
    total_count: int
    page: int
    page_size: int
    has_next: bool
    has_previous: bool


# ==================== MESSAGE HISTORY ====================

class MessageHistoryRequest(BaseModel):
    """Request for message history"""
    other_user_id: UUID
    page: int = Field(1, ge=1)
    page_size: int = Field(20, ge=1, le=100)
    before_message_id: Optional[UUID] = None


class MessageHistoryResponse(BaseModel):
    """Response for message history"""
    messages: List[ChatMessageWithUsers]
    total_count: int
    page: int
    page_size: int
    has_next: bool
    has_previous: bool
    conversation_partner: ConversationUser


# ==================== BULK OPERATIONS ====================

class BulkMessageRead(BaseModel):
    """Schema for marking multiple messages as read"""
    message_ids: List[UUID] = Field(..., max_items=100)


class BulkMessageDelete(BaseModel):
    """Schema for deleting multiple messages"""
    message_ids: List[UUID] = Field(..., max_items=100)
    permanent: bool = Field(False, description="Permanently delete or just mark as deleted")


class BulkOperationResponse(BaseModel):
    """Response for bulk operations"""
    successful_operations: List[UUID]
    failed_operations: List[dict]  # [{"message_id": UUID, "error": str}]
    total_processed: int
    success_count: int
    failure_count: int


# ==================== CHAT STATISTICS ====================

class ChatStats(BaseModel):
    """Chat statistics for a user"""
    user_id: UUID
    total_conversations: int = 0
    total_messages_sent: int = 0
    total_messages_received: int = 0
    unread_messages_count: int = 0
    active_conversations_count: int = 0


class ConversationStats(BaseModel):
    """Statistics for a specific conversation"""
    other_user_id: UUID
    total_messages: int = 0
    messages_sent: int = 0
    messages_received: int = 0
    first_message_date: Optional[datetime] = None
    last_message_date: Optional[datetime] = None
    unread_count: int = 0


# ==================== SEARCH AND FILTER ====================

class MessageSearchRequest(BaseModel):
    """Request for searching messages"""
    query: str = Field(..., min_length=1, max_length=100)
    other_user_id: Optional[UUID] = None
    date_from: Optional[datetime] = None
    date_to: Optional[datetime] = None
    page: int = Field(1, ge=1)
    page_size: int = Field(20, ge=1, le=100)


class MessageSearchResponse(BaseModel):
    """Response for message search"""
    messages: List[ChatMessageWithUsers]
    total_count: int
    page: int
    page_size: int
    has_next: bool
    has_previous: bool
    search_query: str


# ==================== VALIDATION ====================

class ChatPermissionCheck(BaseModel):
    """Schema for checking chat permissions"""
    can_send_message: bool
    can_receive_message: bool
    reason: Optional[str] = None  # If permission denied, reason why


# ==================== TYPING INDICATORS ====================

class TypingIndicator(BaseModel):
    """Typing indicator schema"""
    user_id: UUID
    conversation_with: UUID
    is_typing: bool
    timestamp: datetime = Field(default_factory=datetime.now)
