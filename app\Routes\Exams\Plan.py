from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from uuid import UUID
from config.session import get_db
from Schemas.Exams.Plan import (
    PlanCreate,
    PlanOut,
    PlanUpdate,
    PlanList,
)
from Cruds.Exams.Plan import (
    create_plan,
    get_plan_by_id,
    get_plan_by_name,
    get_all_plans,
    get_active_plans,
    update_plan,
    activate_plan
    )
from config.deps import oauth2_scheme
from config.permission import require_type

router = APIRouter()

@router.post("/", response_model=PlanOut)
def create_new_plan(
    plan: PlanCreate,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("admin"))
):
    """Create a new subscription plan"""
    return create_plan(db, plan)

@router.get("/{plan_id}", response_model=PlanOut)
def get_plan(
    plan_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """Get plan by ID"""
    return get_plan_by_id(db, plan_id)

@router.get("/by-name/{name}", response_model=PlanOut)
def get_plan_by_name_route(
    name: str,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """Get plan by name"""
    return get_plan_by_name(db, name)

@router.get("/", response_model=List[PlanList])
def list_plans(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    active_only: bool = Query(False),
    name: Optional[str] = Query(None, description="Filter by name"),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """List all plans with optional filtering"""
    return get_all_plans(db, skip=skip, limit=limit, active_only=active_only, name_filter=name)

@router.get("/active/", response_model=List[PlanList])
def list_active_plans(
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """List all active plans"""
    return get_active_plans(db)


@router.put("/{plan_id}", response_model=PlanOut)
def update_plan_route(
    plan_id: UUID,
    plan_update: PlanUpdate,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("admin"))
):
    """Update a plan by ID"""
    return update_plan(db, plan_id, plan_update)

@router.patch("/{plan_id}/activate", response_model=PlanOut)
def activate_plan_route(
    plan_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("admin"))
):
    """Activate a plan"""
    return activate_plan(db, plan_id)
