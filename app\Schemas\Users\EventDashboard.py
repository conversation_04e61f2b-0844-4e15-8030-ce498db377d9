"""
User Event Dashboard Schemas for EduFair Platform

This module contains Pydantic schemas for user event dashboard functionality including:
- User event history and status tracking
- Event registration summaries
- Ticket information and QR codes
- Event participation analytics
"""

from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field
from datetime import datetime
from uuid import UUID
from decimal import Decimal
from enum import Enum

# Import Enums
from Models.Events import (
    RegistrationStatusEnum, PaymentStatusEnum, EventStatusEnum, 
    EventCategoryEnum, TicketStatusEnum
)


class EventParticipationStatus(str, Enum):
    """User's participation status for events"""
    UPCOMING = "upcoming"
    ONGOING = "ongoing"
    COMPLETED = "completed"
    CANCELLED = "cancelled"
    MISSED = "missed"


class UserEventSummary(BaseModel):
    """Summary of user's event participation"""
    event_id: UUID
    event_title: str
    event_description: Optional[str] = None
    event_category: EventCategoryEnum
    event_status: EventStatusEnum
    event_location: Optional[str] = None
    event_start_datetime: datetime
    event_end_datetime: datetime
    
    # Registration details
    registration_id: UUID
    registration_status: RegistrationStatusEnum
    registration_number: str
    registered_at: datetime
    confirmed_at: Optional[datetime] = None
    attended_at: Optional[datetime] = None
    cancelled_at: Optional[datetime] = None
    
    # Ticket details
    ticket_id: Optional[UUID] = None
    ticket_name: Optional[str] = None
    ticket_price: Decimal = Field(default=Decimal('0.00'))
    quantity: int = 1
    
    # Payment details
    payment_status: PaymentStatusEnum
    payment_reference: Optional[str] = None
    total_amount: Decimal
    currency: str = "PKR"
    
    # QR Code and check-in
    qr_code: Optional[str] = None
    check_in_code: Optional[str] = None
    
    # Computed fields
    participation_status: EventParticipationStatus
    can_cancel: bool = False
    can_modify: bool = False
    days_until_event: Optional[int] = None
    
    # Organizer info
    organizer_name: Optional[str] = None
    organizer_email: Optional[str] = None

    class Config:
        from_attributes = True


class UserEventStats(BaseModel):
    """User's event participation statistics"""
    total_registrations: int = 0
    confirmed_registrations: int = 0
    attended_events: int = 0
    cancelled_registrations: int = 0
    upcoming_events: int = 0
    completed_events: int = 0
    total_spent: Decimal = Field(default=Decimal('0.00'))
    currency: str = "PKR"
    
    # Category breakdown
    events_by_category: Dict[str, int] = Field(default_factory=dict)
    
    # Recent activity
    last_registration_date: Optional[datetime] = None
    last_attended_event: Optional[datetime] = None


class UserEventDashboard(BaseModel):
    """Complete user event dashboard data"""
    user_id: UUID
    user_name: str
    user_email: str
    user_type: str
    
    # Statistics
    stats: UserEventStats
    
    # Event lists
    upcoming_events: List[UserEventSummary] = Field(default_factory=list)
    past_events: List[UserEventSummary] = Field(default_factory=list)
    cancelled_events: List[UserEventSummary] = Field(default_factory=list)
    
    # Quick access
    next_event: Optional[UserEventSummary] = None
    recent_registrations: List[UserEventSummary] = Field(default_factory=list)
    
    # Dashboard metadata
    generated_at: datetime = Field(default_factory=lambda: datetime.now())
    last_updated: Optional[datetime] = None


class EventFilterOptions(BaseModel):
    """Filter options for user events"""
    status: Optional[List[RegistrationStatusEnum]] = None
    category: Optional[List[EventCategoryEnum]] = None
    participation_status: Optional[List[EventParticipationStatus]] = None
    date_from: Optional[datetime] = None
    date_to: Optional[datetime] = None
    search_query: Optional[str] = None
    payment_status: Optional[List[PaymentStatusEnum]] = None


class UserEventListResponse(BaseModel):
    """Paginated response for user events"""
    events: List[UserEventSummary]
    total_count: int
    page: int
    per_page: int
    has_next: bool
    has_prev: bool
    filters_applied: Optional[EventFilterOptions] = None


class EventTicketInfo(BaseModel):
    """Detailed ticket information for user"""
    ticket_id: UUID
    event_id: UUID
    event_title: str
    ticket_name: str
    ticket_description: Optional[str] = None
    price: Decimal
    currency: str
    quantity: int
    
    # QR Code and access
    qr_code: Optional[str] = None
    check_in_code: Optional[str] = None
    digital_ticket_url: Optional[str] = None
    
    # Event details
    event_location: Optional[str] = None
    event_start_datetime: datetime
    event_end_datetime: datetime
    
    # Status
    registration_status: RegistrationStatusEnum
    payment_status: PaymentStatusEnum
    can_transfer: bool = False
    can_refund: bool = False
    
    # Organizer contact
    organizer_contact: Optional[str] = None
    support_email: Optional[str] = None

    class Config:
        from_attributes = True


class UserEventAction(BaseModel):
    """Available actions for user on an event"""
    action_type: str  # "cancel", "modify", "download_ticket", "contact_organizer"
    action_label: str
    action_url: Optional[str] = None
    is_enabled: bool = True
    requires_confirmation: bool = False
    confirmation_message: Optional[str] = None


class UserEventDetails(BaseModel):
    """Detailed view of user's event registration"""
    event_summary: UserEventSummary
    ticket_info: Optional[EventTicketInfo] = None
    available_actions: List[UserEventAction] = Field(default_factory=list)
    
    # Additional event details
    event_agenda: Optional[List[Dict[str, Any]]] = None
    event_speakers: List[Dict[str, str]] = Field(default_factory=list)
    event_requirements: Optional[str] = None
    
    # User-specific details
    attendee_info: Optional[Dict[str, Any]] = None
    special_requirements: Optional[str] = None
    emergency_contact: Optional[Dict[str, str]] = None
    dietary_preferences: Optional[List[str]] = None
    accessibility_needs: Optional[str] = None

    class Config:
        from_attributes = True
