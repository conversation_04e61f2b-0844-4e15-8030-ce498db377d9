"""
Admin routes module.

This module contains admin-specific routes that were previously defined inline in main.py.
"""

from fastapi import APIRouter, Query, Depends
from sqlalchemy.orm import Session

from config.session import get_db
from config.deps import oauth2_scheme
from config.permission import require_type


# Create admin institute router
admin_institute_router = APIRouter()


@admin_institute_router.get("/institutes/pending")
async def get_admin_pending_institutes(
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("admin"))
):
    """Get pending institutes for admin (frontend compatibility route)"""
    from Cruds.Institute.Institute import get_institutes_pending_verification
    from Schemas.Institute.Institute import InstituteListResponse

    result = get_institutes_pending_verification(db, skip, limit)
    return result
