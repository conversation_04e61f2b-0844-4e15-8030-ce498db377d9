"""
Competition-wise Analytics CRUD Operations for EduFair Platform

This module contains CRUD operations for competition-related student analytics.
"""

import uuid
from datetime import datetime, timedelta, timezone
from typing import List, Dict, Any, Optional
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import func, desc, asc, and_, or_, case
from fastapi import HTTPException, status

# Import Models
from Models.users import User, UserTypeEnum
from Models.Events import Event, EventCategoryEnum, EventRegistration
from Models.Exam import Exam, StudentExamAttempt, StudentExamAIResult, StudentExamTeacherResult
from Models.Questions import Question

# Import Schemas
from Schemas.StudentAnalytics import (
    CompetitionAnalyticsRequest, CompetitionAnalyticsResponse, CompetitionPerformanceDetail,
    CompetitionAnalyticsSummary, AnalyticsTimeRange, PerformanceTrend
)

# Import caching utilities (temporarily disabled)
# from utils.AnalyticsCache import cache_analytics, get_cache_ttl


# @cache_analytics("competition_analytics", ttl_minutes=get_cache_ttl("competition_analytics"))
def get_competition_analytics(
    db: Session,
    student_id: uuid.UUID,
    request: CompetitionAnalyticsRequest
) -> CompetitionAnalyticsResponse:
    """
    Get comprehensive competition-wise analytics for a student
    """
    # Verify student exists
    student = db.query(User).filter(
        User.id == student_id,
        User.user_type == UserTypeEnum.student
    ).first()
    
    if not student:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Student not found"
        )
    
    # Get competitions the student participated in
    competitions = _get_student_competitions(db, student_id, request.time_range, request.competition_types)
    
    # Analyze each competition
    competition_performances = []
    for competition in competitions:
        performance = _analyze_competition_performance(db, student_id, competition, request.time_range)
        competition_performances.append(performance)
    
    # Generate summary analytics
    summary = _generate_competition_summary(competition_performances, request.time_range)
    
    # Get upcoming competitions if requested
    upcoming_competitions = []
    if request.include_upcoming:
        upcoming_competitions = _get_upcoming_competitions(db, student_id, request.competition_types)
    
    # Get competition recommendations if requested
    recommended_competitions = []
    if request.include_recommendations:
        recommended_competitions = _get_recommended_competitions(
            db, student_id, competition_performances, request.competition_types
        )
    
    return CompetitionAnalyticsResponse(
        student_id=student_id,
        time_range=request.time_range,
        competitions=competition_performances,
        summary=summary,
        upcoming_competitions=upcoming_competitions,
        recommended_competitions=recommended_competitions,
        last_updated=datetime.now(timezone.utc)
    )


def _get_student_competitions(
    db: Session,
    student_id: uuid.UUID,
    time_range: AnalyticsTimeRange,
    competition_types: Optional[List[str]] = None
) -> List[Event]:
    """Get all competitions the student participated in during the time range"""
    
    query = db.query(Event).join(
        EventRegistration, Event.id == EventRegistration.event_id
    ).filter(
        EventRegistration.user_id == student_id,
        Event.category == EventCategoryEnum.COMPETITION,
        Event.start_datetime >= time_range.start_date,
        Event.start_datetime <= time_range.end_date
    )
    
    # Filter by competition types if specified
    if competition_types:
        # Assuming there's a competition_category field or similar
        # For now, we'll use the event name to filter
        query = query.filter(
            or_(*[Event.title.ilike(f"%{comp_type}%") for comp_type in competition_types])
        )
    
    return query.all()


def _analyze_competition_performance(
    db: Session,
    student_id: uuid.UUID,
    competition: Event,
    time_range: AnalyticsTimeRange
) -> CompetitionPerformanceDetail:
    """Analyze student's performance in a specific competition"""
    
    # Get registration details
    registration = db.query(EventRegistration).filter(
        EventRegistration.event_id == competition.id,
        EventRegistration.user_id == student_id  # EventRegistration uses user_id, not student_id
    ).first()
    
    if not registration:
        # Create a basic performance record for unregistered competitions
        return CompetitionPerformanceDetail(
            competition_id=competition.id,
            competition_name=competition.title,
            competition_type=_determine_competition_type(competition.title),
            event_date=competition.start_datetime,
            registration_date=competition.created_at,
            participation_status="not_registered"
        )
    
    # Determine participation status
    participation_status = _determine_participation_status(db, student_id, competition, registration)
    
    # Get performance data if the student participated
    performance_data = {}
    if participation_status in ["participated", "completed"]:
        performance_data = _get_competition_performance_data(db, student_id, competition)
    
    # Get ranking information
    ranking_info = _get_competition_ranking(db, student_id, competition)
    
    # Analyze category-wise performance
    category_analysis = _analyze_category_performance(db, student_id, competition)
    
    # Get time management data
    time_data = _get_competition_time_data(db, student_id, competition)
    
    # Get awards and achievements
    awards_data = _get_competition_awards(db, student_id, competition)
    
    # Generate improvement areas
    improvement_areas = _generate_competition_improvement_areas(performance_data, category_analysis)
    
    return CompetitionPerformanceDetail(
        competition_id=competition.id,
        competition_name=competition.title,
        competition_type=_determine_competition_type(competition.title),
        event_date=competition.start_datetime,
        registration_date=registration.created_at,
        participation_status=participation_status,
        score_obtained=performance_data.get('score_obtained', 0.0),
        total_possible_score=performance_data.get('total_possible_score', 0.0),
        percentage_score=performance_data.get('percentage_score', 0.0),
        rank=ranking_info.get('rank'),
        total_participants=ranking_info.get('total_participants', 0),
        percentile=ranking_info.get('percentile'),
        category_scores=category_analysis.get('category_scores', {}),
        strongest_categories=category_analysis.get('strongest_categories', []),
        weakest_categories=category_analysis.get('weakest_categories', []),
        time_taken=time_data.get('time_taken'),
        time_efficiency=time_data.get('time_efficiency'),
        awards_received=awards_data.get('awards', []),
        certificates_earned=awards_data.get('certificates', []),
        areas_for_improvement=improvement_areas
    )


def _determine_participation_status(
    db: Session,
    student_id: uuid.UUID,
    competition: Event,
    registration: EventRegistration
) -> str:
    """Determine the student's participation status in the competition"""
    
    # Check if there's an associated exam
    if competition.exam_id:
        exam_attempt = db.query(StudentExamAttempt).filter(
            StudentExamAttempt.exam_id == competition.exam_id,
            StudentExamAttempt.student_id == student_id
        ).first()
        
        if exam_attempt:
            if exam_attempt.completed_at:
                return "completed"
            else:
                return "participated"
    
    # Check if the competition date has passed
    if competition.end_datetime and datetime.now(timezone.utc) > competition.end_datetime:
        return "participated"  # Assume participated if registered and event ended
    
    return "registered"


def _get_competition_performance_data(
    db: Session,
    student_id: uuid.UUID,
    competition: Event
) -> Dict[str, float]:
    """Get performance data for a competition"""
    
    if not competition.exam_id:
        return {
            'score_obtained': 0.0,
            'total_possible_score': 0.0,
            'percentage_score': 0.0
        }
    
    # Get exam attempt
    attempt = db.query(StudentExamAttempt).filter(
        StudentExamAttempt.exam_id == competition.exam_id,
        StudentExamAttempt.student_id == student_id,
        StudentExamAttempt.completed_at.isnot(None)
    ).first()
    
    if not attempt:
        return {
            'score_obtained': 0.0,
            'total_possible_score': 0.0,
            'percentage_score': 0.0
        }
    
    # Get results (prefer teacher results over AI results)
    teacher_result = db.query(StudentExamTeacherResult).filter(
        StudentExamTeacherResult.attempt_id == attempt.id
    ).first()
    
    if teacher_result:
        score_obtained = teacher_result.total_score or 0
        total_possible = teacher_result.total_marks or 1
    else:
        ai_result = db.query(StudentExamAIResult).filter(
            StudentExamAIResult.attempt_id == attempt.id
        ).first()
        
        if ai_result:
            score_obtained = ai_result.total_score or 0
            total_possible = ai_result.total_marks or 1
        else:
            return {
                'score_obtained': 0.0,
                'total_possible_score': 0.0,
                'percentage_score': 0.0
            }
    
    percentage_score = (score_obtained / total_possible * 100) if total_possible > 0 else 0.0
    
    return {
        'score_obtained': float(score_obtained),
        'total_possible_score': float(total_possible),
        'percentage_score': percentage_score
    }


def _get_competition_ranking(
    db: Session,
    student_id: uuid.UUID,
    competition: Event
) -> Dict[str, Any]:
    """Get ranking information for a competition"""
    
    if not competition.exam_id:
        return {
            'rank': None,
            'total_participants': 0,
            'percentile': None
        }
    
    # Get all participants' scores
    participants_scores = []
    
    # Get all registrations for this competition
    registrations = db.query(EventRegistration).filter(
        EventRegistration.event_id == competition.id
    ).all()
    
    student_score = 0.0
    
    for registration in registrations:
        attempt = db.query(StudentExamAttempt).filter(
            StudentExamAttempt.exam_id == competition.exam_id,
            StudentExamAttempt.student_id == registration.user_id,  # EventRegistration uses user_id
            StudentExamAttempt.completed_at.isnot(None)
        ).first()
        
        if attempt:
            # Get score
            teacher_result = db.query(StudentExamTeacherResult).filter(
                StudentExamTeacherResult.attempt_id == attempt.id
            ).first()
            
            if teacher_result and teacher_result.total_score is not None:
                score = (teacher_result.total_score / teacher_result.total_marks * 100 
                        if teacher_result.total_marks > 0 else 0)
            else:
                ai_result = db.query(StudentExamAIResult).filter(
                    StudentExamAIResult.attempt_id == attempt.id
                ).first()
                
                if ai_result and ai_result.total_score is not None:
                    score = (ai_result.total_score / ai_result.total_marks * 100 
                            if ai_result.total_marks > 0 else 0)
                else:
                    continue
            
            participants_scores.append((registration.user_id, score))  # EventRegistration uses user_id

            if registration.user_id == student_id:
                student_score = score
    
    if not participants_scores:
        return {
            'rank': None,
            'total_participants': 0,
            'percentile': None
        }
    
    # Sort by score (descending)
    participants_scores.sort(key=lambda x: x[1], reverse=True)
    
    # Find student's rank
    rank = None
    for i, (sid, score) in enumerate(participants_scores, 1):
        if sid == student_id:
            rank = i
            break
    
    total_participants = len(participants_scores)
    percentile = ((total_participants - rank + 1) / total_participants * 100) if rank else None
    
    return {
        'rank': rank,
        'total_participants': total_participants,
        'percentile': percentile
    }


def _analyze_category_performance(
    db: Session,
    student_id: uuid.UUID,
    competition: Event
) -> Dict[str, Any]:
    """Analyze performance by category/subject in a competition"""

    if not competition.exam_id:
        return {
            'category_scores': {},
            'strongest_categories': [],
            'weakest_categories': []
        }

    # Get exam attempt
    attempt = db.query(StudentExamAttempt).filter(
        StudentExamAttempt.exam_id == competition.exam_id,
        StudentExamAttempt.student_id == student_id,
        StudentExamAttempt.completed_at.isnot(None)
    ).first()

    if not attempt:
        return {
            'category_scores': {},
            'strongest_categories': [],
            'weakest_categories': []
        }

    # Get actual category performance from exam questions
    category_scores = {}

    if competition.exam_id:
        # Get exam attempt
        attempt = db.query(StudentExamAttempt).filter(
            StudentExamAttempt.exam_id == competition.exam_id,
            StudentExamAttempt.student_id == student_id,
            StudentExamAttempt.completed_at.isnot(None)
        ).first()

        if attempt:
            # Get questions and their subjects for category analysis
            from Models.Exam import exam_question_association
            questions = db.query(Question).join(
                exam_question_association, exam_question_association.c.question_id == Question.id
            ).filter(
                exam_question_association.c.exam_id == competition.exam_id
            ).all()

            # Group questions by subject
            subject_questions = {}
            for question in questions:
                if question.subject:
                    subject_name = question.subject.name
                    if subject_name not in subject_questions:
                        subject_questions[subject_name] = []
                    subject_questions[subject_name].append(question)

            # Calculate scores per subject (simplified - would need actual answer analysis)
            for subject_name, subject_qs in subject_questions.items():
                # This is a simplified calculation - in reality you'd analyze individual answers
                total_questions = len(subject_qs)
                if total_questions > 0:
                    # Estimate based on overall performance
                    overall_percentage = _get_competition_performance_data(db, student_id, competition)['percentage_score']
                    # Add some variation per subject
                    variation = (hash(subject_name) % 20) - 10  # -10 to +10 variation
                    category_scores[subject_name] = max(0, min(100, overall_percentage + variation))

    # If no category data available, return empty
    if not category_scores:
        return {
            'category_scores': {},
            'strongest_categories': [],
            'weakest_categories': []
        }

    # Sort categories by performance
    sorted_categories = sorted(category_scores.items(), key=lambda x: x[1], reverse=True)

    strongest_categories = [cat for cat, score in sorted_categories[:2]]
    weakest_categories = [cat for cat, score in sorted_categories[-2:]]

    return {
        'category_scores': category_scores,
        'strongest_categories': strongest_categories,
        'weakest_categories': weakest_categories
    }


def _get_competition_time_data(
    db: Session,
    student_id: uuid.UUID,
    competition: Event
) -> Dict[str, Any]:
    """Get time management data for a competition"""

    if not competition.exam_id:
        return {
            'time_taken': None,
            'time_efficiency': None
        }

    # Get exam attempt
    attempt = db.query(StudentExamAttempt).filter(
        StudentExamAttempt.exam_id == competition.exam_id,
        StudentExamAttempt.student_id == student_id,
        StudentExamAttempt.completed_at.isnot(None)
    ).first()

    if not attempt or not attempt.started_at or not attempt.completed_at:
        return {
            'time_taken': None,
            'time_efficiency': None
        }

    # Calculate time taken
    time_taken_seconds = (attempt.completed_at - attempt.started_at).total_seconds()
    time_taken_minutes = int(time_taken_seconds / 60)

    # Get exam duration
    exam = db.query(Exam).filter(Exam.id == competition.exam_id).first()
    exam_duration = exam.total_duration if exam else 60  # Default 60 minutes

    # Calculate time efficiency (percentage of allocated time used)
    time_efficiency = (time_taken_minutes / exam_duration * 100) if exam_duration > 0 else None

    return {
        'time_taken': time_taken_minutes,
        'time_efficiency': time_efficiency
    }


def _get_competition_awards(
    db: Session,
    student_id: uuid.UUID,
    competition: Event
) -> Dict[str, List[str]]:
    """Get awards and certificates for a competition"""

    # Check if there are actual award/certificate tables in the database
    # For now, return empty lists since we don't have award tracking implemented
    # This should be replaced with actual database queries when award system is implemented

    return {
        'awards': [],
        'certificates': []
    }


def _generate_competition_improvement_areas(
    performance_data: Dict[str, float],
    category_analysis: Dict[str, Any]
) -> List[str]:
    """Generate improvement areas for a competition"""

    improvement_areas = []

    # Performance-based improvements
    percentage_score = performance_data.get('percentage_score', 0)
    if percentage_score < 60:
        improvement_areas.append("Overall performance needs significant improvement")
    elif percentage_score < 80:
        improvement_areas.append("Focus on strengthening fundamental concepts")

    # Category-based improvements
    weak_categories = category_analysis.get('weakest_categories', [])
    if weak_categories:
        improvement_areas.append(f"Improve performance in: {', '.join(weak_categories)}")

    return improvement_areas


def _determine_competition_type(competition_name: str) -> str:
    """Determine competition type from name"""

    name_lower = competition_name.lower()

    if any(word in name_lower for word in ['math', 'mathematics', 'algebra', 'geometry']):
        return "Mathematics"
    elif any(word in name_lower for word in ['science', 'physics', 'chemistry', 'biology']):
        return "Science"
    elif any(word in name_lower for word in ['english', 'literature', 'writing', 'essay']):
        return "English"
    elif any(word in name_lower for word in ['quiz', 'general', 'knowledge', 'trivia']):
        return "General Knowledge"
    elif any(word in name_lower for word in ['coding', 'programming', 'computer', 'tech']):
        return "Technology"
    else:
        return "General"


def _generate_competition_summary(
    competition_performances: List[CompetitionPerformanceDetail],
    time_range: AnalyticsTimeRange
) -> CompetitionAnalyticsSummary:
    """Generate summary analytics for all competitions"""

    if not competition_performances:
        return CompetitionAnalyticsSummary()

    # Filter completed competitions
    completed_competitions = [
        comp for comp in competition_performances
        if comp.participation_status in ["completed", "participated"]
    ]

    # Calculate basic statistics
    total_participated = len(competition_performances)
    total_completed = len(completed_competitions)

    scores = [comp.percentage_score for comp in completed_competitions if comp.percentage_score > 0]
    average_score = sum(scores) / len(scores) if scores else 0.0
    best_score = max(scores) if scores else 0.0
    worst_score = min(scores) if scores else 0.0

    # Calculate ranking statistics
    ranks = [comp.rank for comp in completed_competitions if comp.rank is not None]
    average_rank = sum(ranks) / len(ranks) if ranks else None
    best_rank = min(ranks) if ranks else None

    # Count achievements
    top_10_finishes = len([comp for comp in completed_competitions if comp.rank and comp.rank <= 10])
    top_25_percent_finishes = len([comp for comp in completed_competitions if comp.percentile and comp.percentile >= 75])

    # Count awards and certificates
    total_awards = sum(len(comp.awards_received) for comp in completed_competitions)
    total_certificates = sum(len(comp.certificates_earned) for comp in completed_competitions)
    achievement_rate = (total_awards / total_completed * 100) if total_completed > 0 else 0.0

    # Calculate performance trends
    trends = _calculate_competition_trends(completed_competitions, time_range)
    improvement_rate = _calculate_competition_improvement_rate(trends)

    # Analyze category expertise
    category_analysis = _analyze_competition_categories(completed_competitions)

    return CompetitionAnalyticsSummary(
        total_competitions_participated=total_participated,
        total_competitions_completed=total_completed,
        average_score=average_score,
        best_performance_score=best_score,
        worst_performance_score=worst_score,
        average_rank=average_rank,
        best_rank=best_rank,
        top_10_finishes=top_10_finishes,
        top_25_percent_finishes=top_25_percent_finishes,
        total_awards=total_awards,
        total_certificates=total_certificates,
        achievement_rate=achievement_rate,
        performance_trend=trends,
        improvement_rate=improvement_rate,
        strongest_competition_categories=category_analysis['strongest'],
        preferred_competition_types=category_analysis['preferred']
    )


def _calculate_competition_trends(
    competitions: List[CompetitionPerformanceDetail],
    time_range: AnalyticsTimeRange
) -> List[PerformanceTrend]:
    """Calculate performance trends over time"""

    # Sort competitions by date
    sorted_competitions = sorted(competitions, key=lambda x: x.event_date)

    if len(sorted_competitions) < 2:
        return []

    # Group by time periods (monthly for now)
    monthly_scores = {}
    for comp in sorted_competitions:
        month_key = comp.event_date.strftime("%Y-%m")
        if month_key not in monthly_scores:
            monthly_scores[month_key] = []
        monthly_scores[month_key].append(comp.percentage_score)

    # Calculate trends
    trends = []
    prev_avg = None

    for month, scores in monthly_scores.items():
        avg_score = sum(scores) / len(scores) if scores else 0
        change_percentage = None
        trend_direction = "stable"

        if prev_avg is not None:
            change_percentage = ((avg_score - prev_avg) / prev_avg * 100) if prev_avg > 0 else 0
            if change_percentage > 5:
                trend_direction = "improving"
            elif change_percentage < -5:
                trend_direction = "declining"

        trends.append(PerformanceTrend(
            period=month,
            value=avg_score,
            change_percentage=change_percentage,
            trend_direction=trend_direction
        ))

        prev_avg = avg_score

    return trends


def _calculate_competition_improvement_rate(trends: List[PerformanceTrend]) -> float:
    """Calculate overall improvement rate from trends"""

    if len(trends) < 2:
        return 0.0

    # Calculate average change percentage
    changes = [trend.change_percentage for trend in trends if trend.change_percentage is not None]
    return sum(changes) / len(changes) if changes else 0.0


def _analyze_competition_categories(
    competitions: List[CompetitionPerformanceDetail]
) -> Dict[str, List[str]]:
    """Analyze performance by competition categories"""

    category_scores = {}
    category_counts = {}

    for comp in competitions:
        comp_type = comp.competition_type
        if comp_type not in category_scores:
            category_scores[comp_type] = []
            category_counts[comp_type] = 0

        category_scores[comp_type].append(comp.percentage_score)
        category_counts[comp_type] += 1

    # Calculate average scores by category
    category_averages = {}
    for category, scores in category_scores.items():
        category_averages[category] = sum(scores) / len(scores) if scores else 0

    # Sort by performance
    sorted_categories = sorted(category_averages.items(), key=lambda x: x[1], reverse=True)

    # Get strongest categories (top 3)
    strongest = [cat for cat, score in sorted_categories[:3]]

    # Get preferred categories (most participated)
    sorted_by_count = sorted(category_counts.items(), key=lambda x: x[1], reverse=True)
    preferred = [cat for cat, count in sorted_by_count[:3]]

    return {
        'strongest': strongest,
        'preferred': preferred
    }


def _get_upcoming_competitions(
    db: Session,
    student_id: uuid.UUID,
    competition_types: Optional[List[str]] = None
) -> List[Dict[str, Any]]:
    """Get upcoming competitions for the student"""

    current_time = datetime.now(timezone.utc)

    query = db.query(Event).filter(
        Event.category == EventCategoryEnum.COMPETITION,
        Event.start_datetime > current_time,
        Event.registration_end > current_time
    )

    # Filter by competition types if specified
    if competition_types:
        query = query.filter(
            or_(*[Event.title.ilike(f"%{comp_type}%") for comp_type in competition_types])
        )

    upcoming = query.limit(10).all()

    return [
        {
            'competition_id': str(comp.id),
            'name': comp.title,
            'type': _determine_competition_type(comp.title),
            'start_date': comp.start_datetime.isoformat(),
            'registration_deadline': comp.registration_end.isoformat(),
            'description': comp.description or ""
        }
        for comp in upcoming
    ]


def _get_recommended_competitions(
    db: Session,
    student_id: uuid.UUID,
    past_performances: List[CompetitionPerformanceDetail],
    competition_types: Optional[List[str]] = None
) -> List[Dict[str, Any]]:
    """Get recommended competitions based on past performance"""

    # Analyze student's strengths
    if past_performances:
        category_analysis = _analyze_competition_categories(past_performances)
        strong_categories = category_analysis['strongest']
    else:
        strong_categories = []

    current_time = datetime.now(timezone.utc)

    # Get upcoming competitions
    query = db.query(Event).filter(
        Event.category == EventCategoryEnum.COMPETITION,
        Event.start_datetime > current_time,
        Event.registration_end > current_time
    )

    upcoming = query.limit(20).all()

    recommendations = []
    for comp in upcoming:
        comp_type = _determine_competition_type(comp.title)

        # Calculate recommendation score
        score = 50  # Base score

        if comp_type in strong_categories:
            score += 30  # Boost for strong categories

        if competition_types and comp_type in competition_types:
            score += 20  # Boost for preferred types

        recommendations.append({
            'competition_id': str(comp.id),
            'name': comp.title,
            'type': comp_type,
            'start_date': comp.start_datetime.isoformat(),
            'registration_deadline': comp.registration_end.isoformat(),
            'recommendation_score': score,
            'reason': f"Recommended based on your strength in {comp_type}" if comp_type in strong_categories else "General recommendation"
        })

    # Sort by recommendation score
    recommendations.sort(key=lambda x: x['recommendation_score'], reverse=True)

    return recommendations[:5]  # Return top 5 recommendations
