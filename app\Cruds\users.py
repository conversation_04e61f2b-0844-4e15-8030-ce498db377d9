from uuid import uuid4
from sqlalchemy import or_
from sqlalchemy.orm import Session
from Models.users import User, CNIC, Passport, SponsorProfile, InstituteProfile, UserFollow, UserTypeEnum
from Schemas.users import User<PERSON><PERSON>, UserOut
from fastapi import HTT<PERSON>Exception
from config.security import get_password_hash, verify_password, create_access_token
from Schemas.Token import Token
import re
from config.config import settings
from cryptography.fernet import <PERSON><PERSON><PERSON>
from sqlalchemy.exc import SQLAlchemyError
import hashlib

# Regex patterns
CNIC_REGEX = r"^\d{13}$"
PASSPORT_REGEX = r"^[A-Z0-9]{6,}$"

# Initialize encryption
fernet = Fernet(settings.FERNET_KEY)

# === Helper Functions ===
def hash_string(data: str) -> str:
    return hashlib.sha256(data.encode()).hexdigest()

def user_has_matching_cnic_fast(db: Session, cnic_to_check: str) -> bool:
    cnic_hash = hash_string(cnic_to_check)
    return db.query(CNIC).filter(CNIC.cnic_hash == cnic_hash).first() is not None

def user_has_matching_passport_fast(db: Session, passport_to_check: str) -> bool:
    passport_hash = hash_string(passport_to_check)
    return db.query(Passport).filter(Passport.passport_hash == passport_hash).first() is not None

def set_cnic(db: Session, user_id: str, encrypted_cnic: str) -> None:
    if user_has_matching_cnic_fast(db, encrypted_cnic):
        raise HTTPException(status_code=400, detail="CNIC already in use.")

    encrypted_value = fernet.encrypt(encrypted_cnic.encode()).decode()
    cnic_hash = hash_string(encrypted_cnic)

    cnic = CNIC(
        user_id=user_id,
        encrypted_cnic=encrypted_value,
        cnic_hash=cnic_hash
    )
    db.add(cnic)

def set_passport(db: Session, user_id: str, encrypted_passport: str) -> None:
    if user_has_matching_passport_fast(db, encrypted_passport):
        raise HTTPException(status_code=400, detail="Passport already in use.")

    encrypted_value = fernet.encrypt(encrypted_passport.encode()).decode()
    passport_hash = hash_string(encrypted_passport)

    passport = Passport(
        user_id=user_id,
        encrypted_passport=encrypted_value,
        passport_hash=passport_hash
    )
    db.add(passport)

# === User CRUD Functions ===
def create_user(db: Session, user_create: UserCreate) -> UserOut:
    try:
        with db.begin():
            existing_user = db.query(User).filter(
                (User.username == user_create.username) |
                (User.email == user_create.email) |
                (User.mobile == user_create.mobile)
            ).first()

            if existing_user:
                raise HTTPException(status_code=400, detail="User with this username, email, or mobile already exists.")

            # Validate user type using enum (exclude admin from user creation)
            valid_user_types = [e.value for e in UserTypeEnum if e.value != "admin"]
            if user_create.user_type not in valid_user_types:
                raise HTTPException(status_code=400, detail=f"Invalid user type. Valid types are: {', '.join(valid_user_types)}")

            if user_create.cnic and not re.fullmatch(CNIC_REGEX, user_create.cnic):
                raise HTTPException(status_code=400, detail="Invalid CNIC format. Must be 13 digits.")

            if user_create.passport and not re.fullmatch(PASSPORT_REGEX, user_create.passport):
                raise HTTPException(status_code=400, detail="Invalid Passport format. Must be at least 6 alphanumeric characters.")

            if user_create.country == "Israel":
                raise HTTPException(status_code=400, detail="User creation from Israel is not allowed due to policy restrictions.")

            if user_create.cnic and user_create.passport:
                raise HTTPException(status_code=400, detail="Provide either CNIC or Passport, not both.")
            if not user_create.cnic and not user_create.passport:
                raise HTTPException(status_code=400, detail="Provide at least CNIC or Passport.")

            if user_create.cnic and user_has_matching_cnic_fast(db, user_create.cnic):
                raise HTTPException(status_code=400, detail="CNIC already in use.")
            if user_create.passport and user_has_matching_passport_fast(db, user_create.passport):
                raise HTTPException(status_code=400, detail="Passport already in use.")

            hashed_pwd = get_password_hash(user_create.password)

            new_user = User(
                id=uuid4(),
                username=user_create.username,
                email=user_create.email,
                mobile=user_create.mobile,
                password_hash=hashed_pwd,
                country=user_create.country,
                user_type=user_create.user_type,
                is_email_verified=False,
                is_mobile_verified=False
            )

            db.add(new_user)
            db.flush()  # Assign new_user.id

            if user_create.cnic:
                set_cnic(db, user_id=new_user.id, encrypted_cnic=user_create.cnic)
            if user_create.passport:
                set_passport(db, user_id=new_user.id, encrypted_passport=user_create.passport)

            # Auto-assign basic subscription
            try:
                from Cruds.Subscriptions import auto_assign_basic_subscription
                auto_assign_basic_subscription(db, new_user.id, user_create.user_type)
            except Exception as e:
                # Log the error but don't fail user creation
                print(f"Failed to assign basic subscription to user {new_user.id}: {e}")

            db.refresh(new_user)
            return UserOut.model_validate(new_user)

    except HTTPException:
        raise
    except SQLAlchemyError as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Database error occurred : {e}")
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Unexpected error occurred : {e}")

def get_all_users(db: Session) -> list[UserOut]:
    users = db.query(User).all()
    return [UserOut.model_validate(user) for user in users] if users else []

def get_all_followable_users(db: Session, user_id: str, skip: int = 0, limit: int = 100) -> list[UserOut]:
    # Subquery: users that the current user already follows
    subquery = db.query(UserFollow.following_id).filter(UserFollow.follower_id == user_id)

    # Main query: get all students/teachers, not the current user, and not already followed
    users = (
        db.query(User)
        .filter(
            User.id != user_id,
            or_(User.user_type == "student", User.user_type == "teacher"),
            ~User.id.in_(subquery)
        )
        .offset(skip)
        .limit(limit)
        .all()
    )

    return [UserOut.model_validate(user) for user in users] if users else []

def get_user_by_id(db: Session, user_id: str) -> UserOut:
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found.")
    return UserOut.from_orm(user)

def verify_email(db: Session, user_id: str) -> UserOut:
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found.")
    user.is_email_verified = True
    db.commit()
    db.refresh(user)
    return UserOut.from_orm(user)

def verify_phone(db: Session, user_id: str) -> UserOut:
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found.")
    user.is_mobile_verified = True
    db.commit()
    db.refresh(user)
    return UserOut.from_orm(user)

def update_password(db: Session, user_id: str, new_password: str) -> UserOut:
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found.")
    user.password_hash = get_password_hash(new_password)
    db.commit()
    db.refresh(user)
    return UserOut.from_orm(user)

def Set_Sponsor_Profile(db: Session, user_id: str, bio: str) -> UserOut:
    user = db.query(User).filter(User.id == user_id).first()
    if not user or user.user_type != "sponsor":
        raise HTTPException(status_code=400, detail="Invalid sponsor user.")
    sponsor_profile = SponsorProfile(user_id=user.id, bio=bio)
    db.add(sponsor_profile)
    db.commit()
    db.refresh(sponsor_profile)
    return UserOut.from_orm(user)

def Set_Institute_Profile(db: Session, user_id: str, institute_name: str, bio: str) -> UserOut:
    user = db.query(User).filter(User.id == user_id).first()
    if not user or user.user_type != "institute":
        raise HTTPException(status_code=400, detail="Invalid institute user.")
    institute_profile = InstituteProfile(user_id=user.id, institute_name=institute_name, bio=bio)
    db.add(institute_profile)
    db.commit()
    db.refresh(institute_profile)
    return UserOut.from_orm(user)

def get_user_by_username(db: Session, username: str) -> UserOut:
    user = db.query(User).filter(User.username == username).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found.")
    return UserOut.from_orm(user)

def get_user_by_email(db: Session, email: str) -> UserOut:
    user = db.query(User).filter(User.email == email).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found.")
    return UserOut.from_orm(user)

def get_user_by_mobile(db: Session, mobile: str) -> UserOut:
    user = db.query(User).filter(User.mobile == mobile).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found.")
    return UserOut.from_orm(user)

def delete_user(db: Session, user_id: str) -> dict:
    """
    Delete a user and all associated data.

    This function handles cascade deletion of all user-related data including:
    - User profiles (mentor, teacher, institute, sponsor)
    - Authentication data (CNIC, Passport, verification tokens)
    - Educational data (tasks, exams, submissions, classrooms)
    - File uploads and attachments
    - Subscriptions and associations

    Args:
        db (Session): Database session
        user_id (str): UUID of the user to delete

    Returns:
        dict: Summary of deletion including user info and cleanup stats

    Raises:
        HTTPException: 404 if user not found
    """
    import logging
    from datetime import datetime, timezone

    logger = logging.getLogger(__name__)

    # Get user with all relationships loaded
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found.")

    # Store user info for response
    user_info = {
        "id": str(user.id),
        "username": user.username,
        "email": user.email,
        "user_type": user.user_type.value,
        "created_at": user.created_at.isoformat() if user.created_at else None
    }

    logger.info(f"Starting deletion of user {user.username} ({user.id}) of type {user.user_type.value}")

    try:
        # The cascade="all, delete-orphan" relationships in the User model
        # will automatically handle deletion of:
        # - sponsor_profile, institute_profile, mentor_profile
        # - cnic, passport, verification_tokens
        # - tasks, task_classrooms, task_attachments, student_task_attachments
        # - teacher_profile, subscription

        # Delete the user (cascades will handle related data)
        db.delete(user)
        db.commit()

        logger.info(f"Successfully deleted user {user_info['username']} ({user_info['id']})")

        return {
            "message": "User deleted successfully",
            "user": user_info,
            "deleted_at": datetime.now(timezone.utc).isoformat(),
            "note": "All associated data including profiles, tasks, and files have been removed"
        }

    except Exception as e:
        db.rollback()
        logger.error(f"Failed to delete user {user_info['username']} ({user_info['id']}): {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to delete user: {str(e)}"
        )

def signin(db: Session, email: str, password: str) -> dict:
    try:
        user = db.query(User).filter(User.email == email).first()
        if not user:
            raise HTTPException(status_code=404, detail="User not found.")
        
        if not verify_password(password, user.password_hash):
            raise HTTPException(status_code=400, detail="Incorrect password.")
        
        # Access the enum value properly
        user_type_value = user.user_type.value if hasattr(user.user_type, 'value') else str(user.user_type)
        
        token = {
            "access_token": create_access_token(data={"sub": user.email}),
            "token_type": "bearer",
            "role": user_type_value
        }
        return token
    except HTTPException:
        raise
    except Exception as e:
        print(f"Error in signin: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


def upload_profile_picture(db: Session, user_id: str, profile_pic_url: str) -> UserOut:
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found.")
    user.profile_picture = profile_pic_url
    db.commit()
    db.refresh(user)
    return UserOut.from_orm(user)

def update_profile_picture(db: Session, user_id: str, profile_pic_url: str) -> UserOut:
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found.")
    user.profile_picture = profile_pic_url
    db.commit()
    db.refresh(user)
    return UserOut.from_orm(user)


def get_all_students(db: Session) -> list[UserOut]:
    students = db.query(User).filter(User.user_type == UserTypeEnum.student).all()
    return [UserOut.model_validate(student) for student in students] if students else []

def get_all_sponsors(db: Session) -> list[UserOut]:
    sponsors = db.query(User).filter(User.user_type == UserTypeEnum.sponsor).all()
    return [UserOut.model_validate(sponsor) for sponsor in sponsors] if sponsors else []

def get_all_institutes(db: Session) -> list[UserOut]:
    institutes = db.query(User).filter(User.user_type == UserTypeEnum.institute).all()
    return [UserOut.model_validate(institute) for institute in institutes] if institutes else []

def get_all_teachers(db: Session) -> list[UserOut]:
    teachers = db.query(User).filter(User.user_type == UserTypeEnum.teacher).all()
    return [UserOut.from_orm(teacher) for teacher in teachers] if teachers else []
