"""
Ticket Service for EduFair Platform

This service handles ticket generation, QR code creation, and ticket verification.
"""

import json
import uuid
from datetime import datetime, timezone
from typing import Optional, Dict, Any, Tuple
from sqlalchemy.orm import Session
from fastapi import HTTPException, status

from Models.Events import EventRegistration, Event, EventTicket
from Models.users import User


class TicketService:
    """Service for managing digital tickets"""
    
    def generate_check_in_code(self) -> str:
        """Generate alternative check-in code for manual verification"""
        return f"CHK-{uuid.uuid4().hex[:8].upper()}"
    
    def update_registration_codes(self, db: Session, registration: EventRegistration) -> EventRegistration:
        """
        Update registration with check-in code

        Args:
            db: Database session
            registration: Event registration object

        Returns:
            EventRegistration: Updated registration object
        """
        try:
            # Generate check-in code
            check_in_code = self.generate_check_in_code()

            # Update registration
            registration.check_in_code = check_in_code

            db.commit()
            db.refresh(registration)

            return registration

        except Exception as e:
            db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to update registration codes: {str(e)}"
            )
    
    def verify_ticket_by_registration_number(self, db: Session, registration_number: str) -> Tuple[bool, str, Optional[EventRegistration]]:
        """
        Verify ticket using registration number

        Args:
            db: Database session
            registration_number: Registration number string

        Returns:
            Tuple[bool, str, Optional[EventRegistration]]: (is_valid, message, registration)
        """
        try:
            # Find registration by registration number
            registration = db.query(EventRegistration).filter(
                EventRegistration.registration_number == registration_number
            ).first()

            if not registration:
                return False, "Registration not found", None

            # Check registration status
            if registration.status.value == "CANCELLED":
                return False, "Ticket has been cancelled", None

            if registration.status.value != "CONFIRMED":
                return False, "Ticket is not confirmed", None

            # Check if already attended
            if registration.attended_at:
                return False, "Ticket already used for check-in", registration

            return True, "Valid ticket", registration

        except Exception as e:
            return False, f"Verification error: {str(e)}", None
    
    def verify_check_in_code(self, db: Session, check_in_code: str) -> Tuple[bool, str, Optional[EventRegistration]]:
        """
        Verify ticket using manual check-in code
        
        Args:
            db: Database session
            check_in_code: Manual check-in code
            
        Returns:
            Tuple[bool, str, Optional[EventRegistration]]: (is_valid, message, registration)
        """
        try:
            # Find registration by check-in code
            registration = db.query(EventRegistration).filter(
                EventRegistration.check_in_code == check_in_code
            ).first()
            
            if not registration:
                return False, "Invalid check-in code", None
            
            # Check registration status
            if registration.status.value == "CANCELLED":
                return False, "Ticket has been cancelled", None
            
            if registration.status.value != "CONFIRMED":
                return False, "Ticket is not confirmed", None
            
            # Check if already attended
            if registration.attended_at:
                return False, "Ticket already used for check-in", registration
            
            return True, "Valid ticket", registration
            
        except Exception as e:
            return False, f"Verification error: {str(e)}", None
    
    def mark_attendance(self, db: Session, registration: EventRegistration) -> EventRegistration:
        """
        Mark registration as attended
        
        Args:
            db: Database session
            registration: Event registration object
            
        Returns:
            EventRegistration: Updated registration object
        """
        try:
            registration.attended_at = datetime.now(timezone.utc)
            registration.status = "ATTENDED"  # Update status to attended
            
            db.commit()
            db.refresh(registration)
            
            return registration
            
        except Exception as e:
            db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to mark attendance: {str(e)}"
            )
    
    def get_ticket_data(self, db: Session, registration_id: str, user_id: str) -> Dict[str, Any]:
        """
        Get complete ticket data for display/download
        
        Args:
            db: Database session
            registration_id: Registration ID
            user_id: User ID for verification
            
        Returns:
            Dict[str, Any]: Complete ticket data
        """
        try:
            # Get registration with related data
            registration = db.query(EventRegistration).filter(
                EventRegistration.id == registration_id,
                EventRegistration.user_id == user_id
            ).first()
            
            if not registration:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Registration not found"
                )
            
            # Get related event and ticket data
            event = db.query(Event).filter(Event.id == registration.event_id).first()
            ticket = None
            if registration.ticket_id:
                ticket = db.query(EventTicket).filter(EventTicket.id == registration.ticket_id).first()
            
            # Get user data
            user = db.query(User).filter(User.id == user_id).first()
            
            # Prepare ticket data
            ticket_data = {
                "registration": {
                    "id": str(registration.id),
                    "registration_number": registration.registration_number,
                    "status": registration.status.value,
                    "quantity": registration.quantity,
                    "total_amount": float(registration.total_amount),
                    "currency": registration.currency,
                    "registered_at": registration.registered_at.isoformat(),
                    "confirmed_at": registration.confirmed_at.isoformat() if registration.confirmed_at else None,
                    "check_in_code": registration.check_in_code
                },
                "event": {
                    "id": str(event.id),
                    "title": event.title,
                    "description": event.description,
                    "start_datetime": event.start_datetime.isoformat(),
                    "end_datetime": event.end_datetime.isoformat() if event.end_datetime else None,
                    "location": event.location,
                    "category": event.category.value if event.category else None
                },
                "ticket": {
                    "id": str(ticket.id) if ticket else None,
                    "name": ticket.name if ticket else "Free Event",
                    "description": ticket.description if ticket else None,
                    "price": float(ticket.price) if ticket else 0.0,
                    "currency": ticket.currency if ticket else registration.currency
                } if ticket else None,
                "user": {
                    "id": str(user.id),
                    "username": user.username,
                    "email": user.email,
                    "display_name": user.username  # Use username as display name
                },
                "generated_at": datetime.now(timezone.utc).isoformat()
            }
            
            return ticket_data
            
        except HTTPException:
            raise
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to get ticket data: {str(e)}"
            )


# Create global ticket service instance
ticket_service = TicketService()
