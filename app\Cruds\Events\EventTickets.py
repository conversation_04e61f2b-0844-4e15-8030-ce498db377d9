"""
Event Tickets CRUD Operations for EduFair Platform

This module contains all CRUD operations for Event Ticket management including:
- Ticket creation, reading, updating, and deletion
- Ticket availability checking
- Ticket sales tracking
"""

import uuid
from datetime import datetime, timezone
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, func
from fastapi import HTTPException, status
from typing import List, Optional
from uuid import UUID

# Import Models
from Models.Events import (
    Event, EventTicket, EventRegistration, 
    TicketStatusEnum, RegistrationStatusEnum
)
from Models.users import User, UserTypeEnum

# Import Schemas
from Schemas.Events.Events import (
    EventTicketCreate, EventTicketUpdate, EventTicketOut
)


# ==================== EVENT TICKET CRUD OPERATIONS ====================

def create_event_ticket(db: Session, ticket_data: EventTicketCreate, user_id: UUID) -> EventTicketOut:
    """Create a new event ticket."""
    # Verify event exists and user is authorized
    event = db.query(Event).filter(Event.id == ticket_data.event_id).first()
    if not event:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Event not found"
        )
    
    # Check if user is authorized (event organizer)
    if event.organizer_id != user_id and event.institute_id != user_id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to create tickets for this event"
        )
    
    try:
        ticket_dict = ticket_data.model_dump()
        
        db_ticket = EventTicket(**ticket_dict)
        db.add(db_ticket)
        db.commit()
        db.refresh(db_ticket)
        
        return db_ticket
        
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create ticket: {str(e)}"
        )


def get_event_ticket_by_id(db: Session, ticket_id: UUID) -> EventTicketOut:
    """Get event ticket by ID."""
    ticket = db.query(EventTicket).options(
        joinedload(EventTicket.event)
    ).filter(EventTicket.id == ticket_id).first()
    
    if not ticket:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Event ticket not found"
        )
    
    return ticket


def get_tickets_by_event(db: Session, event_id: UUID) -> List[EventTicketOut]:
    """Get all tickets for a specific event."""
    # Verify event exists
    event = db.query(Event).filter(Event.id == event_id).first()
    if not event:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Event not found"
        )
    
    tickets = db.query(EventTicket).filter(
        EventTicket.event_id == event_id
    ).order_by(EventTicket.price).all()
    
    # Add sold quantity to each ticket
    for ticket in tickets:
        sold_quantity = db.query(func.sum(EventRegistration.quantity)).filter(
            EventRegistration.ticket_id == ticket.id,
            EventRegistration.status.in_([RegistrationStatusEnum.CONFIRMED, RegistrationStatusEnum.ATTENDED])
        ).scalar() or 0
        ticket.sold_quantity = sold_quantity
    
    return tickets


def update_event_ticket(db: Session, ticket_id: UUID, ticket_data: EventTicketUpdate, user_id: UUID) -> EventTicketOut:
    """Update an existing event ticket."""
    ticket = db.query(EventTicket).options(
        joinedload(EventTicket.event)
    ).filter(EventTicket.id == ticket_id).first()
    
    if not ticket:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Event ticket not found"
        )
    
    # Check if user is authorized
    if ticket.event.organizer_id != user_id and ticket.event.institute_id != user_id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to update this ticket"
        )
    
    try:
        update_data = ticket_data.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(ticket, field, value)
        
        ticket.updated_at = datetime.now(timezone.utc)
        db.commit()
        db.refresh(ticket)
        
        return ticket
        
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update ticket: {str(e)}"
        )


def delete_event_ticket(db: Session, ticket_id: UUID, user_id: UUID) -> None:
    """Delete an event ticket."""
    ticket = db.query(EventTicket).options(
        joinedload(EventTicket.event)
    ).filter(EventTicket.id == ticket_id).first()
    
    if not ticket:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Event ticket not found"
        )
    
    # Check if user is authorized
    if ticket.event.organizer_id != user_id and ticket.event.institute_id != user_id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to delete this ticket"
        )
    
    # Check if ticket has any registrations
    registrations_count = db.query(EventRegistration).filter(
        EventRegistration.ticket_id == ticket_id
    ).count()
    
    if registrations_count > 0:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Cannot delete ticket with existing registrations"
        )
    
    try:
        db.delete(ticket)
        db.commit()
        
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete ticket: {str(e)}"
        )


def check_ticket_availability(db: Session, ticket_id: UUID, quantity: int = 1) -> bool:
    """Check if ticket is available for purchase."""
    ticket = db.query(EventTicket).filter(EventTicket.id == ticket_id).first()
    
    if not ticket:
        return False
    
    # Check ticket status
    if ticket.status != TicketStatusEnum.ACTIVE:
        return False
    
    # Check sale period
    now = datetime.now(timezone.utc)
    if ticket.sale_start and now < ticket.sale_start:
        return False
    if ticket.sale_end and now > ticket.sale_end:
        return False
    
    # Check quantity availability
    if ticket.total_quantity:
        sold_quantity = db.query(func.sum(EventRegistration.quantity)).filter(
            EventRegistration.ticket_id == ticket_id,
            EventRegistration.status.in_([RegistrationStatusEnum.CONFIRMED, RegistrationStatusEnum.ATTENDED])
        ).scalar() or 0
        
        available_quantity = ticket.total_quantity - sold_quantity
        if available_quantity < quantity:
            return False
    
    return True


def get_ticket_sales_summary(db: Session, event_id: UUID, user_id: UUID) -> dict:
    """Get ticket sales summary for an event."""
    # Verify event exists and user is authorized
    event = db.query(Event).filter(Event.id == event_id).first()
    if not event:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Event not found"
        )
    
    if event.organizer_id != user_id and event.institute_id != user_id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to view sales data for this event"
        )
    
    # Get ticket sales data
    tickets = db.query(EventTicket).filter(EventTicket.event_id == event_id).all()
    
    summary = {
        "total_tickets": 0,
        "sold_tickets": 0,
        "revenue": 0,
        "tickets": []
    }
    
    for ticket in tickets:
        sold_quantity = db.query(func.sum(EventRegistration.quantity)).filter(
            EventRegistration.ticket_id == ticket.id,
            EventRegistration.status.in_([RegistrationStatusEnum.CONFIRMED, RegistrationStatusEnum.ATTENDED])
        ).scalar() or 0
        
        revenue = db.query(func.sum(EventRegistration.total_amount)).filter(
            EventRegistration.ticket_id == ticket.id,
            EventRegistration.status.in_([RegistrationStatusEnum.CONFIRMED, RegistrationStatusEnum.ATTENDED])
        ).scalar() or 0
        
        ticket_summary = {
            "id": ticket.id,
            "name": ticket.name,
            "price": ticket.price,
            "total_quantity": ticket.total_quantity,
            "sold_quantity": sold_quantity,
            "revenue": revenue,
            "available_quantity": (ticket.total_quantity - sold_quantity) if ticket.total_quantity else None
        }
        
        summary["tickets"].append(ticket_summary)
        summary["total_tickets"] += ticket.total_quantity or 0
        summary["sold_tickets"] += sold_quantity
        summary["revenue"] += revenue
    
    return summary
