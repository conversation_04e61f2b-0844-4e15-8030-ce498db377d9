"""
Migration to add social features tables.

This migration adds the following tables:
- user_follows: For user following relationships
- chat_messages: For user-to-user messaging
- user_notifications: For notification system
- admin_logs: For comprehensive admin logging

Run this migration to add the social features to an existing database.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import text
from config.session import engine, get_db
from Models.users import UserFollow, ChatMessage, UserNotification, AdminLog
from Models.baseModel import Base


def create_social_tables():
    """Create the social features tables."""
    
    print("Creating social features tables...")
    
    try:
        # Import all models to ensure they're registered
        from Models.users import User, UserFollow, ChatMessage, UserNotification, AdminLog
        
        # Create only the new tables
        tables_to_create = [
            UserFollow.__table__,
            ChatMessage.__table__, 
            UserNotification.__table__,
            AdminLog.__table__
        ]
        
        # Create each table individually
        for table in tables_to_create:
            try:
                table.create(engine, checkfirst=True)
                print(f"✅ Created table: {table.name}")
            except Exception as e:
                if "already exists" in str(e).lower():
                    print(f"⚠️  Table {table.name} already exists - skipping")
                else:
                    print(f"❌ Error creating table {table.name}: {e}")
                    raise
        
        print("🎉 Social features tables created successfully!")
        
    except Exception as e:
        print(f"❌ Error creating social features tables: {e}")
        raise


def verify_tables():
    """Verify that all tables were created successfully."""
    
    print("\nVerifying table creation...")
    
    with engine.connect() as conn:
        # Check each table
        tables_to_check = [
            'user_follows',
            'chat_messages', 
            'user_notifications',
            'admin_logs'
        ]
        
        for table_name in tables_to_check:
            try:
                result = conn.execute(text(f"SELECT 1 FROM {table_name} LIMIT 1"))
                print(f"✅ Table {table_name} exists and is accessible")
            except Exception as e:
                print(f"❌ Table {table_name} check failed: {e}")
                return False
    
    print("🎉 All social features tables verified successfully!")
    return True


if __name__ == "__main__":
    print("🚀 Starting social features migration...")
    
    try:
        create_social_tables()
        verify_tables()
        print("\n✅ Migration completed successfully!")
        print("\nThe following features are now available:")
        print("- User following system")
        print("- Chat messaging between users")
        print("- Comprehensive notification system") 
        print("- Admin logging for all activities")
        
    except Exception as e:
        print(f"\n❌ Migration failed: {e}")
        sys.exit(1)
