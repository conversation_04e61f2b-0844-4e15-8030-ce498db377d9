import uuid
from datetime import datetime, timedelta, timezone
from typing import List, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import func, desc
from fastapi import HTTPException

# Import Models
from Models.users import User, UserTypeEnum
from Models.Classroom import Classroom, StudentClassroom
from Models.Tasks import Task, TaskStudents, TaskStatus
from Models.Exam import Exam, StudentExamAssignment, StudentExamAttempt
from Models.Announcements import Announcement

# Import Schemas
from Schemas.users import UserOut
from Schemas.StudentDashboard import (
    StudentDashboardData, DashboardData, PerformanceMetrics,
    RecentActivity, StudyMetrics, ScheduleItem, QuickAction
)
from Schemas.TeacherModule.Classroom import ClassroomOutForStudent
from Schemas.TeacherModule.tasks import StudentTaskOut
from Schemas.Exams.Exam import ExamStudentMinimalOut

def get_student_dashboard_data(db: Session, student_id: uuid.UUID) -> StudentDashboardData:
    """
    Get comprehensive dashboard data for a student including all academic and activity information.
    """
    # Verify student exists
    student = db.query(User).filter(
        User.id == student_id, 
        User.user_type == UserTypeEnum.student
    ).first()
    
    if not student:
        raise HTTPException(status_code=404, detail="Student not found")
    
    # Get student info
    student_out = UserOut.model_validate(student)
    
    # Get student's classes
    classes_data = _get_student_classes(db, student_id)
    
    # Get upcoming exams
    exams_data = _get_student_exams(db, student_id)
    
    # Get assignments/tasks
    assignments_data = _get_student_assignments(db, student_id)
    
    # Get performance metrics
    performance_data = _get_performance_metrics(db, student_id)

    # Get recent activity
    recent_activity_data = _get_recent_activity(db, student_id)
    
    # Get study metrics (gamification)
    study_metrics_data = _get_study_metrics(db, student_id)
    
    # Get schedule
    schedule_data = _get_student_schedule(db, student_id)
    
    # Get quick actions
    quick_actions_data = _get_quick_actions(db, student_id)
    
    # Get unread notifications count (placeholder)
    unread_count = _get_unread_notifications_count(db, student_id)
    
    dashboard_data = DashboardData(
        student=student_out,
        classes=classes_data,
        exams=exams_data,
        performance=performance_data,
        assignments=assignments_data,

        recent_activity=recent_activity_data,
        study_metrics=study_metrics_data,
        schedule=schedule_data,
        quick_actions=quick_actions_data,
        unread_notifications_count=unread_count,
        last_updated=datetime.now(timezone.utc)
    )
    
    return StudentDashboardData(success=True, data=dashboard_data)

def _get_student_classes(db: Session, student_id: uuid.UUID) -> List[ClassroomOutForStudent]:
    """Get all classes the student is enrolled in."""
    results = (
        db.query(
            Classroom.id,
            Classroom.name,
            User.username.label("teacher_name"),
            Classroom.teacher_id
        )
        .join(StudentClassroom, StudentClassroom.classroom_id == Classroom.id)
        .join(User, Classroom.teacher_id == User.id)
        .filter(StudentClassroom.student_id == student_id)
        .all()
    )
    
    return [
        ClassroomOutForStudent(
            id=row.id,
            name=row.name,
            teacher_name=row.teacher_name,
            teacher_id=row.teacher_id
        )
        for row in results
    ]

def _get_student_exams(db: Session, student_id: uuid.UUID) -> List[ExamStudentMinimalOut]:
    """Get upcoming exams for the student with status information."""
    from Models.Exam import StudentExamAttempt

    now = datetime.now(timezone.utc)
    upcoming_exams = (
        db.query(Exam)
        .join(StudentExamAssignment, StudentExamAssignment.exam_id == Exam.id)
        .filter(
            StudentExamAssignment.student_id == student_id,
            Exam.start_time >= now
        )
        .order_by(Exam.start_time)
        .limit(5)
        .all()
    )

    result = []
    for exam in upcoming_exams:
        end_time = None
        if exam.start_time and exam.total_duration:
            end_time = exam.start_time + timedelta(minutes=exam.total_duration)

        # Get assignment and attempt information for status
        assignment = db.query(StudentExamAssignment).filter(
            StudentExamAssignment.exam_id == exam.id,
            StudentExamAssignment.student_id == student_id
        ).first()

        attempt = db.query(StudentExamAttempt).filter(
            StudentExamAttempt.exam_id == exam.id,
            StudentExamAttempt.student_id == student_id
        ).first()

        # Determine status
        if attempt and hasattr(attempt, 'status') and attempt.status == "disqualified":
            status = "disqualified"
        elif attempt and attempt.completed_at:
            status = "submitted"
        elif attempt and attempt.started_at:
            status = "started"
        elif exam.start_time and now >= exam.start_time:
            status = "ongoing"
        else:
            status = "upcoming"

        result.append(ExamStudentMinimalOut(
            id=exam.id,
            title=exam.title,
            description=exam.description,
            total_marks=exam.total_marks,
            total_duration=exam.total_duration,
            start_time=exam.start_time,
            end_time=end_time,
            status=status
        ))

    return result

def _get_student_assignments(db: Session, student_id: uuid.UUID) -> List[StudentTaskOut]:
    """Get current assignments/tasks for the student."""
    tasks = (
        db.query(Task, TaskStudents)
        .join(TaskStudents, TaskStudents.task_id == Task.id)
        .filter(
            TaskStudents.student_id == student_id,
            Task.status.in_([TaskStatus.PENDING, TaskStatus.IN_PROGRESS])
        )
        .order_by(Task.deadline.asc())
        .limit(10)
        .all()
    )
    
    result = []
    for task, task_student in tasks:
        result.append(StudentTaskOut(
            id=task.id,
            name=task.name,
            description=task.description,
            status=task.status,
            deadline=task.deadline,
            submission_date=task_student.submission_date,
            grade=task_student.grade,
            subject_id=task.subject_id,
            accept_after_deadline=task.accept_after_deadline,
            created_at=task.created_at,
            updated_at=task.updated_at
        ))
    
    return result

def _get_performance_metrics(db: Session, student_id: uuid.UUID) -> PerformanceMetrics:
    """Calculate performance metrics for the student."""
    # Get average grade from completed tasks
    avg_grade_result = (
        db.query(func.avg(TaskStudents.grade))
        .filter(
            TaskStudents.student_id == student_id,
            TaskStudents.grade.isnot(None)
        )
        .scalar()
    )
    
    avg_grade = float(avg_grade_result) if avg_grade_result else 0.0
    
    # Get recent scores (last 5 graded tasks)
    recent_scores_query = (
        db.query(Task.name, TaskStudents.grade, TaskStudents.submission_date)
        .join(TaskStudents, TaskStudents.task_id == Task.id)
        .filter(
            TaskStudents.student_id == student_id,
            TaskStudents.grade.isnot(None)
        )
        .order_by(desc(TaskStudents.submission_date))
        .limit(5)
        .all()
    )
    
    recent_scores = [
        {
            "task_name": score.name,
            "grade": score.grade,
            "date": score.submission_date.isoformat() if score.submission_date else None
        }
        for score in recent_scores_query
    ]
    
    return PerformanceMetrics(
        overall_grade=avg_grade,
        subject_grades={},  # TODO: Implement subject-wise grades
        recent_scores=recent_scores,
        improvement_trend="stable",  # TODO: Calculate trend
        rank_in_class=None  # TODO: Calculate rank
    )



def _get_recent_activity(db: Session, student_id: uuid.UUID) -> List[RecentActivity]:
    """Get recent student activities."""
    activities = []
    
    # Recent task submissions
    recent_submissions = (
        db.query(Task.name, TaskStudents.submission_date)
        .join(TaskStudents, TaskStudents.task_id == Task.id)
        .filter(
            TaskStudents.student_id == student_id,
            TaskStudents.submission_date.isnot(None)
        )
        .order_by(desc(TaskStudents.submission_date))
        .limit(5)
        .all()
    )
    
    for submission in recent_submissions:
        activities.append(RecentActivity(
            id=f"task_submission_{submission.submission_date.timestamp()}",
            type="task_submitted",
            title=f"Submitted: {submission.name}",
            description=f"Task '{submission.name}' was submitted",
            timestamp=submission.submission_date
        ))
    
    # Sort by timestamp and return latest 10
    activities.sort(key=lambda x: x.timestamp, reverse=True)
    return activities[:10]

def _get_study_metrics(db: Session, student_id: uuid.UUID) -> StudyMetrics:
    """Get gamification and study metrics."""
    # Count completed tasks
    completed_tasks = (
        db.query(func.count(TaskStudents.task_id))
        .filter(
            TaskStudents.student_id == student_id,
            TaskStudents.submission_date.isnot(None)
        )
        .scalar()
    ) or 0
    
    # Count exams taken
    exams_taken = (
        db.query(func.count(StudentExamAttempt.id))
        .filter(StudentExamAttempt.student_id == student_id)
        .scalar()
    ) or 0
    
    # Calculate points (simple formula)
    total_points = (completed_tasks * 10) + (exams_taken * 25)
    level = max(1, total_points // 100)
    
    return StudyMetrics(
        total_points=total_points,
        level=level,
        tasks_completed=completed_tasks,
        exams_taken=exams_taken,
        average_grade=0.0,  # TODO: Get from performance metrics
        badges_earned=[]  # TODO: Implement badge system
    )

def _get_student_schedule(db: Session, student_id: uuid.UUID) -> List[ScheduleItem]:
    """Get upcoming schedule items for the student."""
    schedule_items = []
    now = datetime.now(timezone.utc)

    # Upcoming exam deadlines
    upcoming_exams = (
        db.query(Exam)
        .join(StudentExamAssignment, StudentExamAssignment.exam_id == Exam.id)
        .filter(
            StudentExamAssignment.student_id == student_id,
            Exam.start_time >= now
        )
        .order_by(Exam.start_time)
        .limit(5)
        .all()
    )

    for exam in upcoming_exams:
        schedule_items.append(ScheduleItem(
            id=f"exam_{exam.id}",
            title=exam.title,
            type="exam",
            start_time=exam.start_time,
            end_time=exam.start_time + timedelta(minutes=exam.total_duration) if exam.start_time else None,
            subject=None  # TODO: Get subject from exam
        ))

    # Upcoming task deadlines
    upcoming_tasks = (
        db.query(Task)
        .join(TaskStudents, TaskStudents.task_id == Task.id)
        .filter(
            TaskStudents.student_id == student_id,
            Task.deadline >= now,
            TaskStudents.submission_date.is_(None)
        )
        .order_by(Task.deadline)
        .limit(5)
        .all()
    )

    for task in upcoming_tasks:
        schedule_items.append(ScheduleItem(
            id=f"task_{task.id}",
            title=f"Due: {task.name}",
            type="assignment_due",
            start_time=task.deadline,
            subject=None  # TODO: Get subject from task
        ))

    # Upcoming events (registered events only)
    from Models.Events import Event, EventRegistration, EventStatusEnum, RegistrationStatusEnum
    upcoming_events = (
        db.query(Event)
        .join(EventRegistration, EventRegistration.event_id == Event.id)
        .filter(
            EventRegistration.user_id == student_id,
            EventRegistration.status.in_([RegistrationStatusEnum.CONFIRMED, RegistrationStatusEnum.ATTENDED]),
            Event.start_datetime >= now,
            Event.status == EventStatusEnum.PUBLISHED
        )
        .order_by(Event.start_datetime)
        .limit(3)
        .all()
    )

    for event in upcoming_events:
        schedule_items.append(ScheduleItem(
            id=f"event_{event.id}",
            title=event.title,
            type="event",
            start_time=event.start_datetime,
            end_time=event.end_datetime,
            location=event.location.name if event.location else None,
            subject=event.category.name if event.category else None
        ))

    # Sort by start time
    schedule_items.sort(key=lambda x: x.start_time)
    return schedule_items[:10]

def _get_quick_actions(db: Session, student_id: uuid.UUID) -> List[QuickAction]:
    """Get actionable items for the student."""
    actions = []
    now = datetime.now(timezone.utc)
    
    # Overdue tasks
    overdue_tasks = (
        db.query(func.count(Task.id))
        .join(TaskStudents, TaskStudents.task_id == Task.id)
        .filter(
            TaskStudents.student_id == student_id,
            Task.deadline < now,
            TaskStudents.submission_date.is_(None)
        )
        .scalar()
    ) or 0
    
    if overdue_tasks > 0:
        actions.append(QuickAction(
            id="overdue_tasks",
            title="Overdue Assignments",
            description=f"You have {overdue_tasks} overdue assignment(s)",
            action_type="task",
            priority="high",
            url="/tasks/overdue"
        ))
    
    # Upcoming exams (next 7 days)
    upcoming_exams = (
        db.query(func.count(Exam.id))
        .join(StudentExamAssignment, StudentExamAssignment.exam_id == Exam.id)
        .filter(
            StudentExamAssignment.student_id == student_id,
            Exam.start_time.between(now, now + timedelta(days=7))
        )
        .scalar()
    ) or 0
    
    if upcoming_exams > 0:
        actions.append(QuickAction(
            id="upcoming_exams",
            title="Upcoming Exams",
            description=f"You have {upcoming_exams} exam(s) this week",
            action_type="exam",
            priority="high",
            url="/exams/upcoming"
        ))
    
    return actions

def _get_unread_notifications_count(db: Session, student_id: uuid.UUID) -> int:
    """Get count of unread notifications (placeholder)."""
    # TODO: Implement notification system
    return 0
