# Competition Exam API Documentation

## Overview

The Competition Exam API allows students to attempt exams linked to competition events. This system validates that events are competition-type events with linked exams and provides a complete exam attempt workflow.

---

## 🎯 **Key Features**

- ✅ **Event Validation**: Ensures events are valid competitions with linked exams
- ✅ **Registration System**: Students can register for competition exams
- ✅ **Exam Attempts**: Full exam session management for competitions
- ✅ **Real-time Status**: Track attempt status and timing
- ✅ **Results & Leaderboard**: View competition results and rankings
- ✅ **Admin Controls**: Administrative access to comprehensive results

---

## 🔧 **API Endpoints**

### **1. Competition Exam Information**

#### `GET /api/competitions/{event_id}/exam/info`
Get detailed competition exam information for a student.

**Authentication:** Student role required

**Response:**
```json
{
  "event_id": "uuid",
  "event_title": "Mathematics Competition 2024",
  "event_description": "Annual mathematics competition",
  "start_datetime": "2024-01-15T10:00:00Z",
  "end_datetime": "2024-01-15T12:00:00Z",
  "exam_id": "uuid",
  "exam_title": "Math Competition Exam",
  "exam_description": "Comprehensive mathematics exam",
  "total_marks": 100,
  "total_duration": 120,
  "total_questions": 50,
  "competition_rules": "No calculators allowed...",
  "prize_details": {"first": "₹10,000", "second": "₹5,000"},
  "max_attendees": 100,
  "current_participants": 45,
  "is_registered": true,
  "can_attempt": true,
  "attempt_id": "uuid",
  "attempt_status": "registered",
  "registration_open": true,
  "exam_active": false,
  "time_remaining": null
}
```

#### `GET /api/competitions/{event_id}/exam/status`
Get current exam attempt status for the student.

**Authentication:** Student role required

**Response:**
```json
{
  "event_id": "uuid",
  "exam_id": "uuid",
  "attempt_id": "uuid",
  "status": "in_progress",
  "is_registered": true,
  "can_start": false,
  "can_continue": true,
  "started_at": "2024-01-15T10:05:00Z",
  "completed_at": null,
  "time_remaining": 85,
  "score": null
}
```

### **2. Competition Exam Registration**

#### `POST /api/competitions/{event_id}/exam/register`
Register a student for a competition exam.

**Authentication:** Student role required

**Requirements:**
- Competition must exist and be linked to an exam
- Registration must be open
- Competition must not have started
- Student must not already be registered
- Competition must not be full

**Response:**
```json
{
  "success": true,
  "message": "Successfully registered for competition",
  "competition_id": "uuid",
  "attempt_id": "uuid"
}
```

### **3. Competition Exam Attempts**

#### `POST /api/competitions/{event_id}/exam/start`
Start a competition exam attempt.

**Authentication:** Student role required

**Requirements:**
- Student must be registered
- Competition must be active
- Student must not have completed the exam

**Response:**
```json
{
  "success": true,
  "message": "Competition exam attempt started successfully",
  "attempt_id": "uuid",
  "session_id": "session-uuid",
  "exam_info": {
    "event_id": "uuid",
    "event_title": "Mathematics Competition 2024",
    "exam_id": "uuid",
    "exam_title": "Math Competition Exam",
    "total_marks": 100,
    "total_duration": 120,
    "start_time": "2024-01-15T10:00:00Z",
    "end_time": "2024-01-15T12:00:00Z",
    "competition_rules": "No calculators allowed...",
    "prize_details": {"first": "₹10,000"}
  }
}
```

#### `GET /api/competitions/{event_id}/exam/questions`
Get exam questions for active attempt.

**Authentication:** Student role required

**Requirements:**
- Student must have an active exam attempt
- Exam session must be active

**Response:**
```json
{
  "exam_info": {
    "event_id": "uuid",
    "event_title": "Mathematics Competition 2024",
    "exam_id": "uuid",
    "exam_title": "Math Competition Exam",
    "total_marks": 100,
    "total_duration": 120
  },
  "questions": [
    {
      "id": "uuid",
      "question_text": "What is 2 + 2?",
      "question_type": "mcq",
      "marks": 2,
      "options": [
        {"id": "uuid", "option_text": "3", "option_label": "A"},
        {"id": "uuid", "option_text": "4", "option_label": "B"},
        {"id": "uuid", "option_text": "5", "option_label": "C"}
      ]
    }
  ],
  "attempt_id": "uuid",
  "session_id": "session-uuid",
  "time_remaining": 85
}
```

### **4. Competition Results**

#### `GET /api/competitions/{event_id}/results?limit=10`
Get competition results and leaderboard.

**Authentication:** Student, Teacher, or Institute role required

**Query Parameters:**
- `limit`: Number of top results (1-100, default: 10)

**Response:**
```json
{
  "event_id": "uuid",
  "event_title": "Mathematics Competition 2024",
  "total_participants": 45,
  "completed_attempts": 42,
  "average_score": 75.5,
  "highest_score": 98.0,
  "leaderboard": [
    {
      "rank": 1,
      "student_id": "uuid",
      "student_name": "john_doe",
      "score": 98.0,
      "total_marks": 100,
      "percentage": 98.0,
      "time_taken": 95,
      "completed_at": "2024-01-15T11:35:00Z"
    }
  ],
  "student_result": {
    "rank": 5,
    "student_id": "uuid",
    "student_name": "current_student",
    "score": 85.0,
    "total_marks": 100,
    "percentage": 85.0,
    "time_taken": 110,
    "completed_at": "2024-01-15T11:50:00Z"
  }
}
```

#### `GET /api/competitions/{event_id}/admin/results?limit=50`
Get comprehensive results for administrators.

**Authentication:** Teacher, Institute, or Admin role required

**Query Parameters:**
- `limit`: Number of results (1-500, default: 50)

### **5. Helper Endpoints**

#### `GET /api/competitions/{event_id}/exam/validate`
Validate if an event is a valid competition with an exam.

**Authentication:** Student, Teacher, or Institute role required

**Response:**
```json
{
  "success": true,
  "message": "Valid competition exam found",
  "exam_id": "uuid"
}
```

---

## 🔄 **Workflow**

### **For Students:**

1. **Check Competition Info**
   ```
   GET /api/competitions/{event_id}/exam/info
   ```

2. **Register for Competition** (if not registered)
   ```
   POST /api/competitions/{event_id}/exam/register
   ```

3. **Check Status** (when competition starts)
   ```
   GET /api/competitions/{event_id}/exam/status
   ```

4. **Start Exam Attempt**
   ```
   POST /api/competitions/{event_id}/exam/start
   ```

5. **Get Questions**
   ```
   GET /api/competitions/{event_id}/exam/questions
   ```

6. **Submit Answers** (use existing exam session API)
   ```
   POST /api/exams/session/submit
   ```

7. **View Results**
   ```
   GET /api/competitions/{event_id}/results
   ```

### **For Administrators:**

1. **Validate Competition Setup**
   ```
   GET /api/competitions/{event_id}/exam/validate
   ```

2. **Monitor Results**
   ```
   GET /api/competitions/{event_id}/admin/results
   ```

---

## 🛡️ **Security & Validation**

### **Event Validation:**
- ✅ Event must exist
- ✅ Event must be marked as competition (`is_competition = true`)
- ✅ Event must have a linked exam (`competition_exam_id` not null)
- ✅ Linked exam must exist and be accessible

### **Registration Validation:**
- ✅ Registration period must be open
- ✅ Competition must not have started
- ✅ Student must not already be registered
- ✅ Competition capacity must not be exceeded

### **Attempt Validation:**
- ✅ Student must be registered
- ✅ Competition must be currently active
- ✅ Student must not have already completed
- ✅ Exam session must be valid

### **Access Control:**
- ✅ Students can only see their own results in leaderboards
- ✅ Teachers/Institutes can see comprehensive results
- ✅ All endpoints require appropriate role authentication

---

## 🔗 **Integration**

This API integrates seamlessly with:
- **Existing Exam System**: Reuses exam models and session management
- **Event Management**: Works with competition events
- **User Authentication**: Uses existing role-based access control
- **Notification System**: Can trigger notifications for important events

The system leverages existing infrastructure while providing competition-specific functionality and validation.
