import os
from dotenv import load_dotenv
from typing import List
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from Cruds.users import create_user, get_all_students, get_all_teachers, get_all_sponsors, get_all_institutes, get_user_by_id, signin, get_all_users, update_profile_picture, upload_profile_picture, delete_user
from Schemas.users import Signin, UserCreate, UserOut
from Models.users import UserTypeEnum
from config.deps import get_current_user
from config.session import get_db
from config.permission import require_type
from Schemas.Token import Token
from config.security import oauth2_scheme

from datetime import datetime, timedelta
load_dotenv()
router = APIRouter()

@router.post("/signin", response_model=dict)
def sign_in(user : Signin, db: Session = Depends(get_db)):
    try:
        print(f"Signin attempt for email: {user.email}")
        result = signin(db, user.email, user.password)
        print(f"Signin successful for email: {user.email}")
        return result
    except HTTPException:
        raise
    except Exception as e:
        print(f"Error in signin endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.post("/signup", response_model=UserOut)
def sign_up(user: UserCreate, db: Session = Depends(get_db)) -> UserOut:
    if user.user_type == "admin":
        valid_user_types = [e.value for e in UserTypeEnum if e.value != "admin"]
        raise HTTPException(status_code=400, detail=f"User type 'admin' is not allowed for user creation. Please use: {', '.join(valid_user_types)}")
    return create_user(db, user)

@router.get("/me", response_model = UserOut)
def Me(db: Session = Depends(get_db), token: str = Depends(oauth2_scheme)) -> UserOut:
    """
    Get the current user's details.
    
    Args:
        db (Session): Database session dependency.
        token (str): OAuth2 token dependency.

    Returns:
        UserOut: The current user's details.
    """
    user = get_current_user(token = token, db = db)
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    return user


@router.post("/", response_model=UserOut)
def create_user_endpoint(user: UserCreate, db: Session = Depends(get_db), _ = Depends(require_type("admin"))) -> UserOut:
    """
    Create a new user.
    
    Args:
        user (UserCreate): User creation schema.
        db (Session): Database session dependency.

    Returns:
        UserOut: The created user details.
    """
    return create_user(db, user)

@router.get("/", response_model=List[UserOut])
def get_users(db : Session = Depends(get_db), token: str = Depends(oauth2_scheme), _ = Depends(require_type("admin"))) -> List[UserOut]:
    return get_all_users(db)

@router.get("/followable")
def followable_redirect(skip: int = 0, limit: int = 100):
    """
    Redirect endpoint for backward compatibility.

    The /followable endpoint has been moved to /api/social/follow/followable
    for better organization of social features.
    """
    raise HTTPException(
        status_code=410,  # Gone - indicates the resource has been moved permanently
        detail={
            "message": "Endpoint moved permanently",
            "old_endpoint": "/api/users/followable",
            "new_endpoint": "/api/social/follow/followable",
            "comprehensive_discovery": "/api/social/follow/discover",
            "social_dashboard": "/api/social/follow/dashboard",
            "migration_guide": "Use /api/social/follow/discover for better functionality"
        }
    )

@router.get("/{user_id}", response_model=UserOut)
def get_user(user_id: str, db: Session = Depends(get_db), token: str = Depends(oauth2_scheme)) -> UserOut:
    """
    Get a user by ID.

    Args:
        user_id (str): User ID (must be a valid UUID).
        db (Session): Database session dependency.
        token (str): OAuth2 token dependency.

    Returns:
        UserOut: The user details.
    """
    # Validate UUID format
    try:
        from uuid import UUID
        UUID(user_id)  # This will raise ValueError if not a valid UUID
    except ValueError:
        # Check if it's a known moved endpoint
        if user_id in ["followable", "suggestions", "discover"]:
            raise HTTPException(
                status_code=410,
                detail={
                    "message": f"Endpoint /{user_id} has been moved to social module",
                    "new_location": f"/api/social/follow/{user_id}",
                    "comprehensive_discovery": "/api/social/follow/discover"
                }
            )
        else:
            raise HTTPException(
                status_code=400,
                detail=f"Invalid user ID format. Expected UUID, got: {user_id}"
            )

    return get_user_by_id(db, user_id)

@router.delete("/{user_id}")
def delete_user_endpoint(
    user_id: str,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("admin"))
):
    """
    Delete a user by ID (Admin only).

    This endpoint permanently deletes a user and all associated data including:
    - User profile and authentication data
    - All related profiles (mentor, teacher, institute, sponsor)
    - All associated tasks, exams, and submissions
    - All file uploads and attachments
    - All verification tokens and subscriptions

    Args:
        user_id (str): User ID to delete.
        db (Session): Database session dependency.
        token (str): OAuth2 token dependency.
        _ (User): Admin user dependency (ensures only admins can delete users).

    Returns:
        dict: Success message with deleted user details and cleanup summary.

    Raises:
        HTTPException: 404 if user not found, 403 if not admin.
    """
    return delete_user(db, user_id)

# Profile picture endpoints moved to /api/files/profile-picture
# These endpoints are deprecated - use the new file upload endpoints instead

"""
@router.get("/generate_sas_url/{blob_name}", response_model=str)
def generate_sas_url(
    blob_name: str,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
) -> str:
    account_name = os.getenv("AZURE_STORAGE_ACCOUNT_NAME")
    container_name = os.getenv("AZURE_STORAGE_CONTAINER_NAME")
    account_key = os.getenv("AZURE_STORAGE_ACCOUNT_KEY")
    if not all([account_name, container_name, account_key]):
        raise HTTPException(status_code=500, detail="Azure Storage environment variables are not set.")
    sas_token = generate_blob_sas(
        account_name=str(account_name),
        container_name=str(container_name),
        blob_name=blob_name,
        account_key=str(account_key),
        permission=BlobSasPermissions(write=True, create=True),  # ✅ REQUIRED for new blobs
        expiry=datetime.utcnow() + timedelta(minutes=15)
    )
    blob_url = f"https://{account_name}.blob.core.windows.net/{container_name}/{blob_name}?{sas_token}"
    return blob_url


def generate_read_url(blob_name: str) -> str:
    account_name = os.getenv("AZURE_STORAGE_ACCOUNT_NAME")
    container_name = os.getenv("AZURE_STORAGE_CONTAINER_NAME")
    account_key = os.getenv("AZURE_STORAGE_ACCOUNT_KEY")
    if not all([account_name, container_name, account_key]):
        raise Exception("Azure Storage environment variables are not set.")
    sas_token = generate_blob_sas(
        account_name=str(account_name),
        container_name=str(container_name),
        blob_name=blob_name,
        account_key=str(account_key),
        permission=BlobSasPermissions(read=True),
        expiry=datetime.utcnow() + timedelta(hours=1)
    )
    return f"https://{account_name}.blob.core.windows.net/{container_name}/{blob_name}?{sas_token}"
"""
@router.get("/students/all", response_model=List[UserOut])
def get_students(db: Session = Depends(get_db), token: str = Depends(oauth2_scheme)) -> List[UserOut]:
    return get_all_students(db)

@router.get("/teachers/all", response_model=List[UserOut])
def get_teachers(db: Session = Depends(get_db), token: str = Depends(oauth2_scheme)) -> List[UserOut]:
    return get_all_teachers(db)

@router.get("/sponsors/all", response_model=List[UserOut])
def get_sponsors(db: Session = Depends(get_db), token: str = Depends(oauth2_scheme)) -> List[UserOut]:
    return get_all_sponsors(db)

@router.get("/institutes/all", response_model=List[UserOut])
def get_institutes(db: Session = Depends(get_db), token: str = Depends(oauth2_scheme)) -> List[UserOut]:
    return get_all_institutes(db)