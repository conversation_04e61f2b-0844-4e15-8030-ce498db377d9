"""
CRUD operations for Mentor Capabilities in Enhanced Event System
"""
from typing import List, Optional, Dict, Any
import uuid
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, or_, func, desc, case
from fastapi import HTTPException
from datetime import datetime, timezone

# Import Models
from Models.users import User, UserTypeEnum
from Models.Competitions import CompetitionMentorAssignment, MentorAssignmentStatusEnum
from Models.Events import Event, EventTypeEnum

# Import Schemas
from Schemas.Users.MentorCapabilities import (
    MentorCapabilitiesUpdate, EnhancedUserBase, MentorUserOut, TeacherMentorOut,
    MentorSearchFilters, MentorSearchResult, MentorSearchResponse,
    MentorListResponse, MentorPerformanceMetrics, MentorDashboardSummary,
    MentorAssignmentRequest, MentorAssignmentResult
)


# ===== Mentor Capabilities CRUD Operations =====

def update_mentor_capabilities(
    db: Session,
    user_id: uuid.UUID,
    capabilities_data: MentorCapabilitiesUpdate
) -> EnhancedUserBase:
    """Update mentor capabilities for a user"""
    try:
        user = db.query(User).filter(User.id == user_id).first()
        if not user:
            raise HTTPException(status_code=404, detail="User not found")
        
        # Only teachers and mentors can have mentor capabilities
        if user.user_type not in [UserTypeEnum.teacher, UserTypeEnum.mentor]:
            raise HTTPException(
                status_code=400, 
                detail="Only teachers and mentors can have mentor capabilities"
            )
        
        # Update capabilities
        update_data = capabilities_data.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(user, field, value)
        
        user.updated_at = datetime.now(timezone.utc)
        
        db.commit()
        db.refresh(user)
        
        return EnhancedUserBase.model_validate(user)
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Error updating mentor capabilities: {str(e)}")


def get_mentor_users(
    db: Session,
    skip: int = 0,
    limit: int = 20,
    user_type: Optional[str] = None,
    available_only: bool = False
) -> MentorListResponse:
    """Get list of users with mentor capabilities"""
    try:
        query = db.query(User).filter(
            or_(
                and_(User.user_type == UserTypeEnum.mentor),
                and_(User.user_type == UserTypeEnum.teacher, User.can_act_as_mentor == True)
            )
        )
        
        if user_type:
            if user_type == "mentor":
                query = query.filter(User.user_type == UserTypeEnum.mentor)
            elif user_type == "teacher":
                query = query.filter(
                    and_(User.user_type == UserTypeEnum.teacher, User.can_act_as_mentor == True)
                )
        
        total = query.count()
        
        # Count available vs busy mentors
        available = 0
        busy = 0
        
        mentors = query.offset(skip).limit(limit).all()
        mentor_outs = []
        
        for mentor in mentors:
            # Get current workload
            current_assignments = db.query(CompetitionMentorAssignment).filter(
                and_(
                    CompetitionMentorAssignment.mentor_id == mentor.id,
                    CompetitionMentorAssignment.status.in_([
                        MentorAssignmentStatusEnum.ASSIGNED,
                        MentorAssignmentStatusEnum.ACCEPTED
                    ])
                )
            ).count()
            
            # Get performance metrics
            total_assignments = db.query(CompetitionMentorAssignment).filter(
                CompetitionMentorAssignment.mentor_id == mentor.id
            ).count()
            
            completed_assignments = db.query(CompetitionMentorAssignment).filter(
                and_(
                    CompetitionMentorAssignment.mentor_id == mentor.id,
                    CompetitionMentorAssignment.status == MentorAssignmentStatusEnum.COMPLETED
                )
            ).count()
            
            # Calculate availability
            max_capacity = 10  # Default capacity
            is_available = current_assignments < max_capacity
            
            if is_available:
                available += 1
            else:
                busy += 1
            
            mentor_dict = {
                "id": mentor.id,
                "username": mentor.username,
                "email": mentor.email,
                "user_type": mentor.user_type.value,
                "profile_picture": mentor.profile_picture,
                "can_act_as_mentor": mentor.can_act_as_mentor,
                "mentor_specializations": mentor.mentor_specializations,
                "judging_experience_years": mentor.judging_experience_years,
                "total_competitions_judged": completed_assignments,
                "average_rating": None,  # Would calculate from feedback
                "completion_rate": (completed_assignments / total_assignments * 100) if total_assignments > 0 else None,
                "current_workload": current_assignments,
                "max_workload_capacity": max_capacity,
                "is_available": is_available,
                "last_active": mentor.updated_at
            }
            mentor_outs.append(MentorUserOut.model_validate(mentor_dict))
        
        return MentorListResponse(
            mentors=mentor_outs,
            total=total,
            available=available,
            busy=busy,
            page=(skip // limit) + 1,
            per_page=limit
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching mentor users: {str(e)}")


def search_mentors(
    db: Session,
    filters: MentorSearchFilters,
    skip: int = 0,
    limit: int = 20
) -> MentorSearchResponse:
    """Search for mentors based on filters"""
    try:
        query = db.query(User).filter(
            or_(
                and_(User.user_type == UserTypeEnum.mentor),
                and_(User.user_type == UserTypeEnum.teacher, User.can_act_as_mentor == True)
            )
        )
        
        # Apply filters
        if filters.specializations:
            # Filter by specializations (JSON array contains any of the specified specializations)
            for spec in filters.specializations:
                query = query.filter(
                    func.json_array_length(
                        func.json_extract(User.mentor_specializations, f'$[*]')
                    ) > 0
                ).filter(
                    func.json_search(User.mentor_specializations, 'one', spec) != None
                )
        
        if filters.min_experience_years is not None:
            query = query.filter(User.judging_experience_years >= filters.min_experience_years)
        
        if filters.max_experience_years is not None:
            query = query.filter(User.judging_experience_years <= filters.max_experience_years)
        
        if filters.user_types:
            user_type_filters = []
            for user_type in filters.user_types:
                if user_type == "mentor":
                    user_type_filters.append(User.user_type == UserTypeEnum.mentor)
                elif user_type == "teacher":
                    user_type_filters.append(
                        and_(User.user_type == UserTypeEnum.teacher, User.can_act_as_mentor == True)
                    )
            if user_type_filters:
                query = query.filter(or_(*user_type_filters))
        
        if filters.institute_id:
            # Filter by mentors who have collaboration with specific institute
            query = query.join(CompetitionMentorAssignment).filter(
                CompetitionMentorAssignment.institute_id == filters.institute_id
            ).distinct()
        
        total = query.count()
        mentors = query.offset(skip).limit(limit).all()
        
        # Calculate match scores for each mentor
        results = []
        for mentor in mentors:
            # Calculate various scores
            specialization_score = calculate_specialization_match_score(mentor, filters.specializations)
            availability_score = calculate_availability_score(db, mentor)
            performance_score = calculate_performance_score(db, mentor)
            
            # Overall score (weighted average)
            overall_score = (
                specialization_score * 0.4 +
                availability_score * 0.3 +
                performance_score * 0.3
            )
            
            # Get current workload
            current_assignments = db.query(CompetitionMentorAssignment).filter(
                and_(
                    CompetitionMentorAssignment.mentor_id == mentor.id,
                    CompetitionMentorAssignment.status.in_([
                        MentorAssignmentStatusEnum.ASSIGNED,
                        MentorAssignmentStatusEnum.ACCEPTED
                    ])
                )
            ).count()
            
            mentor_dict = {
                "id": mentor.id,
                "username": mentor.username,
                "email": mentor.email,
                "user_type": mentor.user_type.value,
                "profile_picture": mentor.profile_picture,
                "can_act_as_mentor": mentor.can_act_as_mentor,
                "mentor_specializations": mentor.mentor_specializations,
                "judging_experience_years": mentor.judging_experience_years,
                "total_competitions_judged": 0,  # Would calculate
                "current_workload": current_assignments,
                "is_available": current_assignments < 10
            }
            
            result = MentorSearchResult(
                mentor=MentorUserOut.model_validate(mentor_dict),
                match_score=specialization_score,
                availability_score=availability_score,
                performance_score=performance_score,
                overall_score=overall_score
            )
            results.append(result)
        
        # Sort by overall score
        results.sort(key=lambda x: x.overall_score, reverse=True)
        
        return MentorSearchResponse(
            results=results,
            total=total,
            filters_applied=filters,
            search_metadata={
                "search_timestamp": datetime.now(timezone.utc).isoformat(),
                "total_mentors_in_system": db.query(User).filter(
                    or_(
                        User.user_type == UserTypeEnum.mentor,
                        and_(User.user_type == UserTypeEnum.teacher, User.can_act_as_mentor == True)
                    )
                ).count()
            }
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error searching mentors: {str(e)}")


def get_mentor_performance_metrics(db: Session, mentor_id: uuid.UUID) -> MentorPerformanceMetrics:
    """Get performance metrics for a specific mentor"""
    try:
        mentor = db.query(User).filter(User.id == mentor_id).first()
        if not mentor:
            raise HTTPException(status_code=404, detail="Mentor not found")
        
        # Get assignment statistics
        total_assignments = db.query(CompetitionMentorAssignment).filter(
            CompetitionMentorAssignment.mentor_id == mentor_id
        ).count()
        
        completed_assignments = db.query(CompetitionMentorAssignment).filter(
            and_(
                CompetitionMentorAssignment.mentor_id == mentor_id,
                CompetitionMentorAssignment.status == MentorAssignmentStatusEnum.COMPLETED
            )
        ).count()
        
        pending_assignments = db.query(CompetitionMentorAssignment).filter(
            and_(
                CompetitionMentorAssignment.mentor_id == mentor_id,
                CompetitionMentorAssignment.status.in_([
                    MentorAssignmentStatusEnum.ASSIGNED,
                    MentorAssignmentStatusEnum.ACCEPTED
                ])
            )
        ).count()
        
        # Calculate average completion time (placeholder)
        avg_completion_time = None
        
        # Calculate specialization match average
        avg_match_score = db.query(func.avg(CompetitionMentorAssignment.specialization_match_score)).filter(
            and_(
                CompetitionMentorAssignment.mentor_id == mentor_id,
                CompetitionMentorAssignment.specialization_match_score != None
            )
        ).scalar()
        
        # This month statistics
        current_month_start = datetime.now(timezone.utc).replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        assignments_this_month = db.query(CompetitionMentorAssignment).filter(
            and_(
                CompetitionMentorAssignment.mentor_id == mentor_id,
                CompetitionMentorAssignment.assigned_at >= current_month_start
            )
        ).count()
        
        completed_this_month = db.query(CompetitionMentorAssignment).filter(
            and_(
                CompetitionMentorAssignment.mentor_id == mentor_id,
                CompetitionMentorAssignment.completed_at >= current_month_start,
                CompetitionMentorAssignment.status == MentorAssignmentStatusEnum.COMPLETED
            )
        ).count()
        
        completion_rate_this_month = (completed_this_month / assignments_this_month * 100) if assignments_this_month > 0 else None
        
        return MentorPerformanceMetrics(
            mentor_id=mentor_id,
            total_assignments=total_assignments,
            completed_assignments=completed_assignments,
            pending_assignments=pending_assignments,
            average_completion_time_hours=avg_completion_time,
            average_rating=None,  # Would calculate from feedback
            total_feedback_given=completed_assignments,  # Assuming feedback given for each completion
            specialization_match_average=float(avg_match_score) if avg_match_score else None,
            assignments_this_month=assignments_this_month,
            completion_rate_this_month=completion_rate_this_month,
            overall_rank=None,  # Would calculate ranking
            specialization_rank=None
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching performance metrics: {str(e)}")


def get_mentor_dashboard_summary(db: Session, mentor_id: uuid.UUID) -> MentorDashboardSummary:
    """Get dashboard summary for a mentor"""
    try:
        mentor = db.query(User).filter(User.id == mentor_id).first()
        if not mentor:
            raise HTTPException(status_code=404, detail="Mentor not found")
        
        # Current assignments
        current_assignments = db.query(CompetitionMentorAssignment).filter(
            and_(
                CompetitionMentorAssignment.mentor_id == mentor_id,
                CompetitionMentorAssignment.status.in_([
                    MentorAssignmentStatusEnum.ASSIGNED,
                    MentorAssignmentStatusEnum.ACCEPTED
                ])
            )
        ).count()
        
        # Pending judgments (assignments that need action)
        pending_judgments = db.query(CompetitionMentorAssignment).filter(
            and_(
                CompetitionMentorAssignment.mentor_id == mentor_id,
                CompetitionMentorAssignment.status == MentorAssignmentStatusEnum.ASSIGNED
            )
        ).count()
        
        # Completed this month
        current_month_start = datetime.now(timezone.utc).replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        completed_this_month = db.query(CompetitionMentorAssignment).filter(
            and_(
                CompetitionMentorAssignment.mentor_id == mentor_id,
                CompetitionMentorAssignment.completed_at >= current_month_start,
                CompetitionMentorAssignment.status == MentorAssignmentStatusEnum.COMPLETED
            )
        ).count()
        
        # Get upcoming deadlines (placeholder)
        upcoming_deadlines = []
        
        # Get recent competitions
        recent_assignments = db.query(CompetitionMentorAssignment).options(
            joinedload(CompetitionMentorAssignment.competition)
        ).filter(
            CompetitionMentorAssignment.mentor_id == mentor_id
        ).order_by(desc(CompetitionMentorAssignment.assigned_at)).limit(5).all()
        
        recent_competitions = []
        for assignment in recent_assignments:
            if assignment.competition:
                recent_competitions.append({
                    "id": assignment.competition.id,
                    "title": assignment.competition.title,
                    "status": assignment.status.value,
                    "assigned_at": assignment.assigned_at.isoformat()
                })
        
        return MentorDashboardSummary(
            mentor_id=mentor_id,
            current_assignments=current_assignments,
            pending_judgments=pending_judgments,
            completed_this_month=completed_this_month,
            average_rating=None,  # Would calculate from feedback
            total_earnings=None,  # Would calculate from compensation
            upcoming_deadlines=upcoming_deadlines,
            recent_competitions=recent_competitions,
            performance_trend=None  # Would calculate trends
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching dashboard summary: {str(e)}")


# ===== Helper Functions =====

def calculate_specialization_match_score(mentor: User, required_specializations: Optional[List[str]]) -> float:
    """Calculate how well mentor's specializations match requirements"""
    if not required_specializations or not mentor.mentor_specializations:
        return 0.5  # Neutral score
    
    mentor_specs = set(mentor.mentor_specializations)
    required_specs = set(required_specializations)
    
    if not mentor_specs:
        return 0.0
    
    # Calculate overlap
    overlap = len(mentor_specs.intersection(required_specs))
    total_required = len(required_specs)
    
    return min(overlap / total_required, 1.0) if total_required > 0 else 0.5


def calculate_availability_score(db: Session, mentor: User) -> float:
    """Calculate mentor availability score"""
    current_assignments = db.query(CompetitionMentorAssignment).filter(
        and_(
            CompetitionMentorAssignment.mentor_id == mentor.id,
            CompetitionMentorAssignment.status.in_([
                MentorAssignmentStatusEnum.ASSIGNED,
                MentorAssignmentStatusEnum.ACCEPTED
            ])
        )
    ).count()
    
    max_capacity = 10  # Default capacity
    availability_ratio = max(0, (max_capacity - current_assignments) / max_capacity)
    
    return availability_ratio


def calculate_performance_score(db: Session, mentor: User) -> float:
    """Calculate mentor performance score"""
    total_assignments = db.query(CompetitionMentorAssignment).filter(
        CompetitionMentorAssignment.mentor_id == mentor.id
    ).count()
    
    if total_assignments == 0:
        return 0.5  # Neutral score for new mentors
    
    completed_assignments = db.query(CompetitionMentorAssignment).filter(
        and_(
            CompetitionMentorAssignment.mentor_id == mentor.id,
            CompetitionMentorAssignment.status == MentorAssignmentStatusEnum.COMPLETED
        )
    ).count()
    
    completion_rate = completed_assignments / total_assignments
    
    # Factor in experience
    experience_factor = min(mentor.judging_experience_years / 5.0, 1.0)  # Max at 5 years
    
    return (completion_rate * 0.7) + (experience_factor * 0.3)
