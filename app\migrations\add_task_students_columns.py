"""
Migration to add missing columns to task_students table.

This migration adds the following columns to task_students table:
- marks (Integer, nullable=True) - Marks obtained by student
- total_marks (Integer, nullable=True) - Total marks for the task
- percentage (Float, nullable=True) - Calculated percentage
- teacher_notes (String, nullable=True) - Additional teacher notes for grading
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from config.session import SQLALCHEMY_DATABASE_URL


def run_migration():
    """
    Execute the migration to add missing columns to task_students table.
    """

    engine = create_engine(SQLALCHEMY_DATABASE_URL)
    Session = sessionmaker(bind=engine)
    session = Session()

    try:
        print("Starting task_students columns migration...")

        # Check if columns already exist
        result = session.execute(text("""
            SELECT column_name
            FROM information_schema.columns
            WHERE table_name = 'task_students'
            AND column_name IN ('marks', 'total_marks', 'percentage', 'teacher_notes')
        """))
        existing_columns = [row[0] for row in result.fetchall()]

        columns_to_add = []
        if 'marks' not in existing_columns:
            columns_to_add.append("ADD COLUMN marks INTEGER")
        if 'total_marks' not in existing_columns:
            columns_to_add.append("ADD COLUMN total_marks INTEGER")
        if 'percentage' not in existing_columns:
            columns_to_add.append("ADD COLUMN percentage NUMERIC(5,2)")
        if 'teacher_notes' not in existing_columns:
            columns_to_add.append("ADD COLUMN teacher_notes TEXT")

        if columns_to_add:
            print(f"Adding columns: {', '.join([col.split(' ')[2] for col in columns_to_add])}")

            # Add columns one by one
            for column_def in columns_to_add:
                session.execute(text(f"ALTER TABLE task_students {column_def}"))
                print(f"Added column: {column_def.split(' ')[2]}")

            # Create indexes for better performance on commonly queried columns
            if 'marks' not in existing_columns:
                print("Creating index on marks column...")
                session.execute(text("""
                    CREATE INDEX IF NOT EXISTS idx_task_students_marks
                    ON task_students(marks)
                    WHERE marks IS NOT NULL
                """))
                print("Created index on marks column")

            if 'percentage' not in existing_columns:
                print("Creating index on percentage column...")
                session.execute(text("""
                    CREATE INDEX IF NOT EXISTS idx_task_students_percentage
                    ON task_students(percentage)
                    WHERE percentage IS NOT NULL
                """))
                print("Created index on percentage column")

            session.commit()
            print("Task_students columns migration completed successfully!")
        else:
            print("All task_students columns already exist. No migration needed.")

    except Exception as e:
        session.rollback()
        print(f"Migration failed: {str(e)}")
        raise
    finally:
        session.close()


if __name__ == "__main__":
    run_migration()
