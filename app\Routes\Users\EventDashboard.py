"""
User Event Dashboard Routes for EduFair Platform

This module contains API routes for user event dashboard functionality including:
- User event history and status tracking
- Event registration summaries
- Event participation analytics
- Universal access for all user types
"""

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session
from typing import List, Optional
from uuid import UUID

# Import dependencies
from config.session import get_db
from config.deps import oauth2_scheme, get_current_user
from config.permission import require_type

# Import CRUD operations
from Cruds.Users.EventDashboard import (
    get_user_event_dashboard, get_user_events, get_user_event_details,
    get_user_event_stats
)

# Import schemas
from Schemas.Users.EventDashboard import (
    UserEventDashboard, UserEventListResponse, UserEventDetails,
    EventFilterOptions, UserEventStats, EventParticipationStatus
)
from Models.Events import RegistrationStatusEnum, PaymentStatusEnum, EventCategoryEnum

router = APIRouter()


# ==================== USER EVENT DASHBOARD ROUTES ====================

@router.get("/dashboard", response_model=UserEventDashboard)
def get_my_event_dashboard(
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """
    Get comprehensive event dashboard for the current user.
    
    Works for all user types (students, teachers, sponsors, institutes).
    Returns upcoming events, past events, statistics, and quick access data.
    """
    current_user = get_current_user(token, db)
    return get_user_event_dashboard(db, current_user.id)


@router.get("/events", response_model=UserEventListResponse)
def get_my_events(
    skip: int = Query(0, ge=0, description="Number of events to skip"),
    limit: int = Query(20, ge=1, le=100, description="Number of events to return"),
    
    # Filter parameters
    status: Optional[List[RegistrationStatusEnum]] = Query(None, description="Filter by registration status"),
    category: Optional[List[EventCategoryEnum]] = Query(None, description="Filter by event category"),
    participation_status: Optional[List[EventParticipationStatus]] = Query(None, description="Filter by participation status"),
    payment_status: Optional[List[PaymentStatusEnum]] = Query(None, description="Filter by payment status"),
    search: Optional[str] = Query(None, description="Search in event title, description, or location"),
    
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """
    Get paginated list of user's events with filtering options.
    
    Supports comprehensive filtering by status, category, dates, and search.
    Works for all user types.
    """
    current_user = get_current_user(token, db)
    
    # Build filters
    filters = EventFilterOptions(
        status=status,
        category=category,
        participation_status=participation_status,
        payment_status=payment_status,
        search_query=search
    )
    
    return get_user_events(db, current_user.id, filters, skip, limit)


@router.get("/events/upcoming", response_model=UserEventListResponse)
def get_my_upcoming_events(
    skip: int = Query(0, ge=0),
    limit: int = Query(10, ge=1, le=50),
    days_ahead: int = Query(30, ge=1, le=365, description="Number of days to look ahead"),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """
    Get user's upcoming events within specified timeframe.
    """
    current_user = get_current_user(token, db)
    
    from datetime import datetime, timezone, timedelta
    now = datetime.now(timezone.utc)
    future_date = now + timedelta(days=days_ahead)
    
    filters = EventFilterOptions(
        status=[RegistrationStatusEnum.PENDING, RegistrationStatusEnum.CONFIRMED],
        date_from=now,
        date_to=future_date
    )
    
    return get_user_events(db, current_user.id, filters, skip, limit)


@router.get("/events/past", response_model=UserEventListResponse)
def get_my_past_events(
    skip: int = Query(0, ge=0),
    limit: int = Query(10, ge=1, le=50),
    days_back: int = Query(90, ge=1, le=365, description="Number of days to look back"),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """
    Get user's past events within specified timeframe.
    """
    current_user = get_current_user(token, db)
    
    from datetime import datetime, timezone, timedelta
    now = datetime.now(timezone.utc)
    past_date = now - timedelta(days=days_back)
    
    filters = EventFilterOptions(
        status=[RegistrationStatusEnum.CONFIRMED, RegistrationStatusEnum.ATTENDED],
        date_from=past_date,
        date_to=now
    )
    
    return get_user_events(db, current_user.id, filters, skip, limit)


@router.get("/events/{registration_id}", response_model=UserEventDetails)
def get_my_event_details(
    registration_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """
    Get detailed information about a specific event registration.
    
    Includes ticket information, available actions, and event details.
    """
    current_user = get_current_user(token, db)
    return get_user_event_details(db, current_user.id, registration_id)


@router.get("/stats", response_model=UserEventStats)
def get_my_event_stats(
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """
    Get user's event participation statistics.
    
    Includes total registrations, attendance, spending, and category breakdown.
    """
    current_user = get_current_user(token, db)
    return get_user_event_stats(db, current_user.id)


# ==================== ADMIN ROUTES FOR USER EVENT DATA ====================

@router.get("/users/{user_id}/dashboard", response_model=UserEventDashboard)
def get_user_dashboard_admin(
    user_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("admin"))
):
    """
    Get event dashboard for any user (admin only).
    """
    current_user = get_current_user(token, db)
    return get_user_event_dashboard(db, user_id)


@router.get("/users/{user_id}/events", response_model=UserEventListResponse)
def get_user_events_admin(
    user_id: UUID,
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    status: Optional[List[RegistrationStatusEnum]] = Query(None),
    category: Optional[List[EventCategoryEnum]] = Query(None),
    search: Optional[str] = Query(None),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("admin"))
):
    """
    Get events for any user (admin only).
    """
    current_user = get_current_user(token, db)
    
    filters = EventFilterOptions(
        status=status,
        category=category,
        search_query=search
    )
    
    return get_user_events(db, user_id, filters, skip, limit)


@router.get("/users/{user_id}/stats", response_model=UserEventStats)
def get_user_stats_admin(
    user_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("admin"))
):
    """
    Get event statistics for any user (admin only).
    """
    current_user = get_current_user(token, db)
    return get_user_event_stats(db, user_id)


# ==================== QUICK ACCESS ROUTES ====================

@router.get("/next-event", response_model=Optional[UserEventDetails])
def get_my_next_event(
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """
    Get details of user's next upcoming event.
    """
    current_user = get_current_user(token, db)
    
    dashboard = get_user_event_dashboard(db, current_user.id)
    if dashboard.next_event:
        return get_user_event_details(db, current_user.id, dashboard.next_event.registration_id)
    
    return None


@router.get("/recent-activity", response_model=List[UserEventDetails])
def get_my_recent_activity(
    limit: int = Query(5, ge=1, le=10),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """
    Get user's recent event activity (registrations, cancellations, etc.).
    """
    current_user = get_current_user(token, db)
    
    dashboard = get_user_event_dashboard(db, current_user.id)
    recent_details = []
    
    for event_summary in dashboard.recent_registrations[:limit]:
        details = get_user_event_details(db, current_user.id, event_summary.registration_id)
        recent_details.append(details)
    
    return recent_details
