from fastapi import APIRouter, Depends, HTTPException, status, Form, File, UploadFile
from sqlalchemy.orm import Session
from typing import List, Optional
from uuid import UUID
from datetime import datetime
from Schemas.Token import Token
from config.deps import get_current_user
from config.permission import require_type
from config.security import oauth2_scheme
from Schemas.TeacherModule.tasks import (
    TaskCreate, TaskOut, TaskUpdate, TaskListOut, TaskWithDetailsOut,
    TaskStudentOut, TaskClassroomOut, TaskClassroomStudentOut, TaskCreateWithAssignments,
    TaskCreateForStudent, TaskCreateForClassroom, TaskCreateForMultipleStudents, TaskCreateForMultipleClassrooms,
    TaskAttachmentOut, StudentTaskAttachmentOut, MyTasksMinimal, StudentTaskDetailedOut,
    TaskSubmissionCreate, TaskSubmissionOut, TaskSubmissionDetailOut, TaskGradeCreate, TaskGradeOut, StudentGradesListOut,
    TaskEditDetailsOut, TaskEditUpdate, TeacherTaskMinimalOut, TeacherTaskListOut,
    StudentSubmissionOut, TaskSubmissionsListOut, StudentSubmissionDetailOut, BatchGradeCreate, BatchGradeOut,
    TaskCreateWithFilesResponse, TaskSubmissionWithFilesResponse
)
from Cruds.TeacherModule import tasks as crud
from config.session import get_db

router = APIRouter()

def validate_task_id(task_id: str) -> UUID:
    """Helper function to validate task_id and handle 'undefined' case"""
    if task_id == "undefined":
        raise HTTPException(status_code=400, detail="Invalid task ID: 'undefined' is not a valid UUID")

    try:
        return UUID(task_id)
    except ValueError:
        raise HTTPException(status_code=400, detail=f"Invalid task ID format: '{task_id}' is not a valid UUID")

# === POST endpoints ===
@router.post("/for-student", response_model=TaskOut)
def create_task_for_student(task: TaskCreateForStudent, db: Session = Depends(get_db), token: Token = Depends(oauth2_scheme),
    _ = Depends(require_type("teacher"))):
    return crud.create_task_for_student(db, task)

@router.post("/for-classroom", response_model=TaskOut)
def create_task_for_classroom(task: TaskCreateForClassroom, db: Session = Depends(get_db), token: Token = Depends(oauth2_scheme),
    _ = Depends(require_type("teacher"))):
    return crud.create_task_for_classroom(db, task)

@router.post("/for-multiple-students", response_model=TaskOut)
def create_task_for_multiple_students(task: TaskCreateForMultipleStudents, db: Session = Depends(get_db), token: Token = Depends(oauth2_scheme),
    _ = Depends(require_type("teacher"))):
    return crud.create_task_for_multiple_students(db, task)

@router.post("/for-multiple-classrooms", response_model=TaskOut)
def create_task_for_multiple_classrooms(task: TaskCreateForMultipleClassrooms, db: Session = Depends(get_db), token: Token = Depends(oauth2_scheme),
    _ = Depends(require_type("teacher"))):
    return crud.create_task_for_multiple_classrooms(db, task)

@router.post("/{task_id}/students/bulk", response_model=List[TaskStudentOut])
def assign_task_to_multiple_students(task_id: UUID, student_ids: List[UUID], db: Session = Depends(get_db), token: Token = Depends(oauth2_scheme),
    _ = Depends(require_type("teacher"))):
    return crud.assign_task_to_multiple_students(db, task_id, student_ids)


# ===== NEW TASK CREATION ENDPOINTS WITH FILE UPLOAD SUPPORT =====

@router.post("/for-student-with-files", response_model=TaskCreateWithFilesResponse)
async def create_task_for_student_with_files(
    name: str = Form(...),
    description: Optional[str] = Form(None),
    deadline: Optional[str] = Form(None),
    accept_after_deadline: bool = Form(False),
    student_id: str = Form(...),
    subject_id: Optional[str] = Form(None),
    chapter_ids: Optional[str] = Form("[]"),  # JSON string of UUIDs
    topic_ids: Optional[str] = Form("[]"),    # JSON string of UUIDs
    subtopic_ids: Optional[str] = Form("[]"), # JSON string of UUIDs
    files: List[UploadFile] = File([]),
    db: Session = Depends(get_db),
    token: Token = Depends(oauth2_scheme),
    _ = Depends(require_type("teacher"))
):
    """Create a task for a student with optional file attachments"""
    return await crud.create_task_for_student_with_files(
        db, name, description, deadline, accept_after_deadline,
        student_id, subject_id, chapter_ids, topic_ids, subtopic_ids, files
    )

@router.post("/for-classroom-with-files", response_model=TaskCreateWithFilesResponse)
async def create_task_for_classroom_with_files(
    name: str = Form(...),
    description: Optional[str] = Form(None),
    deadline: Optional[str] = Form(None),
    accept_after_deadline: bool = Form(False),
    classroom_id: str = Form(...),
    subject_id: Optional[str] = Form(None),
    chapter_ids: Optional[str] = Form("[]"),  # JSON string of UUIDs
    topic_ids: Optional[str] = Form("[]"),    # JSON string of UUIDs
    subtopic_ids: Optional[str] = Form("[]"), # JSON string of UUIDs
    files: List[UploadFile] = File([]),
    db: Session = Depends(get_db),
    token: Token = Depends(oauth2_scheme),
    _ = Depends(require_type("teacher"))
):
    """Create a task for a classroom with optional file attachments"""
    return await crud.create_task_for_classroom_with_files(
        db, name, description, deadline, accept_after_deadline,
        classroom_id, subject_id, chapter_ids, topic_ids, subtopic_ids, files
    )

@router.post("/for-multiple-students-with-files", response_model=TaskCreateWithFilesResponse)
async def create_task_for_multiple_students_with_files(
    name: str = Form(...),
    description: Optional[str] = Form(None),
    deadline: Optional[str] = Form(None),
    accept_after_deadline: bool = Form(False),
    student_ids: str = Form(...),  # JSON string of UUIDs
    subject_id: Optional[str] = Form(None),
    chapter_ids: Optional[str] = Form("[]"),  # JSON string of UUIDs
    topic_ids: Optional[str] = Form("[]"),    # JSON string of UUIDs
    subtopic_ids: Optional[str] = Form("[]"), # JSON string of UUIDs
    files: List[UploadFile] = File([]),
    db: Session = Depends(get_db),
    token: Token = Depends(oauth2_scheme),
    _ = Depends(require_type("teacher"))
):
    """Create a task for multiple students with optional file attachments"""
    return await crud.create_task_for_multiple_students_with_files(
        db, name, description, deadline, accept_after_deadline,
        student_ids, subject_id, chapter_ids, topic_ids, subtopic_ids, files
    )

@router.post("/for-multiple-classrooms-with-files", response_model=TaskCreateWithFilesResponse)
async def create_task_for_multiple_classrooms_with_files(
    name: str = Form(...),
    description: Optional[str] = Form(None),
    deadline: Optional[str] = Form(None),
    accept_after_deadline: bool = Form(False),
    classroom_ids: str = Form(...),  # JSON string of UUIDs
    subject_id: Optional[str] = Form(None),
    chapter_ids: Optional[str] = Form("[]"),  # JSON string of UUIDs
    topic_ids: Optional[str] = Form("[]"),    # JSON string of UUIDs
    subtopic_ids: Optional[str] = Form("[]"), # JSON string of UUIDs
    files: List[UploadFile] = File([]),
    db: Session = Depends(get_db),
    token: Token = Depends(oauth2_scheme),
    _ = Depends(require_type("teacher"))
):
    """Create a task for multiple classrooms with optional file attachments"""
    return await crud.create_task_for_multiple_classrooms_with_files(
        db, name, description, deadline, accept_after_deadline,
        classroom_ids, subject_id, chapter_ids, topic_ids, subtopic_ids, files
    )

# === GET endpoints ===
# IMPORTANT: More specific routes must come before parameterized routes
@router.get("/minimal", response_model=TeacherTaskListOut)
def get_all_tasks_minimal(
    subject_id: Optional[UUID] = None,
    chapter_id: Optional[UUID] = None,
    topic_id: Optional[UUID] = None,
    subtopic_id: Optional[UUID] = None,
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db),
    token: Token = Depends(oauth2_scheme),
    _ = Depends(require_type("teacher"))
):
    """
    Get minimal task information for teachers with filtering - optimized for performance.
    Returns only essential task data to reduce server traffic.
    Use GET /tasks/{task_id}/edit to get full details of a specific task.
    """
    return crud.get_all_tasks_minimal_for_teacher(
        db, subject_id, chapter_id, topic_id, subtopic_id, skip, limit
    )

@router.get("/{task_id}", response_model=TaskOut)
def get_task_by_id(task_id: str, db: Session = Depends(get_db), token: Token = Depends(oauth2_scheme)):
    task_uuid = validate_task_id(task_id)
    return crud.get_task_by_id(db, task_uuid)

@router.get("/{task_id}/details", response_model=TaskWithDetailsOut)
def get_task_with_details(task_id: str, db: Session = Depends(get_db), token: Token = Depends(oauth2_scheme)):
    """Get task details including all attachments and assignments"""
    task_uuid = validate_task_id(task_id)
    return crud.get_task_with_details(db, task_uuid)

@router.get("/{task_id}/edit", response_model=TaskEditDetailsOut)
def get_task_for_editing(task_id: UUID, db: Session = Depends(get_db), token: Token = Depends(oauth2_scheme),
    _ = Depends(require_type("teacher"))):
    """Get task data formatted for teacher editing with assignment information"""
    return crud.get_task_for_editing(db, task_id)

@router.get("/", response_model=TaskListOut)
def get_all_tasks_with_filters(
    subject_id: Optional[UUID] = None,
    chapter_id: Optional[UUID] = None,
    topic_id: Optional[UUID] = None,
    subtopic_id: Optional[UUID] = None,
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db),
    token: Token = Depends(oauth2_scheme)
):
    """
    Filter tasks by optional subject_id, chapter_id, topic_id, subtopic_id.
    Returns full task details with attachments included.
    ⚠️  DEPRECATED: Use GET /tasks/minimal for better performance in task lists.
    """
    query = db.query(crud.Task)
    if subject_id is not None:
        query = query.filter(crud.Task.subject_id == subject_id)
    if chapter_id is not None:
        query = query.join(crud.TaskChapter).filter(crud.TaskChapter.chapter_id == chapter_id)
    if topic_id is not None:
        query = query.join(crud.TaskTopic).filter(crud.TaskTopic.topic_id == topic_id)
    if subtopic_id is not None:
        query = query.join(crud.TaskSubTopic).filter(crud.TaskSubTopic.subtopic_id == subtopic_id)
    total = query.count()
    tasks = query.offset(skip).limit(limit).all()

    # Get attachments for all tasks
    task_ids = [task.id for task in tasks]
    attachments = db.query(crud.TaskAttachment).filter(crud.TaskAttachment.task_id.in_(task_ids)).all()

    # Group attachments by task_id
    attachments_by_task = {}
    for attachment in attachments:
        if attachment.task_id not in attachments_by_task:
            attachments_by_task[attachment.task_id] = []
        attachments_by_task[attachment.task_id].append(attachment)

    # Build task objects with attachments
    task_objects = []
    for task in tasks:
        task_data = crud.TaskOut.model_validate(task)
        task_dict = task_data.model_dump()
        task_dict['attachments'] = [
            TaskAttachmentOut.model_validate(att)
            for att in attachments_by_task.get(task.id, [])
        ]
        task_objects.append(crud.TaskOut(**task_dict))

    return TaskListOut(tasks=task_objects, total=total)

@router.get("/classrooms/{classroom_id}", response_model=TaskListOut)
def get_tasks_by_classroom(classroom_id: UUID, skip: int = 0, limit: int = 100, db: Session = Depends(get_db), token: Token = Depends(oauth2_scheme)):
    return crud.get_tasks_by_classroom(db, classroom_id, skip, limit)

@router.get("/student", response_model=MyTasksMinimal)
def get_student_tasks(skip: int = 0, limit: int = 100, db: Session = Depends(get_db), token: str = Depends(oauth2_scheme), _ = Depends(require_type("student"))):
    """Get tasks for the current student"""
    current_user = get_current_user(token, db)
    return crud.get_tasks_by_student_minimal(db, student_id=UUID(str(current_user.id)), skip=skip, limit=limit)

@router.get("/my/tasks", response_model=MyTasksMinimal)
def get_my_tasks_minimal(skip: int = 0, limit: int = 100, db: Session = Depends(get_db), token: str = Depends(oauth2_scheme), _ = Depends(require_type("student"))):
    """Get minimal task list for student"""
    current_user = get_current_user(token, db)
    return crud.get_tasks_by_student_minimal(db, student_id=UUID(str(current_user.id)), skip=skip, limit=limit)

@router.get("/my/{task_id}", response_model=StudentTaskDetailedOut)
def get_my_task_details(task_id: str, db: Session = Depends(get_db), token: str = Depends(oauth2_scheme), _ = Depends(require_type("student"))):
    """Get detailed task info for student including attachments"""
    task_uuid = validate_task_id(task_id)
    current_user = get_current_user(token, db)
    return crud.get_task_by_id_for_student(db, task_uuid, UUID(str(current_user.id)))


@router.get("/my/{task_id}/with-data")
def get_my_task_details_with_data(task_id: str, db: Session = Depends(get_db), token: str = Depends(oauth2_scheme), _ = Depends(require_type("student"))):
    """Get detailed task info for student including attachments with binary file data"""
    task_uuid = validate_task_id(task_id)
    current_user = get_current_user(token, db)
    return crud.get_task_by_id_for_student_with_data(db, task_uuid, UUID(str(current_user.id)))


@router.get("/my/{task_id}/submission")
def get_my_task_submission(task_id: str, db: Session = Depends(get_db), token: str = Depends(oauth2_scheme), _ = Depends(require_type("student"))):
    """Get complete task submission data for current student including binary file data"""
    task_uuid = validate_task_id(task_id)
    current_user = get_current_user(token, db)
    return crud.get_student_task_submission_complete(db, task_uuid, UUID(str(current_user.id)))

@router.get("/{task_id}/students", response_model=List[TaskStudentOut])
def get_students_by_task(task_id: UUID, skip: int = 0, limit: int = 100, db: Session = Depends(get_db), token: Token = Depends(oauth2_scheme)):
    return crud.get_students_by_task(db, task_id, skip, limit)

@router.get("/{task_id}/attachments", response_model=List[TaskAttachmentOut])
def get_task_attachments(task_id: str, db: Session = Depends(get_db), token: Token = Depends(oauth2_scheme)):
    """Get all teacher attachments for a specific task"""
    if task_id == "undefined":
        raise HTTPException(status_code=400, detail="Invalid task ID: 'undefined' is not a valid UUID")

    try:
        task_uuid = UUID(task_id)
    except ValueError:
        raise HTTPException(status_code=400, detail=f"Invalid task ID format: '{task_id}' is not a valid UUID")

    return crud.get_task_attachments(db, task_uuid)

@router.get("/{task_id}/student-attachments", response_model=List[StudentTaskAttachmentOut])
def get_all_student_attachments_for_task(task_id: UUID, db: Session = Depends(get_db), token: Token = Depends(oauth2_scheme),
    _ = Depends(require_type("teacher"))):
    """Get all student attachments for a specific task (teacher only)"""
    return crud.get_all_student_attachments_for_task(db, task_id)

@router.get("/{task_id}/my-attachments", response_model=List[StudentTaskAttachmentOut])
def get_my_task_attachments(task_id: UUID, db: Session = Depends(get_db), token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("student"))):
    """Get student's own attachments for a specific task"""
    current_user = get_current_user(token, db)
    return crud.get_student_task_attachments(db, task_id, UUID(str(current_user.id)))


@router.get("/{task_id}/my-attachments-with-data")
def get_my_task_attachments_with_data(task_id: str, db: Session = Depends(get_db), token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("student"))):
    """Get student's own attachments for a specific task with binary file data"""
    task_uuid = validate_task_id(task_id)
    current_user = get_current_user(token, db)
    return crud.get_student_task_attachments_with_data(db, task_uuid, UUID(str(current_user.id)))


@router.get("/{task_id}/attachments-with-data")
def get_task_attachments_with_data(task_id: str, db: Session = Depends(get_db), token: str = Depends(oauth2_scheme)):
    """Get all teacher attachments for a specific task with binary file data"""
    task_uuid = validate_task_id(task_id)
    return crud.get_task_attachments_with_data(db, task_uuid)



# === PUT endpoints ===
@router.put("/{task_id}", response_model=TaskOut)
def update_task(task_id: UUID, task: TaskUpdate, db: Session = Depends(get_db), token: Token = Depends(oauth2_scheme),
    _ = Depends(require_type("admin"))):
    return crud.update_task(db, task_id, task)

@router.put("/{task_id}/edit", response_model=TaskEditDetailsOut)
def update_task_for_teacher(task_id: UUID, task_update: TaskEditUpdate, db: Session = Depends(get_db),
    token: Token = Depends(oauth2_scheme), _ = Depends(require_type("teacher"))):
    """Update task fields that teachers can modify: deadline, accept_after_deadline, and assignments"""
    return crud.update_task_for_teacher(db, task_id, task_update)

# === DELETE endpoints ===
@router.delete("/{task_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_task(task_id: UUID, db: Session = Depends(get_db), token: Token = Depends(oauth2_scheme),
    _ = Depends(require_type("admin"))):
    crud.delete_task(db, task_id)
    return None


# ===== NEW TASK SUBMISSION AND GRADING ENDPOINTS =====

@router.post("/{task_id}/submit", response_model=TaskSubmissionDetailOut)
def submit_task(
    task_id: UUID,
    submission: TaskSubmissionCreate,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("student"))
):
    """Submit a task with comprehensive submission data (for students)"""
    current_user = get_current_user(token, db)
    return crud.submit_task_comprehensive(
        db,
        task_id,
        UUID(str(current_user.id)),
        submission.submission_text,
        submission.submission_notes
    )


@router.post("/{task_id}/submit-with-files", response_model=TaskSubmissionWithFilesResponse)
async def submit_task_with_files(
    task_id: UUID,
    submission_text: Optional[str] = Form(None),
    submission_notes: Optional[str] = Form(None),
    files: List[UploadFile] = File([]),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("student"))
):
    """Submit a task with text and file attachments (for students)"""
    current_user = get_current_user(token, db)
    return await crud.submit_task_with_files(
        db,
        task_id,
        UUID(str(current_user.id)),
        submission_text,
        submission_notes,
        files
    )


@router.post("/{task_id}/grade/{student_id}", response_model=TaskGradeOut)
def grade_task(
    task_id: UUID,
    student_id: UUID,
    grade_data: TaskGradeCreate,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("teacher"))
):
    """Grade a student's task submission (for teachers)"""
    current_user = get_current_user(token, db)
    return crud.grade_task(
        db,
        task_id,
        student_id,
        grade_data.grade,
        grade_data.feedback,
        UUID(str(current_user.id)),
        grade_data.marks,
        grade_data.total_marks,
        grade_data.teacher_notes
    )


@router.get("/grades/my-grades", response_model=StudentGradesListOut)
def get_my_grades(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("student"))
):
    """Get all grades for the current student"""
    current_user = get_current_user(token, db)
    return crud.get_student_grades(db, UUID(str(current_user.id)), skip, limit)


@router.get("/{task_id}/submission", response_model=TaskSubmissionDetailOut)
def get_task_submission(
    task_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("student"))
):
    """Get detailed submission information for a specific task (for students)"""
    current_user = get_current_user(token, db)
    return crud.get_task_submission_details(db, task_id, UUID(str(current_user.id)))


@router.get("/{task_id}/submission/{student_id}", response_model=TaskSubmissionDetailOut)
def get_student_task_submission(
    task_id: UUID,
    student_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("teacher"))
):
    """Get detailed submission information for a specific student's task (for teachers)"""
    return crud.get_task_submission_details(db, task_id, student_id)


# ===== ENHANCED SUBMISSION VIEWING AND GRADING ENDPOINTS =====

@router.get("/{task_id}/submissions", response_model=TaskSubmissionsListOut)
def get_task_submissions(
    task_id: UUID,
    status: Optional[str] = None,  # "submitted", "pending", "graded", "late"
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("teacher"))
):
    """
    Get all submissions for a task with filtering options.

    Status filters:
    - submitted: Only students who have submitted
    - pending: Only students who haven't submitted yet
    - graded: Only submissions that have been graded
    - late: Only late submissions
    """
    return crud.get_task_submissions(db, task_id, status)


@router.get("/{task_id}/submissions/{student_id}", response_model=StudentSubmissionDetailOut)
def get_student_submission_detail(
    task_id: UUID,
    student_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("teacher"))
):
    """Get detailed view of a single student's submission for grading"""
    return crud.get_student_submission_detail(db, task_id, student_id)


@router.post("/{task_id}/submissions/{student_id}/grade", response_model=TaskGradeOut)
def grade_student_submission(
    task_id: UUID,
    student_id: UUID,
    grade_data: TaskGradeCreate,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("teacher"))
):
    """Grade a single student's submission"""
    current_user = get_current_user(token, db)
    return crud.grade_student_submission(
        db, task_id, student_id, grade_data.grade, grade_data.feedback, UUID(str(current_user.id)),
        grade_data.marks, grade_data.total_marks, grade_data.teacher_notes
    )


@router.post("/{task_id}/submissions/batch-grade", response_model=BatchGradeOut)
def batch_grade_submissions(
    task_id: UUID,
    grades_data: List[dict],  # [{"student_id": "uuid", "grade": 85, "feedback": "Good work"}]
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("teacher"))
):
    """
    Grade multiple submissions at once.

    Request body should be a list of objects with:
    - student_id: UUID of the student
    - grade: Integer between 0 and 100
    - feedback: Optional feedback string

    Example:
    [
        {"student_id": "uuid1", "grade": 85, "feedback": "Good work"},
        {"student_id": "uuid2", "grade": 92, "feedback": "Excellent"}
    ]
    """
    current_user = get_current_user(token, db)
    return crud.batch_grade_submissions(db, task_id, grades_data, UUID(str(current_user.id)))

