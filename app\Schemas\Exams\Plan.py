from pydantic import BaseModel, Field
from uuid import UUID
from typing import Optional, List
from datetime import datetime

class PlanBase(BaseModel):
    name: str = Field(..., description="Name of the subscription plan")
    description: Optional[str] = Field(None, description="Description of the plan")
    price: int = Field(..., description="Price in cents")
    duration_days: int = Field(..., description="Duration of the plan in days")
    features: Optional[str] = Field(None, description="Features as JSON string or comma-separated")
    is_active: bool = Field(True, description="Whether the plan is active")

class PlanCreate(PlanBase):
    pass

class PlanUpdate(BaseModel):
    name: Optional[str] = Field(None, description="Name of the subscription plan")
    description: Optional[str] = Field(None, description="Description of the plan")
    price: Optional[int] = Field(None, description="Price in cents")
    duration_days: Optional[int] = Field(None, description="Duration of the plan in days")
    features: Optional[str] = Field(None, description="Features as JSON string or comma-separated")
    is_active: Optional[bool] = Field(None, description="Whether the plan is active")

class PlanOut(PlanBase):
    id: UUID = Field(..., description="Unique identifier for the plan")
    created_at: Optional[datetime] = Field(None, description="Creation timestamp")
    updated_at: Optional[datetime] = Field(None, description="Last update timestamp")
    
    class Config:
        from_attributes = True
        use_enum_values = True

class PlanList(PlanBase):
    id: UUID = Field(..., description="Unique identifier for the plan")
    created_at: Optional[datetime] = Field(None, description="Creation timestamp")
    updated_at: Optional[datetime] = Field(None, description="Last update timestamp")
    
    class Config:
        from_attributes = True
        use_enum_values = True

