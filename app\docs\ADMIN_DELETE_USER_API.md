# Admin Delete User API Documentation

## Overview

The Admin Delete User API provides a secure way for administrators to permanently delete users and all their associated data from the EduFair platform. This is a destructive operation that cannot be undone.

## ⚠️ **IMPORTANT WARNINGS**

- **PERMANENT DELETION**: This operation permanently deletes the user and ALL associated data
- **NO RECOVERY**: Deleted data cannot be recovered
- **ADMIN ONLY**: Only users with admin privileges can access this endpoint
- **CASCADE DELETION**: All related data is automatically deleted

## API Endpoint

### Delete User

**DELETE** `/api/users/{user_id}`

Permanently delete a user and all associated data.

**Authentication Required:** Admin JWT Token

**Path Parameters:**
- `user_id` (string, required): UUID of the user to delete

**Headers:**
```
Authorization: Bearer <admin-jwt-token>
Content-Type: application/json
```

**Example Request:**
```http
DELETE /api/users/123e4567-e89b-12d3-a456-************
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Success Response (200):**
```json
{
  "message": "User deleted successfully",
  "user": {
    "id": "123e4567-e89b-12d3-a456-************",
    "username": "john_doe",
    "email": "<EMAIL>",
    "user_type": "student",
    "created_at": "2024-01-15T10:30:00Z"
  },
  "deleted_at": "2024-08-25T14:30:00Z",
  "note": "All associated data including profiles, tasks, and files have been removed"
}
```

## Data Deletion Scope

When a user is deleted, the following data is automatically removed:

### 🔐 **Authentication & Identity**
- User account (username, email, password hash)
- CNIC and Passport records
- Email verification tokens
- Mobile verification data

### 👤 **User Profiles**
- Student profile data
- Teacher profile and specializations
- Mentor profile and subject associations
- Institute profile and verification data
- Sponsor profile information

### 📚 **Educational Data**
- All assigned tasks and submissions
- Exam attempts and results
- Classroom memberships
- Question responses and scores
- AI-generated content and results

### 📁 **File Uploads**
- Profile pictures
- Task attachments
- Document uploads
- Any other user-uploaded files

### 💳 **Subscriptions & Payments**
- User subscription records
- Payment history
- Billing information

### 🔗 **Associations & Relationships**
- Mentor-institute associations
- Teacher-classroom assignments
- Student-classroom memberships
- Competition participations

## Error Responses

### 404 Not Found
```json
{
  "detail": "User not found."
}
```

### 403 Forbidden
```json
{
  "detail": "User 'current_user' does not have permission for: 'admin'"
}
```

### 401 Unauthorized
```json
{
  "detail": "Not authenticated"
}
```

### 500 Internal Server Error
```json
{
  "detail": "Failed to delete user: [specific error message]"
}
```

## Security Features

### 🛡️ **Access Control**
- **Admin Only**: Endpoint requires admin user type
- **JWT Authentication**: Valid admin JWT token required
- **Permission Validation**: Automatic permission checking

### 📝 **Audit Logging**
- All deletion attempts are logged
- Includes admin user ID, target user info, and timestamp
- Failed attempts are also logged with error details

### 🔒 **Data Integrity**
- Transaction-based deletion (all-or-nothing)
- Automatic rollback on errors
- Cascade deletion prevents orphaned records

## Usage Examples

### Python/Requests Example
```python
import requests

# Admin authentication
admin_token = "your-admin-jwt-token"
user_id_to_delete = "123e4567-e89b-12d3-a456-************"

# Delete user
response = requests.delete(
    f"https://api.edufair.com/api/users/{user_id_to_delete}",
    headers={
        "Authorization": f"Bearer {admin_token}",
        "Content-Type": "application/json"
    }
)

if response.status_code == 200:
    result = response.json()
    print(f"User {result['user']['username']} deleted successfully")
    print(f"Deleted at: {result['deleted_at']}")
else:
    print(f"Error: {response.status_code} - {response.json()}")
```

### JavaScript/Fetch Example
```javascript
const deleteUser = async (userId, adminToken) => {
    try {
        const response = await fetch(`/api/users/${userId}`, {
            method: 'DELETE',
            headers: {
                'Authorization': `Bearer ${adminToken}`,
                'Content-Type': 'application/json'
            }
        });

        if (response.ok) {
            const result = await response.json();
            console.log('User deleted:', result.user.username);
            return result;
        } else {
            const error = await response.json();
            throw new Error(error.detail);
        }
    } catch (error) {
        console.error('Delete failed:', error.message);
        throw error;
    }
};

// Usage
deleteUser('123e4567-e89b-12d3-a456-************', adminToken)
    .then(result => console.log('Success:', result))
    .catch(error => console.error('Error:', error));
```

### cURL Example
```bash
curl -X DELETE \
  "https://api.edufair.com/api/users/123e4567-e89b-12d3-a456-************" \
  -H "Authorization: Bearer your-admin-jwt-token" \
  -H "Content-Type: application/json"
```

## Best Practices

### ✅ **Before Deletion**
1. **Verify User Identity**: Confirm you have the correct user ID
2. **Check User Type**: Understand what type of user you're deleting
3. **Review Dependencies**: Consider impact on other users/data
4. **Backup if Needed**: Export important data before deletion

### ✅ **During Deletion**
1. **Use Admin Account**: Ensure you're authenticated as admin
2. **Monitor Logs**: Check application logs for any issues
3. **Verify Success**: Confirm the deletion completed successfully

### ✅ **After Deletion**
1. **Audit Trail**: Document the deletion in your admin logs
2. **Verify Cleanup**: Confirm all related data was removed
3. **Update Records**: Update any external systems if needed

## Testing

A test script is provided at `test_delete_user_endpoint.py` to help verify the endpoint functionality:

```bash
python test_delete_user_endpoint.py
```

The test script includes:
- Creating test users for safe deletion testing
- Testing admin access control
- Testing error handling for non-existent users
- Verifying proper response formats

## Database Schema Impact

The deletion leverages SQLAlchemy's cascade relationships defined in the User model:

```python
# Automatic cascade deletions
sponsor_profile = relationship("SponsorProfile", cascade="all, delete-orphan")
institute_profile = relationship("InstituteProfile", cascade="all, delete-orphan")
mentor_profile = relationship("MentorProfile", cascade="all, delete-orphan")
verification_tokens = relationship("EmailVerificationToken", cascade="all, delete-orphan")
# ... and many more
```

## Troubleshooting

### Common Issues

1. **403 Forbidden**: User doesn't have admin privileges
   - Solution: Ensure the JWT token belongs to an admin user

2. **404 Not Found**: User ID doesn't exist
   - Solution: Verify the user ID is correct and exists

3. **500 Internal Error**: Database constraint violations
   - Solution: Check logs for specific constraint issues

### Debug Mode

Enable debug logging to troubleshoot issues:

```python
import logging
logging.getLogger('Cruds.users').setLevel(logging.DEBUG)
```

## Related Endpoints

- `GET /api/users/` - List all users (admin)
- `GET /api/users/{user_id}` - Get user details
- `POST /api/users/` - Create new user (admin)
- `PUT /api/users/{user_id}` - Update user (admin)

## Support

For issues with the delete user endpoint:
1. Check the application logs for detailed error messages
2. Verify admin authentication and permissions
3. Ensure the user ID exists and is valid
4. Contact system administrators for database-level issues
