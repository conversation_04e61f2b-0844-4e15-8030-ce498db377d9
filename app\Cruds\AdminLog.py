"""
Admin Log CRUD Operations for EduFair Platform

This module contains CRUD operations for admin logging functionality.
"""

from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func, desc, asc
from fastapi import HTTPException, status, Request
from uuid import UUID
from typing import List, Optional, Dict, Any
from datetime import datetime, timezone

from Models.users import User, AdminLog
from Schemas.AdminLog import (
    AdminLogCreate, AdminLogResponse, AdminLogWithUser, AdminLogFilter,
    AdminLogListResponse, AdminLogStats, LogAction, ResourceType
)


# ==================== LOG CREATION ====================

def create_admin_log(
    db: Session,
    action: LogAction,
    resource_type: ResourceType,
    user_id: Optional[UUID] = None,
    resource_id: Optional[UUID] = None,
    details: Optional[Dict[str, Any]] = None,
    ip_address: Optional[str] = None,
    user_agent: Optional[str] = None
) -> AdminLogResponse:
    """Create a new admin log entry"""
    
    db_log = AdminLog(
        user_id=user_id,
        action=action.value,
        resource_type=resource_type.value,
        resource_id=resource_id,
        details=details,
        ip_address=ip_address,
        user_agent=user_agent,
        timestamp=datetime.now(timezone.utc)
    )
    
    db.add(db_log)
    db.commit()
    db.refresh(db_log)
    
    return AdminLogResponse.model_validate(db_log)


def log_user_action(
    db: Session,
    action: LogAction,
    resource_type: ResourceType,
    user_id: UUID,
    resource_id: Optional[UUID] = None,
    details: Optional[Dict[str, Any]] = None,
    request: Optional[Request] = None
) -> AdminLogResponse:
    """Log a user action with request details"""
    
    ip_address = None
    user_agent = None
    
    if request:
        # Get IP address
        ip_address = request.client.host if request.client else None
        
        # Get user agent
        user_agent = request.headers.get("user-agent")
    
    return create_admin_log(
        db=db,
        action=action,
        resource_type=resource_type,
        user_id=user_id,
        resource_id=resource_id,
        details=details,
        ip_address=ip_address,
        user_agent=user_agent
    )


def log_system_action(
    db: Session,
    action: LogAction,
    resource_type: ResourceType,
    details: Optional[Dict[str, Any]] = None
) -> AdminLogResponse:
    """Log a system action (no user involved)"""
    
    return create_admin_log(
        db=db,
        action=action,
        resource_type=resource_type,
        user_id=None,
        resource_id=None,
        details=details
    )


# ==================== LOG RETRIEVAL ====================

def get_admin_log(db: Session, log_id: UUID) -> AdminLogWithUser:
    """Get a specific admin log entry"""
    
    log_entry = db.query(AdminLog).filter(AdminLog.id == log_id).first()
    
    if not log_entry:
        raise HTTPException(status_code=404, detail="Log entry not found")
    
    # Get user info if available
    user_info = {}
    if log_entry.user_id:
        user = db.query(User).filter(User.id == log_entry.user_id).first()
        if user:
            user_info = {
                "username": user.username,
                "user_email": user.email,
                "user_type": user.user_type.value
            }
    
    return AdminLogWithUser(
        **log_entry.__dict__,
        **user_info
    )


def get_admin_logs(
    db: Session,
    filters: Optional[AdminLogFilter] = None,
    page: int = 1,
    page_size: int = 50,
    sort_by: str = "timestamp",
    sort_order: str = "desc"
) -> AdminLogListResponse:
    """Get admin logs with filtering and pagination"""
    
    offset = (page - 1) * page_size
    
    # Build base query
    query = db.query(AdminLog)
    
    # Apply filters
    if filters:
        if filters.user_id:
            query = query.filter(AdminLog.user_id == filters.user_id)
        
        if filters.action:
            query = query.filter(AdminLog.action == filters.action.value)
        
        if filters.resource_type:
            query = query.filter(AdminLog.resource_type == filters.resource_type.value)
        
        if filters.resource_id:
            query = query.filter(AdminLog.resource_id == filters.resource_id)
        
        if filters.date_from:
            query = query.filter(AdminLog.timestamp >= filters.date_from)
        
        if filters.date_to:
            query = query.filter(AdminLog.timestamp <= filters.date_to)
        
        if filters.ip_address:
            query = query.filter(AdminLog.ip_address == filters.ip_address)
        
        if filters.search_query:
            search_term = f"%{filters.search_query}%"
            query = query.join(User, AdminLog.user_id == User.id, isouter=True).filter(
                or_(
                    AdminLog.action.ilike(search_term),
                    AdminLog.resource_type.ilike(search_term),
                    User.username.ilike(search_term),
                    User.email.ilike(search_term)
                )
            )
    
    # Get total count
    total_count = query.count()
    
    # Apply sorting
    if sort_order.lower() == "asc":
        sort_func = asc
    else:
        sort_func = desc
    
    if sort_by == "timestamp":
        query = query.order_by(sort_func(AdminLog.timestamp))
    elif sort_by == "action":
        query = query.order_by(sort_func(AdminLog.action))
    elif sort_by == "resource_type":
        query = query.order_by(sort_func(AdminLog.resource_type))
    else:
        query = query.order_by(desc(AdminLog.timestamp))  # Default
    
    # Get logs
    logs = query.offset(offset).limit(page_size).all()
    
    # Convert to response format with user info
    logs_with_users = []
    for log_entry in logs:
        user_info = {}
        if log_entry.user_id:
            user = db.query(User).filter(User.id == log_entry.user_id).first()
            if user:
                user_info = {
                    "username": user.username,
                    "user_email": user.email,
                    "user_type": user.user_type.value
                }
        
        logs_with_users.append(AdminLogWithUser(
            **log_entry.__dict__,
            **user_info
        ))
    
    return AdminLogListResponse(
        logs=logs_with_users,
        total_count=total_count,
        page=page,
        page_size=page_size,
        has_next=offset + page_size < total_count,
        has_previous=page > 1,
        filters_applied=filters
    )


# ==================== LOG STATISTICS ====================

def get_admin_log_stats(db: Session, days: int = 30) -> AdminLogStats:
    """Get admin log statistics"""
    
    # Calculate date range
    end_date = datetime.now(timezone.utc)
    start_date = end_date.replace(hour=0, minute=0, second=0, microsecond=0)
    week_start = start_date - timezone.timedelta(days=7)
    month_start = start_date - timezone.timedelta(days=30)
    
    # Total logs
    total_logs = db.query(AdminLog).count()
    
    # Logs today
    logs_today = db.query(AdminLog).filter(
        func.date(AdminLog.timestamp) == start_date.date()
    ).count()
    
    # Logs this week
    logs_this_week = db.query(AdminLog).filter(
        AdminLog.timestamp >= week_start
    ).count()
    
    # Logs this month
    logs_this_month = db.query(AdminLog).filter(
        AdminLog.timestamp >= month_start
    ).count()
    
    # Most active user
    most_active_user_query = db.query(
        User.username,
        func.count(AdminLog.id).label('log_count')
    ).join(
        AdminLog, User.id == AdminLog.user_id
    ).group_by(
        User.username
    ).order_by(desc('log_count')).first()
    
    # Most common action
    most_common_action_query = db.query(
        AdminLog.action,
        func.count(AdminLog.action).label('action_count')
    ).group_by(
        AdminLog.action
    ).order_by(desc('action_count')).first()
    
    # Most affected resource
    most_affected_resource_query = db.query(
        AdminLog.resource_type,
        func.count(AdminLog.resource_type).label('resource_count')
    ).group_by(
        AdminLog.resource_type
    ).order_by(desc('resource_count')).first()
    
    return AdminLogStats(
        total_logs=total_logs,
        logs_today=logs_today,
        logs_this_week=logs_this_week,
        logs_this_month=logs_this_month,
        most_active_user=most_active_user_query.username if most_active_user_query else None,
        most_common_action=most_common_action_query.action if most_common_action_query else None,
        most_affected_resource=most_affected_resource_query.resource_type if most_affected_resource_query else None
    )


# ==================== AUDIT TRAIL ====================

def get_resource_audit_trail(
    db: Session,
    resource_type: ResourceType,
    resource_id: UUID
) -> List[AdminLogWithUser]:
    """Get audit trail for a specific resource"""
    
    logs = db.query(AdminLog).filter(
        and_(
            AdminLog.resource_type == resource_type.value,
            AdminLog.resource_id == resource_id
        )
    ).order_by(desc(AdminLog.timestamp)).all()
    
    # Convert to response format with user info
    logs_with_users = []
    for log_entry in logs:
        user_info = {}
        if log_entry.user_id:
            user = db.query(User).filter(User.id == log_entry.user_id).first()
            if user:
                user_info = {
                    "username": user.username,
                    "user_email": user.email,
                    "user_type": user.user_type.value
                }
        
        logs_with_users.append(AdminLogWithUser(
            **log_entry.__dict__,
            **user_info
        ))
    
    return logs_with_users


def get_user_activity_logs(
    db: Session,
    user_id: UUID,
    days: int = 30
) -> List[AdminLogResponse]:
    """Get activity logs for a specific user"""
    
    start_date = datetime.now(timezone.utc) - timezone.timedelta(days=days)
    
    logs = db.query(AdminLog).filter(
        and_(
            AdminLog.user_id == user_id,
            AdminLog.timestamp >= start_date
        )
    ).order_by(desc(AdminLog.timestamp)).all()
    
    return [AdminLogResponse.model_validate(log) for log in logs]


# ==================== CLEANUP OPERATIONS ====================

def cleanup_old_logs(db: Session, days_to_keep: int = 365) -> int:
    """Clean up old log entries"""
    
    cutoff_date = datetime.now(timezone.utc) - timezone.timedelta(days=days_to_keep)
    
    # Count logs to be deleted
    logs_to_delete = db.query(AdminLog).filter(
        AdminLog.timestamp < cutoff_date
    ).count()
    
    # Delete old logs
    db.query(AdminLog).filter(
        AdminLog.timestamp < cutoff_date
    ).delete()
    
    db.commit()
    
    return logs_to_delete
