from pydantic import BaseModel, Field, field_validator
from typing import Dict, Optional, List
from datetime import datetime

# --- Session Management Schemas ---
class StartSessionRequest(BaseModel):
    exam_id: str

class StartSessionResponse(BaseModel):
    session_id: str

class ExamData(BaseModel):
    """Complete exam object information required for submission"""
    exam_id: str = Field(..., description="Unique identifier for the exam")
    title: str = Field(..., description="Title of the exam")
    description: Optional[str] = Field(None, description="Description of the exam")
    total_marks: int = Field(..., ge=0, description="Total marks for the exam")
    total_duration: int = Field(..., ge=0, description="Total duration in minutes")
    start_time: Optional[datetime] = Field(None, description="Exam start time")

class QuestionData(BaseModel):
    """Complete question information required for submission"""
    question_id: str = Field(..., description="Unique identifier for the question")
    question_text: str = Field(..., min_length=1, description="Text of the question")
    question_type: str = Field(..., description="Type of question (mcq, short_answer, long_answer, etc.)")
    options: Optional[Dict] = Field(None, description="For MCQs: {'A': 'option1', 'B': 'option2', ...}")
    marks: int = Field(..., ge=0, description="Marks allocated to this question")

class StudentAnswer(BaseModel):
    """Student's attempted answer for a question"""
    question_id: str = Field(..., description="Unique identifier for the question")
    answer: str = Field(..., description="Student's answer text")
    time_spent_seconds: Optional[int] = Field(None, ge=0, description="Time spent on this question in seconds")

class SubmitSessionRequest(BaseModel):
    """Enhanced exam submission request with mandatory exam object and attempted answers"""
    session_id: str = Field(..., description="Unique session identifier")
    exam: ExamData = Field(..., description="Complete exam object - MANDATORY")
    questions: List[QuestionData] = Field(..., min_items=1, description="List of all exam questions - MANDATORY")
    student_answers: List[StudentAnswer] = Field(default_factory=list, description="List of attempted answers - Can be empty for disqualified students")

    @field_validator('questions')
    @classmethod
    def validate_questions_not_empty(cls, v):
        if not v:
            raise ValueError('Questions list cannot be empty')
        return v

    @field_validator('student_answers')
    @classmethod
    def validate_answers_for_disqualification(cls, v):
        # Allow empty answers for disqualified students - validation will be handled in business logic
        return v

class SubmitSessionResponse(BaseModel):
    success: bool
    disqualified: bool
    disqualification_reason: Optional[str] = None
    attempt_id: Optional[str] = Field(None, description="Unique identifier for the exam attempt")
    status: Optional[str] = Field(None, description="Final status of the exam attempt (submitted, disqualified, etc.)")

# --- Reconnection Schemas ---
class ReconnectionRequest(BaseModel):
    session_id: str
    reason: str

class ReconnectionRequestResponse(BaseModel):
    request_id: str
    status: str

class TeacherApprovalRequest(BaseModel):
    request_id: str
    approved: bool
    reason: Optional[str] = None

class TeacherApprovalResponse(BaseModel):
    status: str
    session_id: Optional[str] = None

class ReconnectionStatusResponse(BaseModel):
    status: str
    teacher_reason: Optional[str] = None
    session_id: Optional[str] = None

# --- Session Resume Schemas ---
class SessionResumeResponse(BaseModel):
    session_id: str
    exam_id: str
    exam_title: str
    current_answers: Dict[str, str]
    remaining_time_seconds: int
    total_duration_seconds: int
    strikes: int
    status: str

# --- Admin Schemas ---
class AdminViewSessionResponse(BaseModel):
    session_id: str
    student_id: str
    exam_id: str
    answers: str
    strikes: str
    last_heartbeat: str
    status: str
    start_time: str
    duration: str

class AdminListSessionsResponse(BaseModel):
    session_id: str
    student_id: str
    exam_id: str
    answers: str
    strikes: str
    last_heartbeat: str
    status: str
    start_time: str
    duration: str

class PendingReconnectionRequest(BaseModel):
    request_id: str
    session_id: str
    student_id: str
    exam_id: str
    reason: str
    status: str
    requested_at: str

# --- WebSocket Message Schemas ---
class WebSocketMessage(BaseModel):
    type: str
    data: Optional[Dict] = None

class SessionResumeMessage(BaseModel):
    type: str = "session_resume"
    current_answers: Dict[str, str]
    remaining_time_seconds: int
    strikes: int
    session_id: str

class AnswersSavedMessage(BaseModel):
    type: str = "answers_saved"
    timestamp: str

class DisqualifiedMessage(BaseModel):
    event: str = "disqualified"
    reason: str 