"""
Notifications Routes for EduFair Platform

This module provides APIs for user notifications functionality.
"""

from fastapi import APIRouter, Depends, HTTPException, status, Request
from sqlalchemy.orm import Session
from uuid import UUID
from typing import List, Optional

# Import dependencies
from config.session import get_db
from config.deps import oauth2_scheme, get_current_user
from config.permission import require_type

# Import CRUD operations
from Cruds.Notifications import (
    create_notification, get_notification, get_user_notifications,
    mark_notification_as_read, mark_all_notifications_as_read,
    delete_notification, create_bulk_notifications, delete_bulk_notifications,
    get_notification_stats, get_notification_dashboard
)
from Cruds.AdminLog import log_user_action

# Import schemas
from Schemas.Notifications import (
    NotificationCreate, NotificationResponse, NotificationListResponse,
    NotificationDashboard, NotificationStats, BulkNotificationCreate,
    BulkNotificationRead, BulkNotificationDelete, BulkOperationResponse,
    NotificationType
)
from Schemas.AdminLog import LogAction, ResourceType

# Import models
from Models.users import User

router = APIRouter()


# ==================== NOTIFICATION OPERATIONS ====================

@router.post("/create", response_model=NotificationResponse)
def create_user_notification(
    notification_data: NotificationCreate,
    request: Request,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_type(["admin", "teacher"]))
):
    """
    Create a notification for a user.
    
    Only admins and teachers can create notifications.
    """
    # current_user is already injected via dependency
    
    try:
        notification = create_notification(db, notification_data)
        
        # Log the action
        log_user_action(
            db=db,
            action=LogAction.USER_CREATE,  # Using generic action for notification creation
            resource_type=ResourceType.NOTIFICATION,
            user_id=current_user.id,
            resource_id=notification.id,
            details={
                "creator_username": current_user.username,
                "target_user_id": str(notification_data.user_id),
                "notification_type": notification_data.notification_type.value,
                "title": notification_data.title
            },
            request=request
        )
        
        return notification
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create notification: {str(e)}"
        )


@router.get("/", response_model=NotificationListResponse)
def get_my_notifications(
    page: int = 1,
    page_size: int = 20,
    unread_only: bool = False,
    notification_type: Optional[str] = None,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """
    Get notifications for the current user.
    
    Returns paginated list of notifications with filtering options.
    """
    current_user = get_current_user(token, db)
    
    try:
        notifications = get_user_notifications(
            db, current_user.id, page, page_size, unread_only, notification_type
        )
        return notifications
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get notifications: {str(e)}"
        )


@router.get("/{notification_id}", response_model=NotificationResponse)
def get_notification_detail(
    notification_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """
    Get a specific notification.
    
    Users can only view their own notifications.
    """
    current_user = get_current_user(token, db)
    
    try:
        notification = get_notification(db, notification_id, current_user.id)
        return notification
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get notification: {str(e)}"
        )


@router.put("/{notification_id}/read")
def mark_notification_read(
    notification_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """
    Mark a notification as read.
    
    Users can only mark their own notifications as read.
    """
    current_user = get_current_user(token, db)
    
    try:
        success = mark_notification_as_read(db, notification_id, current_user.id)
        
        if success:
            return {"message": "Notification marked as read", "success": True}
        else:
            return {"message": "Notification not found or already read", "success": False}
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to mark notification as read: {str(e)}"
        )


@router.put("/read-all")
def mark_all_notifications_read(
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """
    Mark all notifications as read for the current user.
    
    Marks all unread notifications as read.
    """
    current_user = get_current_user(token, db)
    
    try:
        count = mark_all_notifications_as_read(db, current_user.id)
        
        return {
            "message": f"Marked {count} notifications as read",
            "notifications_marked": count
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to mark all notifications as read: {str(e)}"
        )


@router.delete("/{notification_id}")
def delete_user_notification(
    notification_id: UUID,
    request: Request,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """
    Delete a notification.
    
    Users can only delete their own notifications.
    """
    current_user = get_current_user(token, db)
    
    try:
        success = delete_notification(db, notification_id, current_user.id)
        
        # Log the action
        log_user_action(
            db=db,
            action=LogAction.USER_DELETE,  # Using generic action for notification deletion
            resource_type=ResourceType.NOTIFICATION,
            user_id=current_user.id,
            resource_id=notification_id,
            details={
                "deleter_username": current_user.username,
                "action": "notification_delete"
            },
            request=request
        )
        
        return {"message": "Notification deleted successfully", "success": success}
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete notification: {str(e)}"
        )


# ==================== BULK OPERATIONS ====================

@router.post("/bulk/create", response_model=BulkOperationResponse)
def create_bulk_user_notifications(
    bulk_data: BulkNotificationCreate,
    request: Request,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_type(["admin", "teacher"]))
):
    """
    Create notifications for multiple users.
    
    Only admins and teachers can create bulk notifications.
    """
    # current_user is already injected via dependency
    
    try:
        result = create_bulk_notifications(db, bulk_data)
        
        # Log the bulk action
        log_user_action(
            db=db,
            action=LogAction.USER_CREATE,
            resource_type=ResourceType.NOTIFICATION,
            user_id=current_user.id,
            details={
                "action_type": "bulk_create_notifications",
                "creator_username": current_user.username,
                "user_count": len(bulk_data.user_ids),
                "success_count": result["success_count"],
                "failure_count": result["failure_count"],
                "notification_type": bulk_data.notification_type.value,
                "title": bulk_data.title
            },
            request=request
        )
        
        return BulkOperationResponse(**result)
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create bulk notifications: {str(e)}"
        )


@router.post("/bulk/read")
def bulk_mark_notifications_read(
    bulk_request: BulkNotificationRead,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """
    Mark multiple notifications as read.
    
    Allows marking up to 100 notifications as read in a single request.
    """
    current_user = get_current_user(token, db)
    
    try:
        successful_operations = []
        failed_operations = []
        
        for notification_id in bulk_request.notification_ids:
            try:
                success = mark_notification_as_read(db, notification_id, current_user.id)
                if success:
                    successful_operations.append(notification_id)
                else:
                    failed_operations.append({
                        "notification_id": notification_id,
                        "error": "Notification not found or already read"
                    })
            except Exception as e:
                failed_operations.append({
                    "notification_id": notification_id,
                    "error": str(e)
                })
        
        return BulkOperationResponse(
            successful_operations=successful_operations,
            failed_operations=failed_operations,
            total_processed=len(bulk_request.notification_ids),
            success_count=len(successful_operations),
            failure_count=len(failed_operations)
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to bulk mark notifications as read: {str(e)}"
        )


@router.post("/bulk/delete")
def bulk_delete_user_notifications(
    bulk_request: BulkNotificationDelete,
    request: Request,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """
    Delete multiple notifications.
    
    Allows deleting up to 100 notifications in a single request.
    """
    current_user = get_current_user(token, db)
    
    try:
        result = delete_bulk_notifications(db, current_user.id, bulk_request.notification_ids)
        
        # Log the bulk action
        log_user_action(
            db=db,
            action=LogAction.USER_DELETE,
            resource_type=ResourceType.NOTIFICATION,
            user_id=current_user.id,
            details={
                "action_type": "bulk_delete_notifications",
                "deleter_username": current_user.username,
                "notification_count": len(bulk_request.notification_ids),
                "success_count": result["success_count"],
                "failure_count": result["failure_count"]
            },
            request=request
        )
        
        return BulkOperationResponse(**result)
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to bulk delete notifications: {str(e)}"
        )


# ==================== NOTIFICATION STATISTICS ====================

@router.get("/stats/summary", response_model=NotificationStats)
def get_notification_statistics(
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """
    Get notification statistics for the current user.
    
    Returns counts and summary information about notifications.
    """
    current_user = get_current_user(token, db)
    
    try:
        stats = get_notification_stats(db, current_user.id)
        return stats
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get notification stats: {str(e)}"
        )


@router.get("/dashboard", response_model=NotificationDashboard)
def get_notification_dashboard_data(
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """
    Get notification dashboard data for the current user.
    
    Returns comprehensive notification overview including recent notifications.
    """
    current_user = get_current_user(token, db)
    
    try:
        dashboard = get_notification_dashboard(db, current_user.id)
        return dashboard
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get notification dashboard: {str(e)}"
        )
