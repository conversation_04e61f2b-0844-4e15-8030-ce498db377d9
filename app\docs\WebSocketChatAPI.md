# WebSocket Chat API Documentation

## Overview

The EduFair WebSocket Chat API provides real-time messaging capabilities between users. It supports instant message delivery, typing indicators, user presence, and message read receipts.

## Architecture

The WebSocket implementation consists of:

- **Connection Manager** (`services/websocket_manager.py`): Manages WebSocket connections and user presence
- **WebSocket Routes** (`Routes/Social/WebSocketChat.py`): Handles WebSocket endpoints and message routing
- **WebSocket Schemas** (`Schemas/Social/WebSocketChat.py`): Defines message types and validation
- **Integration**: Works with existing chat CRUD operations and follow relationships

## Base URL

```
ws://localhost:8000/api/social/chat/ws/chat
```

## Authentication

WebSocket connections require JWT authentication via query parameter:

```
ws://localhost:8000/api/social/chat/ws/chat?token=<your_jwt_token>
```

## Connection Flow

1. **Connect**: Establish WebSocket connection with valid JWT token
2. **Authenticate**: Server validates token and establishes user session
3. **Exchange Messages**: Send/receive real-time messages
4. **Disconnect**: Clean connection closure with status updates

## Message Types

### Incoming Messages (Client → Server)

#### 1. Send Message
```json
{
  "type": "send_message",
  "receiver_id": "uuid-of-receiver",
  "message": "Hello there!"
}
```

#### 2. Typing Indicators
```json
{
  "type": "typing_start",
  "receiver_id": "uuid-of-receiver"
}
```

```json
{
  "type": "typing_stop",
  "receiver_id": "uuid-of-receiver"
}
```

#### 3. Message Read Receipt
```json
{
  "type": "message_read",
  "message_id": "uuid-of-message"
}
```

#### 4. Heartbeat
```json
{
  "type": "heartbeat"
}
```

### Outgoing Messages (Server → Client)

#### 1. New Message Received
```json
{
  "type": "new_message",
  "message_id": "uuid",
  "sender_id": "uuid",
  "sender_username": "john_doe",
  "sender_profile_picture": "url_or_null",
  "message": "Hello there!",
  "sent_at": "2024-01-15T10:30:00Z",
  "conversation_id": "uuid1_uuid2"
}
```

#### 2. Message Sent Confirmation
```json
{
  "type": "message_sent",
  "message_id": "uuid",
  "sender_id": "uuid",
  "sender_username": "john_doe",
  "sender_profile_picture": "url_or_null",
  "message": "Hello there!",
  "sent_at": "2024-01-15T10:30:00Z",
  "conversation_id": "uuid1_uuid2"
}
```

#### 3. Typing Indicators
```json
{
  "type": "typing_start",
  "user_id": "uuid",
  "username": "john_doe",
  "timestamp": "2024-01-15T10:30:00Z"
}
```

#### 4. User Status Updates
```json
{
  "type": "user_online",
  "user_id": "uuid",
  "username": "john_doe",
  "timestamp": "2024-01-15T10:30:00Z"
}
```

#### 5. Connection Established
```json
{
  "type": "connection_established",
  "message": "Connected to real-time messaging",
  "timestamp": "2024-01-15T10:30:00Z",
  "connection_id": "uuid",
  "user_id": "uuid"
}
```

#### 6. Heartbeat Response
```json
{
  "type": "heartbeat_response",
  "timestamp": "2024-01-15T10:30:00Z",
  "server_time": "2024-01-15T10:30:00Z"
}
```

#### 7. Error Messages
```json
{
  "type": "error",
  "error_code": "invalid_json",
  "message": "Invalid JSON format",
  "timestamp": "2024-01-15T10:30:00Z",
  "details": {}
}
```

## JavaScript Client Example

```javascript
class ChatWebSocket {
  constructor(authToken) {
    this.token = authToken;
    this.ws = null;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
  }

  connect() {
    const wsUrl = `ws://localhost:8000/api/social/chat/ws/chat?token=${this.token}`;
    this.ws = new WebSocket(wsUrl);

    this.ws.onopen = (event) => {
      console.log('WebSocket connected');
      this.reconnectAttempts = 0;
      this.startHeartbeat();
    };

    this.ws.onmessage = (event) => {
      const message = JSON.parse(event.data);
      this.handleMessage(message);
    };

    this.ws.onclose = (event) => {
      console.log('WebSocket disconnected');
      this.stopHeartbeat();
      this.attemptReconnect();
    };

    this.ws.onerror = (error) => {
      console.error('WebSocket error:', error);
    };
  }

  sendMessage(receiverId, messageText) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify({
        type: 'send_message',
        receiver_id: receiverId,
        message: messageText
      }));
    }
  }

  startTyping(receiverId) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify({
        type: 'typing_start',
        receiver_id: receiverId
      }));
    }
  }

  stopTyping(receiverId) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify({
        type: 'typing_stop',
        receiver_id: receiverId
      }));
    }
  }

  markMessageAsRead(messageId) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify({
        type: 'message_read',
        message_id: messageId
      }));
    }
  }

  handleMessage(message) {
    switch (message.type) {
      case 'new_message':
        this.onNewMessage(message);
        break;
      case 'message_sent':
        this.onMessageSent(message);
        break;
      case 'typing_start':
        this.onTypingStart(message);
        break;
      case 'typing_stop':
        this.onTypingStop(message);
        break;
      case 'user_online':
        this.onUserOnline(message);
        break;
      case 'user_offline':
        this.onUserOffline(message);
        break;
      case 'error':
        this.onError(message);
        break;
    }
  }

  startHeartbeat() {
    this.heartbeatInterval = setInterval(() => {
      if (this.ws && this.ws.readyState === WebSocket.OPEN) {
        this.ws.send(JSON.stringify({ type: 'heartbeat' }));
      }
    }, 30000); // Send heartbeat every 30 seconds
  }

  stopHeartbeat() {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
    }
  }

  attemptReconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      setTimeout(() => {
        console.log(`Reconnection attempt ${this.reconnectAttempts}`);
        this.connect();
      }, 1000 * this.reconnectAttempts);
    }
  }

  // Event handlers (implement these based on your UI)
  onNewMessage(message) {
    console.log('New message received:', message);
  }

  onMessageSent(message) {
    console.log('Message sent confirmation:', message);
  }

  onTypingStart(message) {
    console.log(`${message.username} is typing...`);
  }

  onTypingStop(message) {
    console.log(`${message.username} stopped typing`);
  }

  onUserOnline(message) {
    console.log(`${message.username} is now online`);
  }

  onUserOffline(message) {
    console.log(`${message.username} is now offline`);
  }

  onError(message) {
    console.error('WebSocket error:', message);
  }

  disconnect() {
    this.stopHeartbeat();
    if (this.ws) {
      this.ws.close();
    }
  }
}

// Usage
const authToken = 'your-jwt-token-here';
const chatWS = new ChatWebSocket(authToken);
chatWS.connect();

// Send a message
chatWS.sendMessage('receiver-uuid', 'Hello there!');
```

## REST API Endpoints

### Get WebSocket Statistics
```http
GET /api/social/chat/ws/stats
```

Response:
```json
{
  "total_connections": 25,
  "online_users": 15,
  "active_conversations": 8,
  "messages_per_minute": 12.5,
  "average_response_time": 150.0
}
```

### Get Online Users
```http
GET /api/social/chat/ws/online-users
```

Response:
```json
[
  {
    "user_id": "uuid",
    "username": "john_doe",
    "is_online": true,
    "last_seen": "2024-01-15T10:30:00Z",
    "connection_count": 2,
    "last_activity": "2024-01-15T10:29:45Z"
  }
]
```

## Error Codes

| Code | Description |
|------|-------------|
| `invalid_json` | Malformed JSON in message |
| `authentication_error` | Invalid or expired token |
| `unknown_message_type` | Unsupported message type |
| `message_error` | Error processing message |
| `handler_error` | Internal server error |

## Features

- ✅ Real-time message delivery
- ✅ Typing indicators
- ✅ User presence (online/offline)
- ✅ Message read receipts
- ✅ Connection heartbeat
- ✅ Automatic reconnection support
- ✅ Multiple device support
- ✅ Follow relationship validation
- ✅ Comprehensive error handling
- ✅ Connection statistics

## Security

- JWT token authentication required
- Follow relationship validation for messaging
- Connection rate limiting (planned)
- Message content validation
- Secure WebSocket connections (WSS in production)

## Best Practices

1. **Implement heartbeat** to maintain connection
2. **Handle reconnection** for network interruptions
3. **Validate messages** before sending
4. **Implement typing timeouts** (stop typing after inactivity)
5. **Cache messages locally** for offline support
6. **Rate limit** message sending on client side
7. **Handle errors gracefully** with user feedback
