"""
Student Analytics Schemas for EduFair Platform

This module contains comprehensive Pydantic schemas for student analytics
including subject-wise, class/grade-wise, classroom-wise, and competition-wise analytics.
"""

from pydantic import BaseModel, Field
from uuid import UUID
from typing import List, Optional, Dict, Any
from datetime import datetime
from decimal import Decimal


# ==================== BASE ANALYTICS SCHEMAS ====================

class AnalyticsTimeRange(BaseModel):
    """Time range for analytics queries"""
    start_date: datetime
    end_date: datetime
    period_type: str = Field(..., description="daily, weekly, monthly, quarterly, yearly")


class PerformanceTrend(BaseModel):
    """Performance trend data"""
    period: str
    value: float
    change_percentage: Optional[float] = None
    trend_direction: str = Field(..., description="improving, declining, stable")


class RankingInfo(BaseModel):
    """Ranking information"""
    current_rank: int
    total_students: int
    percentile: float
    rank_change: Optional[int] = None
    rank_trend: str = Field(..., description="up, down, stable")


# ==================== SUBJECT-WISE ANALYTICS ====================

class SubjectPerformanceDetail(BaseModel):
    """Detailed subject performance analytics"""
    subject_id: UUID
    subject_name: str
    
    # Performance metrics
    total_exams: int = 0
    total_assignments: int = 0
    average_score: float = 0.0
    highest_score: float = 0.0
    lowest_score: float = 0.0
    median_score: float = 0.0
    
    # Marks breakdown
    total_marks_obtained: int = 0
    total_marks_possible: int = 0
    success_rate: float = 0.0  # Percentage of passing grades
    
    # Comparative metrics
    class_average: float = 0.0
    performance_vs_class: float = 0.0  # Difference from class average
    ranking: RankingInfo
    
    # Trend analysis
    performance_trend: List[PerformanceTrend] = Field(default_factory=list)
    improvement_rate: float = 0.0  # Monthly improvement percentage
    
    # Chapter-wise breakdown
    chapter_performance: Dict[str, float] = Field(default_factory=dict)
    strongest_chapters: List[str] = Field(default_factory=list)
    weakest_chapters: List[str] = Field(default_factory=list)
    
    # Time-based metrics
    study_time_hours: float = 0.0
    assignment_completion_rate: float = 0.0
    on_time_submission_rate: float = 0.0
    
    # Recommendations
    improvement_areas: List[str] = Field(default_factory=list)
    recommended_actions: List[str] = Field(default_factory=list)


class SubjectAnalyticsResponse(BaseModel):
    """Response for subject-wise analytics"""
    student_id: UUID
    time_range: AnalyticsTimeRange
    subjects: List[SubjectPerformanceDetail]
    overall_gpa: float = 0.0
    strongest_subject: Optional[str] = None
    weakest_subject: Optional[str] = None
    total_study_hours: float = 0.0
    last_updated: datetime = Field(default_factory=datetime.now)


# ==================== CLASS/GRADE-WISE ANALYTICS ====================

class ClassPerformanceMetrics(BaseModel):
    """Class-level performance metrics"""
    class_id: Optional[UUID] = None
    class_name: str
    grade_level: str
    
    # Student's position in class
    student_rank: int
    total_students: int
    percentile: float
    
    # Performance comparison
    student_average: float
    class_average: float
    top_performer_score: float
    performance_gap: float  # Gap to top performer
    
    # Subject-wise class ranking
    subject_rankings: Dict[str, RankingInfo] = Field(default_factory=dict)
    
    # Peer comparison
    students_above: int
    students_below: int
    similar_performers: List[Dict[str, Any]] = Field(default_factory=list)
    
    # Improvement tracking
    rank_history: List[Dict[str, Any]] = Field(default_factory=list)
    rank_trend: str = Field(..., description="improving, declining, stable")


class GradeAnalyticsDetail(BaseModel):
    """Grade-level analytics across all classes"""
    grade_level: str
    
    # Overall grade performance
    total_students_in_grade: int
    student_rank_in_grade: int
    grade_percentile: float
    
    # Performance metrics
    student_gpa: float
    grade_average_gpa: float
    top_gpa_in_grade: float
    
    # Subject performance across grade
    grade_subject_averages: Dict[str, float] = Field(default_factory=dict)
    student_vs_grade_subjects: Dict[str, float] = Field(default_factory=dict)
    
    # Competition within grade
    top_performers: List[Dict[str, Any]] = Field(default_factory=list)
    achievement_level: str = Field(..., description="excellent, good, average, needs_improvement")


class ClassGradeAnalyticsResponse(BaseModel):
    """Response for class/grade-wise analytics"""
    student_id: UUID
    time_range: AnalyticsTimeRange
    class_performance: List[ClassPerformanceMetrics]
    grade_analytics: GradeAnalyticsDetail
    overall_academic_standing: str
    improvement_recommendations: List[str] = Field(default_factory=list)
    last_updated: datetime = Field(default_factory=datetime.now)


# ==================== CLASSROOM-WISE ANALYTICS ====================

class ClassroomEngagementMetrics(BaseModel):
    """Classroom engagement and participation metrics"""
    classroom_id: UUID
    classroom_name: str
    teacher_name: str
    subject: str
    
    # Participation metrics
    attendance_rate: float = 0.0
    participation_score: float = 0.0
    assignment_submission_rate: float = 0.0
    on_time_submission_rate: float = 0.0
    
    # Performance in classroom
    classroom_average: float
    student_average: float
    performance_vs_classroom: float
    classroom_rank: int
    total_classroom_students: int
    
    # Engagement indicators
    questions_asked: int = 0
    discussions_participated: int = 0
    peer_interactions: int = 0
    help_requests: int = 0
    
    # Assignment analytics
    assignments_completed: int = 0
    assignments_total: int = 0
    average_assignment_score: float = 0.0
    best_assignment_score: float = 0.0
    
    # Time-based metrics
    time_spent_in_classroom: float = 0.0  # Hours
    active_learning_time: float = 0.0
    
    # Teacher feedback
    positive_feedback_count: int = 0
    improvement_suggestions: List[str] = Field(default_factory=list)


class ClassroomAnalyticsResponse(BaseModel):
    """Response for classroom-wise analytics"""
    student_id: UUID
    time_range: AnalyticsTimeRange
    classrooms: List[ClassroomEngagementMetrics]
    most_engaged_classroom: Optional[str] = None
    least_engaged_classroom: Optional[str] = None
    overall_engagement_score: float = 0.0
    engagement_trend: str = Field(..., description="improving, declining, stable")
    recommendations: List[str] = Field(default_factory=list)
    last_updated: datetime = Field(default_factory=datetime.now)


# ==================== COMPETITION-WISE ANALYTICS ====================

class CompetitionPerformanceDetail(BaseModel):
    """Detailed competition performance analytics"""
    competition_id: UUID
    competition_name: str
    competition_type: str
    event_date: datetime
    
    # Participation details
    registration_date: datetime
    participation_status: str = Field(..., description="registered, participated, completed, withdrawn")
    
    # Performance metrics
    score_obtained: float = 0.0
    total_possible_score: float = 0.0
    percentage_score: float = 0.0
    
    # Ranking information
    rank: Optional[int] = None
    total_participants: int = 0
    percentile: Optional[float] = None
    
    # Category-wise performance
    category_scores: Dict[str, float] = Field(default_factory=dict)
    strongest_categories: List[str] = Field(default_factory=list)
    weakest_categories: List[str] = Field(default_factory=list)
    
    # Time management
    time_taken: Optional[int] = None  # Minutes
    time_efficiency: Optional[float] = None
    
    # Awards and achievements
    awards_received: List[str] = Field(default_factory=list)
    certificates_earned: List[str] = Field(default_factory=list)
    
    # Improvement areas
    areas_for_improvement: List[str] = Field(default_factory=list)


class CompetitionAnalyticsSummary(BaseModel):
    """Summary of competition analytics"""
    total_competitions_participated: int = 0
    total_competitions_completed: int = 0
    average_score: float = 0.0
    best_performance_score: float = 0.0
    worst_performance_score: float = 0.0
    
    # Ranking statistics
    average_rank: Optional[float] = None
    best_rank: Optional[int] = None
    top_10_finishes: int = 0
    top_25_percent_finishes: int = 0
    
    # Achievement tracking
    total_awards: int = 0
    total_certificates: int = 0
    achievement_rate: float = 0.0  # Percentage of competitions with awards
    
    # Performance trends
    performance_trend: List[PerformanceTrend] = Field(default_factory=list)
    improvement_rate: float = 0.0
    
    # Category expertise
    strongest_competition_categories: List[str] = Field(default_factory=list)
    preferred_competition_types: List[str] = Field(default_factory=list)


class CompetitionAnalyticsResponse(BaseModel):
    """Response for competition-wise analytics"""
    student_id: UUID
    time_range: AnalyticsTimeRange
    competitions: List[CompetitionPerformanceDetail]
    summary: CompetitionAnalyticsSummary
    upcoming_competitions: List[Dict[str, Any]] = Field(default_factory=list)
    recommended_competitions: List[Dict[str, Any]] = Field(default_factory=list)
    last_updated: datetime = Field(default_factory=datetime.now)


# ==================== COMPREHENSIVE ANALYTICS RESPONSE ====================

class StudentComprehensiveAnalytics(BaseModel):
    """Comprehensive analytics combining all aspects"""
    student_id: UUID
    student_name: str
    time_range: AnalyticsTimeRange
    
    # Individual analytics
    subject_analytics: SubjectAnalyticsResponse
    class_grade_analytics: ClassGradeAnalyticsResponse
    classroom_analytics: ClassroomAnalyticsResponse
    competition_analytics: CompetitionAnalyticsResponse
    
    # Overall insights
    overall_performance_score: float = 0.0
    academic_strength_areas: List[str] = Field(default_factory=list)
    improvement_opportunities: List[str] = Field(default_factory=list)
    personalized_recommendations: List[str] = Field(default_factory=list)
    
    # Goals and targets
    current_goals: List[Dict[str, Any]] = Field(default_factory=list)
    achievement_progress: Dict[str, float] = Field(default_factory=dict)
    
    last_updated: datetime = Field(default_factory=datetime.now)


# ==================== REQUEST SCHEMAS ====================

class AnalyticsRequest(BaseModel):
    """Base request for analytics"""
    time_range: AnalyticsTimeRange
    include_trends: bool = True
    include_comparisons: bool = True
    include_recommendations: bool = True


class SubjectAnalyticsRequest(AnalyticsRequest):
    """Request for subject-wise analytics"""
    subject_ids: Optional[List[UUID]] = None  # If None, include all subjects
    include_chapter_breakdown: bool = True


class ClassGradeAnalyticsRequest(AnalyticsRequest):
    """Request for class/grade analytics"""
    include_peer_comparison: bool = True
    include_rank_history: bool = True


class ClassroomAnalyticsRequest(AnalyticsRequest):
    """Request for classroom analytics"""
    classroom_ids: Optional[List[UUID]] = None  # If None, include all classrooms
    include_engagement_details: bool = True


class CompetitionAnalyticsRequest(AnalyticsRequest):
    """Request for competition analytics"""
    competition_types: Optional[List[str]] = None  # Filter by competition types
    include_upcoming: bool = True
    include_recommendations: bool = True
