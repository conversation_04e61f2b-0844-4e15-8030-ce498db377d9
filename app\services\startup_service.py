"""
Application startup service module.

This module handles all startup operations and initialization logic.
"""

from config.mongodb import init_mongodb
from services.background_services import BackgroundServiceManager


class StartupService:
    """Service for handling application startup operations."""
    
    def __init__(self):
        self.background_manager = BackgroundServiceManager()
    
    async def initialize_application(self):
        """
        Initialize all application services and components.
        
        This method handles the complete startup sequence including:
        - External service connections (Redis, MongoDB)
        - Background task initialization
        - Any other startup operations
        """
        print("Starting EduFair application...")

        try:
            # Initialize external services
            await self._initialize_external_services()

            # Start background services
            await self._start_background_services()

            print("EduFair application startup completed successfully")

        except Exception as e:
            print(f"Application startup failed: {str(e)}")
            raise

    async def _initialize_external_services(self):
        """Initialize external service connections."""
        await init_mongodb()
        print("MongoDB connection initialized")

        # Initialize in-memory cache
        from utils.cache import CacheManager
        cache_manager = CacheManager()
        print("In-memory cache initialized")
    
    async def _start_background_services(self):
        """Start background services and tasks."""
        await self.background_manager.start_services()
    
    def get_background_manager(self) -> BackgroundServiceManager:
        """Get the background service manager instance."""
        return self.background_manager
