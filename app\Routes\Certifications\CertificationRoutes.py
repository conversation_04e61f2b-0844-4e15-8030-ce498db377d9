"""
Certification System API Routes
"""
from fastapi import APIRouter, Depends, HTTPException, Query, BackgroundTasks
from sqlalchemy.orm import Session
from typing import List, Optional
import uuid

# Import dependencies
from config.session import get_db
from config.deps import get_current_user
from config.permission import require_type, require_verified_institute

# Import Models
from Models.users import User, UserTypeEnum
from Models.Certifications import CertificationTypeEnum, CertificationStatusEnum

# Import CRUD functions
from Cruds.Certifications.CertificationManagement import (
    create_certification_template, get_certification_templates,
    calculate_competition_statistics, generate_certifications_for_competition,
    submit_mentor_evaluation, get_certifications_for_competition,
    get_mentor_pending_evaluations, verify_certificate
)

# Import Schemas
from Schemas.Certifications.Certifications import (
    CertificationTemplateCreate, CertificationTemplateUpdate, CertificationTemplateOut,
    CompetitionCertificationOut, MentorEvaluationRequest, CertificationBatchRequest,
    CertificationListResponse, CertificationVerificationResponse, CertificationAnalytics
)

router = APIRouter()


# ===== Certification Template Management =====

@router.post("/templates", response_model=CertificationTemplateOut)
def create_template_endpoint(
    template_data: CertificationTemplateCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    _: None = Depends(require_verified_institute)
):
    """Create a new certification template"""
    return create_certification_template(db, template_data, current_user.id)


@router.get("/templates", response_model=List[CertificationTemplateOut])
def get_templates_endpoint(
    certification_type: Optional[CertificationTypeEnum] = Query(None),
    active_only: bool = Query(True),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    _: None = Depends(require_verified_institute)
):
    """Get certification templates for current institute"""
    return get_certification_templates(db, current_user.id, certification_type, active_only)


@router.get("/templates/institute/{institute_id}", response_model=List[CertificationTemplateOut])
def get_institute_templates_endpoint(
    institute_id: uuid.UUID,
    certification_type: Optional[CertificationTypeEnum] = Query(None),
    active_only: bool = Query(True),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get certification templates for a specific institute (Admin only)"""
    if current_user.user_type != UserTypeEnum.admin and current_user.id != institute_id:
        raise HTTPException(status_code=403, detail="Access denied")
    
    return get_certification_templates(db, institute_id, certification_type, active_only)


# ===== Competition Certification Management =====

@router.get("/competitions/{competition_id}/statistics")
def get_competition_statistics_endpoint(
    competition_id: uuid.UUID,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get competition statistics for certification planning"""
    # Verify access to competition
    from Models.Events import Event
    competition = db.query(Event).filter(Event.id == competition_id).first()
    if not competition:
        raise HTTPException(status_code=404, detail="Competition not found")
    
    if current_user.user_type == UserTypeEnum.institute and competition.institute_id != current_user.id:
        raise HTTPException(status_code=403, detail="Access denied")
    
    return calculate_competition_statistics(db, competition_id)


@router.post("/competitions/{competition_id}/generate")
def generate_certifications_endpoint(
    competition_id: uuid.UUID,
    batch_request: CertificationBatchRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    _: None = Depends(require_verified_institute)
):
    """Generate certifications for competition participants"""
    # Verify competition belongs to current institute
    from Models.Events import Event
    competition = db.query(Event).filter(Event.id == competition_id).first()
    if not competition:
        raise HTTPException(status_code=404, detail="Competition not found")
    
    if competition.institute_id != current_user.id:
        raise HTTPException(status_code=403, detail="Can only generate certifications for your own competitions")
    
    # Generate certifications
    result = generate_certifications_for_competition(
        db, 
        competition_id, 
        auto_evaluate=batch_request.auto_evaluate
    )
    
    return result


@router.get("/competitions/{competition_id}/certifications", response_model=CertificationListResponse)
def get_competition_certifications_endpoint(
    competition_id: uuid.UUID,
    status: Optional[CertificationStatusEnum] = Query(None),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get certifications for a competition"""
    return get_certifications_for_competition(db, competition_id, status)


# ===== Mentor Evaluation Routes =====

@router.get("/mentors/my/pending-evaluations", response_model=List[CompetitionCertificationOut])
def get_my_pending_evaluations_endpoint(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get certifications pending evaluation by current mentor"""
    if current_user.user_type not in [UserTypeEnum.mentor, UserTypeEnum.teacher]:
        raise HTTPException(status_code=403, detail="Only mentors can access this endpoint")
    
    return get_mentor_pending_evaluations(db, current_user.id)


@router.post("/evaluations", response_model=CompetitionCertificationOut)
def submit_evaluation_endpoint(
    evaluation_data: MentorEvaluationRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Submit mentor evaluation for a certification"""
    if current_user.user_type not in [UserTypeEnum.mentor, UserTypeEnum.teacher]:
        raise HTTPException(status_code=403, detail="Only mentors can submit evaluations")
    
    return submit_mentor_evaluation(db, evaluation_data, current_user.id)


@router.get("/mentors/{mentor_id}/pending-evaluations", response_model=List[CompetitionCertificationOut])
def get_mentor_pending_evaluations_endpoint(
    mentor_id: uuid.UUID,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get pending evaluations for a specific mentor (Admin/Institute only)"""
    if current_user.user_type not in [UserTypeEnum.admin, UserTypeEnum.institute]:
        if current_user.id != mentor_id:
            raise HTTPException(status_code=403, detail="Access denied")
    
    return get_mentor_pending_evaluations(db, mentor_id)


# ===== Certificate Verification =====

@router.get("/verify/{verification_code}", response_model=CertificationVerificationResponse)
def verify_certificate_endpoint(
    verification_code: str,
    db: Session = Depends(get_db)
):
    """Verify a certificate using verification code (Public endpoint)"""
    return verify_certificate(db, verification_code)


@router.get("/verify", response_model=CertificationVerificationResponse)
def verify_certificate_by_code_endpoint(
    code: str = Query(..., description="Verification code"),
    db: Session = Depends(get_db)
):
    """Verify a certificate using query parameter (Public endpoint)"""
    return verify_certificate(db, code)


# ===== Participant Certificate Access =====

@router.get("/my/certificates", response_model=List[CompetitionCertificationOut])
def get_my_certificates_endpoint(
    status: Optional[CertificationStatusEnum] = Query(None),
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get certificates for current user"""
    from Models.Certifications import CompetitionCertification
    
    query = db.query(CompetitionCertification).filter(
        CompetitionCertification.participant_id == current_user.id
    )
    
    if status:
        query = query.filter(CompetitionCertification.status == status)
    
    certifications = query.offset(skip).limit(limit).all()
    return [CompetitionCertificationOut.model_validate(cert) for cert in certifications]


@router.get("/participants/{participant_id}/certificates", response_model=List[CompetitionCertificationOut])
def get_participant_certificates_endpoint(
    participant_id: uuid.UUID,
    status: Optional[CertificationStatusEnum] = Query(None),
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get certificates for a specific participant (Admin/Institute only)"""
    if current_user.user_type not in [UserTypeEnum.admin, UserTypeEnum.institute]:
        if current_user.id != participant_id:
            raise HTTPException(status_code=403, detail="Access denied")
    
    from Models.Certifications import CompetitionCertification
    
    query = db.query(CompetitionCertification).filter(
        CompetitionCertification.participant_id == participant_id
    )
    
    if status:
        query = query.filter(CompetitionCertification.status == status)
    
    certifications = query.offset(skip).limit(limit).all()
    return [CompetitionCertificationOut.model_validate(cert) for cert in certifications]


# ===== Certificate Analytics =====

@router.get("/competitions/{competition_id}/analytics")
def get_certification_analytics_endpoint(
    competition_id: uuid.UUID,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get certification analytics for a competition"""
    # Verify access
    from Models.Events import Event
    competition = db.query(Event).filter(Event.id == competition_id).first()
    if not competition:
        raise HTTPException(status_code=404, detail="Competition not found")
    
    if current_user.user_type == UserTypeEnum.institute and competition.institute_id != current_user.id:
        raise HTTPException(status_code=403, detail="Access denied")
    
    # Get certifications for analytics
    from Models.Certifications import CompetitionCertification
    from sqlalchemy import func
    
    certifications = db.query(CompetitionCertification).filter(
        CompetitionCertification.competition_id == competition_id
    ).all()
    
    if not certifications:
        return {
            "competition_id": competition_id,
            "total_participants": 0,
            "total_certifications": 0,
            "certifications_by_type": {},
            "certifications_by_tier": {},
            "average_score": 0,
            "average_percentile": 0,
            "mentor_evaluation_rate": 0,
            "approval_rate": 0,
            "issuance_rate": 0
        }
    
    # Calculate analytics
    total_certifications = len(certifications)
    total_participants = len(set(cert.participant_id for cert in certifications))
    
    # Group by type
    by_type = {}
    for cert in certifications:
        cert_type = cert.certification_type.value
        by_type[cert_type] = by_type.get(cert_type, 0) + 1
    
    # Group by tier
    by_tier = {}
    for cert in certifications:
        tier = cert.performance_tier.value
        by_tier[tier] = by_tier.get(tier, 0) + 1
    
    # Calculate rates
    evaluated = len([c for c in certifications if c.evaluated_by is not None])
    approved = len([c for c in certifications if c.status == CertificationStatusEnum.APPROVED])
    issued = len([c for c in certifications if c.status == CertificationStatusEnum.ISSUED])
    
    return {
        "competition_id": competition_id,
        "total_participants": total_participants,
        "total_certifications": total_certifications,
        "certifications_by_type": by_type,
        "certifications_by_tier": by_tier,
        "average_score": sum(c.final_score for c in certifications) / total_certifications,
        "average_percentile": sum(c.percentile_rank for c in certifications) / total_certifications,
        "mentor_evaluation_rate": (evaluated / total_certifications * 100) if total_certifications > 0 else 0,
        "approval_rate": (approved / total_certifications * 100) if total_certifications > 0 else 0,
        "issuance_rate": (issued / total_certifications * 100) if total_certifications > 0 else 0
    }
