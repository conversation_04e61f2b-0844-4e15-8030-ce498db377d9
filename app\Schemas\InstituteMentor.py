"""
Institute-Mentor Relationship Schemas

Schemas for managing the relationship between institutes and mentors,
including exam/competition assignments and access control.
"""

from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime
from uuid import UUID
from enum import Enum


# ==================== ENUMS ====================

class MentorAccessTypeEnum(str, Enum):
    REVIEW = "review"
    COMPETITION = "competition"
    EVALUATION = "evaluation"
    CONSULTATION = "consultation"


class MentorAssignmentStatusEnum(str, Enum):
    ASSIGNED = "assigned"
    ACCEPTED = "accepted"
    DECLINED = "declined"
    SUSPENDED = "suspended"
    COMPLETED = "completed"


# ==================== INSTITUTE-MENTOR ASSIGNMENT SCHEMAS ====================

class InstituteMentorAssignmentCreate(BaseModel):
    """Schema for creating institute-mentor assignments"""
    mentor_id: UUID = Field(..., description="ID of the mentor to assign")
    workload_capacity: Optional[int] = Field(10, ge=1, le=50, description="Maximum concurrent assignments")
    specialization_areas: Optional[List[str]] = Field(None, description="Areas of expertise")
    hourly_rate: Optional[float] = Field(None, ge=0, description="Hourly compensation rate")
    assignment_notes: Optional[str] = Field(None, description="Additional notes about the assignment")


class InstituteMentorAssignmentUpdate(BaseModel):
    """Schema for updating institute-mentor assignments"""
    workload_capacity: Optional[int] = Field(None, ge=1, le=50)
    specialization_areas: Optional[List[str]] = Field(None)
    hourly_rate: Optional[float] = Field(None, ge=0)
    assignment_notes: Optional[str] = Field(None)
    status: Optional[MentorAssignmentStatusEnum] = Field(None)


class InstituteMentorAssignmentOut(BaseModel):
    """Schema for institute-mentor assignment output"""
    id: UUID
    mentor_id: UUID
    institute_id: UUID
    assigned_by: UUID
    assignment_type: str
    status: str
    assigned_at: datetime
    workload_capacity: int
    current_workload: Optional[int] = 0
    specialization_areas: Optional[List[str]] = None
    hourly_rate: Optional[float] = None
    assignment_notes: Optional[str] = None
    
    # Mentor details (from relationship)
    mentor_name: Optional[str] = None
    mentor_email: Optional[str] = None
    mentor_phone: Optional[str] = None
    
    class Config:
        from_attributes = True


# ==================== EXAM ACCESS SCHEMAS ====================

class MentorExamAssignmentCreate(BaseModel):
    """Schema for assigning exams to mentors"""
    exam_id: UUID = Field(..., description="ID of the exam to assign")
    mentor_id: UUID = Field(..., description="ID of the mentor")
    access_type: MentorAccessTypeEnum = Field(MentorAccessTypeEnum.REVIEW, description="Type of access")
    deadline: Optional[datetime] = Field(None, description="Deadline for review/evaluation")
    instructions: Optional[str] = Field(None, description="Special instructions for the mentor")


class MentorExamAccessOut(BaseModel):
    """Schema for mentor exam access output"""
    exam_id: UUID
    exam_title: str
    exam_description: Optional[str] = None
    access_type: str
    assigned_at: str
    status: str
    institute_id: UUID
    deadline: Optional[datetime] = None
    instructions: Optional[str] = None
    
    # Exam details
    total_marks: Optional[int] = None
    total_duration: Optional[int] = None
    question_count: Optional[int] = None
    difficulty_level: Optional[str] = None
    
    class Config:
        from_attributes = True


# ==================== COMPETITION ACCESS SCHEMAS ====================

class MentorCompetitionAccessOut(BaseModel):
    """Schema for mentor competition access output"""
    id: UUID
    competition_id: UUID
    mentor_id: UUID
    institute_id: UUID
    status: str
    assigned_at: datetime
    workload_capacity: int
    current_workload: Optional[int] = 0
    
    # Competition details (from relationship)
    competition_title: Optional[str] = None
    competition_description: Optional[str] = None
    competition_start: Optional[datetime] = None
    competition_end: Optional[datetime] = None
    
    class Config:
        from_attributes = True


# ==================== MENTOR DASHBOARD SCHEMAS ====================

class MentorDashboardSummary(BaseModel):
    """Schema for mentor dashboard summary"""
    mentor_id: UUID
    mentor_name: str
    total_institutes: int
    active_assignments: int
    pending_evaluations: int
    completed_evaluations: int
    current_workload_percentage: float
    
    # Recent activity
    recent_exam_assignments: List[MentorExamAccessOut]
    recent_competition_assignments: List[MentorCompetitionAccessOut]
    
    class Config:
        from_attributes = True


class InstituteMentorSummary(BaseModel):
    """Schema for institute's mentor management summary"""
    institute_id: UUID
    total_mentors: int
    active_mentors: int
    total_assignments: int
    pending_evaluations: int
    average_mentor_rating: Optional[float] = None
    
    # Mentor list
    mentors: List[InstituteMentorAssignmentOut]
    
    class Config:
        from_attributes = True


# ==================== BULK OPERATIONS SCHEMAS ====================

class BulkExamAssignment(BaseModel):
    """Schema for bulk exam assignments to mentors"""
    exam_ids: List[UUID] = Field(..., description="List of exam IDs to assign")
    mentor_ids: List[UUID] = Field(..., description="List of mentor IDs")
    access_type: MentorAccessTypeEnum = Field(MentorAccessTypeEnum.REVIEW)
    deadline: Optional[datetime] = Field(None)
    instructions: Optional[str] = Field(None)


class BulkAssignmentResult(BaseModel):
    """Schema for bulk assignment operation results"""
    successful_assignments: int
    failed_assignments: int
    total_requested: int
    success_rate: float
    errors: List[Dict[str, Any]]
    
    class Config:
        from_attributes = True


# ==================== MENTOR SEARCH AND FILTER SCHEMAS ====================

class MentorSearchFilters(BaseModel):
    """Schema for mentor search and filtering"""
    specialization_areas: Optional[List[str]] = Field(None)
    min_rating: Optional[float] = Field(None, ge=0, le=5)
    max_hourly_rate: Optional[float] = Field(None, ge=0)
    availability_status: Optional[str] = Field(None)
    experience_years: Optional[int] = Field(None, ge=0)
    location: Optional[str] = Field(None)


class AvailableMentor(BaseModel):
    """Schema for available mentor search results"""
    mentor_id: UUID
    mentor_name: str
    mentor_email: str
    specialization_areas: Optional[List[str]] = None
    hourly_rate: Optional[float] = None
    rating: Optional[float] = None
    total_reviews: Optional[int] = None
    current_workload: int
    max_capacity: int
    availability_percentage: float
    is_available: bool
    
    class Config:
        from_attributes = True


# ==================== MENTOR PERFORMANCE SCHEMAS ====================

class MentorPerformanceMetrics(BaseModel):
    """Schema for mentor performance tracking"""
    mentor_id: UUID
    institute_id: UUID
    evaluation_period_start: datetime
    evaluation_period_end: datetime
    
    # Performance metrics
    total_assignments: int
    completed_assignments: int
    completion_rate: float
    average_evaluation_time: Optional[float] = None  # in hours
    average_rating: Optional[float] = None
    total_feedback_given: int
    
    # Quality metrics
    consistency_score: Optional[float] = None
    timeliness_score: Optional[float] = None
    feedback_quality_score: Optional[float] = None
    
    class Config:
        from_attributes = True
