"""
Event Registrations CRUD Operations for EduFair Platform

This module contains all CRUD operations for Event Registration management including:
- Registration creation and management
- Registration status updates
- Registration analytics and reporting
"""

import uuid
from datetime import datetime, timezone
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, func, desc
from fastapi import HTTPException, status
from typing import List, Optional
from uuid import UUID
from decimal import Decimal

# Import Models
from Models.Events import (
    Event, EventTicket, EventRegistration, EventPayment,
    EventStatusEnum, RegistrationStatusEnum, PaymentStatusEnum, TicketStatusEnum
)
from Models.users import User, UserTypeEnum

# Import Schemas
from Schemas.Events.Events import (
    EventRegistrationCreate, EventRegistrationOut
)


# ==================== EVENT REGISTRATION CRUD OPERATIONS ====================

def create_event_registration(db: Session, registration_data: EventRegistrationCreate, user_id: UUID) -> EventRegistrationOut:
    """Create a new event registration."""
    # Verify event exists
    event = db.query(Event).filter(Event.id == registration_data.event_id).first()
    if not event:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Event not found"
        )
    
    # Check if event is published and registration is open
    if event.status != EventStatusEnum.PUBLISHED:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Event is not available for registration"
        )
    
    now = datetime.now(timezone.utc)
    if event.registration_start and now < event.registration_start:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Registration has not started yet"
        )
    
    if event.registration_end and now > event.registration_end:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Registration has ended"
        )
    
    # Verify ticket exists and is available
    ticket = db.query(EventTicket).filter(EventTicket.id == registration_data.ticket_id).first()
    if not ticket:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Ticket not found"
        )
    
    # Check ticket availability
    from Cruds.Events.EventTickets import check_ticket_availability
    if not check_ticket_availability(db, registration_data.ticket_id, registration_data.quantity):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Ticket is not available or insufficient quantity"
        )
    
    # Check if user is already registered for this event
    existing_registration = db.query(EventRegistration).filter(
        EventRegistration.event_id == registration_data.event_id,
        EventRegistration.user_id == user_id,
        EventRegistration.status.in_([RegistrationStatusEnum.CONFIRMED, RegistrationStatusEnum.PENDING])
    ).first()
    
    if existing_registration:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="You are already registered for this event"
        )
    
    try:
        # Calculate total amount
        total_amount = ticket.price * registration_data.quantity
        
        # Generate registration number
        import random
        import string
        registration_number = f"REG-{event.id.hex[:8].upper()}-{''.join(random.choices(string.ascii_uppercase + string.digits, k=6))}"

        # DEMO MODE: Auto-confirm all registrations without payment processing
        # In demo mode, all tickets are automatically confirmed regardless of price
        registration_status = RegistrationStatusEnum.CONFIRMED
        payment_status = PaymentStatusEnum.COMPLETED if total_amount > 0 else None

        # Create registration
        registration_dict = registration_data.model_dump()
        registration_dict.update({
            'user_id': user_id,
            'registration_number': registration_number,
            'total_amount': total_amount,
            'status': registration_status,
            'payment_status': payment_status,
            'payment_method': 'demo' if total_amount > 0 else None,
            'payment_reference': f"DEMO-{registration_number}" if total_amount > 0 else None,
            'confirmed_at': datetime.now(timezone.utc)  # Auto-confirm in demo mode
        })
        
        db_registration = EventRegistration(**registration_dict)
        db.add(db_registration)
        db.flush()  # Get the ID without committing
        
        # Create demo payment record if ticket has a price
        if ticket.price > 0:
            # Use string value directly to avoid import issues in demo mode
            payment_data = {
                'event_id': registration_data.event_id,
                'registration_id': db_registration.id,
                'user_id': user_id,
                'amount': total_amount,
                'currency': ticket.currency,
                'status': PaymentStatusEnum.COMPLETED,  # Auto-complete in demo mode
                'payment_method': 'DEMO',  # Use string value directly for demo mode
                'gateway_transaction_id': f"DEMO-{registration_number}",
                'processed_at': datetime.now(timezone.utc),
                'payment_description': f"Demo payment for {event.title}"
            }
            db_payment = EventPayment(**payment_data)
            db.add(db_payment)
        
        db.commit()
        db.refresh(db_registration)

        # Generate check-in code for the ticket
        try:
            from services.ticket_service import ticket_service
            db_registration = ticket_service.update_registration_codes(db, db_registration)
        except Exception as e:
            print(f"Warning: Failed to generate check-in code: {e}")

        # Send confirmation email
        try:
            from services.ticket_email_service import ticket_email_service
            user = db.query(User).filter(User.id == user_id).first()
            if user:
                ticket_email_service.send_ticket_confirmation(
                    db=db,
                    registration=db_registration,
                    user=user,
                    event=event,
                    ticket=ticket
                )
        except Exception as e:
            print(f"Warning: Failed to send confirmation email: {e}")

        return db_registration
        
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create registration: {str(e)}"
        )


def get_registration_by_id(db: Session, registration_id: UUID, user_id: UUID) -> EventRegistrationOut:
    """Get registration by ID."""
    registration = db.query(EventRegistration).options(
        joinedload(EventRegistration.event),
        joinedload(EventRegistration.ticket),
        joinedload(EventRegistration.user)
    ).filter(EventRegistration.id == registration_id).first()
    
    if not registration:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Registration not found"
        )
    
    # Check if user is authorized to view this registration
    user = db.query(User).filter(User.id == user_id).first()
    is_organizer = (registration.event.organizer_id == user_id or 
                   registration.event.institute_id == user_id)
    is_owner = registration.user_id == user_id
    is_admin = user and user.user_type == UserTypeEnum.admin
    
    if not (is_organizer or is_owner or is_admin):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to view this registration"
        )
    
    return registration


def get_user_registrations(db: Session, user_id: UUID, skip: int = 0, limit: int = 100) -> List[EventRegistrationOut]:
    """Get all registrations for a user."""
    registrations = db.query(EventRegistration).options(
        joinedload(EventRegistration.event),
        joinedload(EventRegistration.ticket)
    ).filter(
        EventRegistration.user_id == user_id
    ).order_by(desc(EventRegistration.created_at)).offset(skip).limit(limit).all()
    
    return registrations


def get_event_registrations(db: Session, event_id: UUID, organizer_id: UUID, skip: int = 0, limit: int = 100) -> List[EventRegistrationOut]:
    """Get all registrations for an event (organizer only)."""
    # Verify event exists and user is authorized
    event = db.query(Event).filter(Event.id == event_id).first()
    if not event:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Event not found"
        )
    
    if event.organizer_id != organizer_id and event.institute_id != organizer_id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to view registrations for this event"
        )
    
    registrations = db.query(EventRegistration).options(
        joinedload(EventRegistration.user),
        joinedload(EventRegistration.ticket)
    ).filter(
        EventRegistration.event_id == event_id
    ).order_by(desc(EventRegistration.created_at)).offset(skip).limit(limit).all()
    
    return registrations


def update_registration_status(db: Session, registration_id: UUID, status: RegistrationStatusEnum, user_id: UUID) -> EventRegistrationOut:
    """Update registration status (organizer only)."""
    registration = db.query(EventRegistration).options(
        joinedload(EventRegistration.event)
    ).filter(EventRegistration.id == registration_id).first()
    
    if not registration:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Registration not found"
        )
    
    # Check if user is authorized
    if registration.event.organizer_id != user_id and registration.event.institute_id != user_id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to update this registration"
        )
    
    try:
        registration.status = status
        registration.updated_at = datetime.now(timezone.utc)
        
        # If confirming registration, check ticket availability again
        if status == RegistrationStatusEnum.CONFIRMED:
            from Cruds.Events.EventTickets import check_ticket_availability
            if not check_ticket_availability(db, registration.ticket_id, registration.quantity):
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Ticket is no longer available"
                )
        
        db.commit()
        db.refresh(registration)
        
        return registration
        
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update registration: {str(e)}"
        )


def confirm_registration_payment(db: Session, registration_id: UUID, payment_id: UUID) -> EventRegistration:
    """
    Confirm registration after successful payment.
    This function should only be called when payment is actually completed.
    """
    # Get registration
    registration = db.query(EventRegistration).filter(EventRegistration.id == registration_id).first()
    if not registration:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Registration not found"
        )

    # Get payment to verify it's completed
    payment = db.query(EventPayment).filter(EventPayment.id == payment_id).first()
    if not payment:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Payment not found"
        )

    # Only confirm if payment is actually completed
    if payment.status != PaymentStatusEnum.COMPLETED:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Cannot confirm registration: payment status is {payment.status.value}"
        )

    try:
        # Update registration status to confirmed
        registration.status = RegistrationStatusEnum.CONFIRMED
        registration.confirmed_at = datetime.now(timezone.utc)
        registration.payment_status = PaymentStatusEnum.COMPLETED
        registration.payment_reference = payment.gateway_transaction_id
        registration.updated_at = datetime.now(timezone.utc)

        # Check ticket availability one more time
        from Cruds.Events.EventTickets import check_ticket_availability
        if not check_ticket_availability(db, registration.ticket_id, registration.quantity):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Ticket is no longer available"
            )

        db.commit()
        db.refresh(registration)

        return registration

    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to confirm registration: {str(e)}"
        )


def cancel_registration(db: Session, registration_id: UUID, user_id: UUID) -> None:
    """Cancel a registration."""
    registration = db.query(EventRegistration).options(
        joinedload(EventRegistration.event)
    ).filter(EventRegistration.id == registration_id).first()
    
    if not registration:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Registration not found"
        )
    
    # Check if user is authorized (registration owner or event organizer)
    is_owner = registration.user_id == user_id
    is_organizer = (registration.event.organizer_id == user_id or 
                   registration.event.institute_id == user_id)
    
    if not (is_owner or is_organizer):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to cancel this registration"
        )
    
    # Check if cancellation is allowed
    if registration.status == RegistrationStatusEnum.ATTENDED:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Cannot cancel registration for attended event"
        )
    
    try:
        registration.status = RegistrationStatusEnum.CANCELLED
        registration.updated_at = datetime.now(timezone.utc)
        
        # Update payment status if exists
        payment = db.query(EventPayment).filter(
            EventPayment.registration_id == registration_id
        ).first()
        if payment and payment.status == PaymentStatusEnum.COMPLETED:
            payment.status = PaymentStatusEnum.REFUNDED
            payment.updated_at = datetime.now(timezone.utc)
        
        db.commit()
        
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to cancel registration: {str(e)}"
        )


def get_registration_statistics(db: Session, event_id: UUID, organizer_id: UUID) -> dict:
    """Get registration statistics for an event."""
    # Verify event exists and user is authorized
    event = db.query(Event).filter(Event.id == event_id).first()
    if not event:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Event not found"
        )
    
    if event.organizer_id != organizer_id and event.institute_id != organizer_id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to view statistics for this event"
        )
    
    # Get registration counts by status
    stats = db.query(
        EventRegistration.status,
        func.count(EventRegistration.id).label('count'),
        func.sum(EventRegistration.quantity).label('total_quantity')
    ).filter(
        EventRegistration.event_id == event_id
    ).group_by(EventRegistration.status).all()
    
    # Get total revenue
    total_revenue = db.query(func.sum(EventRegistration.total_amount)).filter(
        EventRegistration.event_id == event_id,
        EventRegistration.status.in_([RegistrationStatusEnum.CONFIRMED, RegistrationStatusEnum.ATTENDED])
    ).scalar() or 0
    
    # Build statistics
    statistics = {
        "total_registrations": 0,
        "total_attendees": 0,
        "pending_registrations": 0,
        "confirmed_registrations": 0,
        "cancelled_registrations": 0,
        "attended_count": 0,
        "total_revenue": float(total_revenue),
        "by_status": {}
    }
    
    for stat in stats:
        status_name = stat.status.value
        count = stat.count
        quantity = stat.total_quantity or 0
        
        statistics["by_status"][status_name] = {
            "count": count,
            "quantity": quantity
        }
        
        statistics["total_registrations"] += count
        
        if stat.status == RegistrationStatusEnum.PENDING:
            statistics["pending_registrations"] = count
        elif stat.status == RegistrationStatusEnum.CONFIRMED:
            statistics["confirmed_registrations"] = count
            statistics["total_attendees"] += quantity
        elif stat.status == RegistrationStatusEnum.CANCELLED:
            statistics["cancelled_registrations"] = count
        elif stat.status == RegistrationStatusEnum.ATTENDED:
            statistics["attended_count"] = count
            statistics["total_attendees"] += quantity
    
    return statistics


# ==================== ADMIN REGISTRATION OPERATIONS ====================

def update_event_registration(db: Session, registration_id: UUID, registration_update) -> EventRegistrationOut:
    """Update an event registration (admin only)."""
    try:
        registration = db.query(EventRegistration).filter(EventRegistration.id == registration_id).first()
        if not registration:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Registration not found"
            )

        # Update fields that are provided
        update_data = registration_update.model_dump(exclude_unset=True)

        for field, value in update_data.items():
            if hasattr(registration, field):
                setattr(registration, field, value)

        # Update timestamp
        registration.updated_at = datetime.now(timezone.utc)

        # Handle status changes
        if 'status' in update_data:
            if update_data['status'] == 'CONFIRMED' and not registration.confirmed_at:
                registration.confirmed_at = datetime.now(timezone.utc)
            elif update_data['status'] == 'CANCELLED' and not registration.cancelled_at:
                registration.cancelled_at = datetime.now(timezone.utc)
            elif update_data['status'] == 'ATTENDED' and not registration.attended_at:
                registration.attended_at = datetime.now(timezone.utc)

        db.commit()
        db.refresh(registration)

        return registration

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update registration: {str(e)}"
        )


def delete_event_registration(db: Session, registration_id: UUID) -> bool:
    """Delete an event registration (admin only)."""
    try:
        registration = db.query(EventRegistration).filter(EventRegistration.id == registration_id).first()
        if not registration:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Registration not found"
            )

        # Delete associated payments first
        db.query(EventPayment).filter(EventPayment.registration_id == registration_id).delete()

        # Delete the registration
        db.delete(registration)
        db.commit()

        return True

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete registration: {str(e)}"
        )
