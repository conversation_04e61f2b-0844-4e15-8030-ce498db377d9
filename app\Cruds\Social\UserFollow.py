"""
User Follow CRUD Operations for EduFair Platform

This module contains CRUD operations for user following functionality.
"""

from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func, desc
from fastapi import HTTPException, status
from uuid import UUID
from typing import List, Optional, Tuple
from datetime import datetime, timezone

from Models.users import User, UserFollow
from Schemas.Social.UserFollow import (
    UserFollowCreate, UserFollowResponse, UserBasicInfo,
    FollowListResponse, UserFollowStats, FollowSuggestion
)


# ==================== FOLLOW OPERATIONS ====================

def create_follow(db: Session, follower_id: UUID, follow_data: UserFollowCreate) -> UserFollowResponse:
    """Create a new follow relationship"""
    
    # Check if users exist
    follower = db.query(User).filter(User.id == follower_id).first()
    following = db.query(User).filter(User.id == follow_data.following_id).first()
    
    if not follower:
        raise HTTPException(status_code=404, detail="Follower user not found")
    if not following:
        raise HTTPException(status_code=404, detail="User to follow not found")
    
    # Check if not trying to follow self
    if follower_id == follow_data.following_id:
        raise HTTPException(status_code=400, detail="Cannot follow yourself")
    
    # Check if already following
    existing_follow = db.query(UserFollow).filter(
        and_(
            UserFollow.follower_id == follower_id,
            UserFollow.following_id == follow_data.following_id
        )
    ).first()
    
    if existing_follow:
        raise HTTPException(status_code=400, detail="Already following this user")
    
    # Create follow relationship
    db_follow = UserFollow(
        follower_id=follower_id,
        following_id=follow_data.following_id,
        followed_at=datetime.now(timezone.utc)
    )
    
    db.add(db_follow)
    db.commit()
    db.refresh(db_follow)
    
    return UserFollowResponse.model_validate(db_follow)


def delete_follow(db: Session, follower_id: UUID, following_id: UUID) -> bool:
    """Delete a follow relationship (unfollow)"""
    
    follow = db.query(UserFollow).filter(
        and_(
            UserFollow.follower_id == follower_id,
            UserFollow.following_id == following_id
        )
    ).first()
    
    if not follow:
        raise HTTPException(status_code=404, detail="Follow relationship not found")
    
    db.delete(follow)
    db.commit()
    
    return True


def check_follow_status(db: Session, user_id: UUID, other_user_id: UUID) -> dict:
    """Check follow status between two users"""
    
    is_following = db.query(UserFollow).filter(
        and_(
            UserFollow.follower_id == user_id,
            UserFollow.following_id == other_user_id
        )
    ).first() is not None
    
    is_followed_by = db.query(UserFollow).filter(
        and_(
            UserFollow.follower_id == other_user_id,
            UserFollow.following_id == user_id
        )
    ).first() is not None
    
    return {
        "is_following": is_following,
        "is_followed_by": is_followed_by,
        "is_mutual": is_following and is_followed_by
    }


# ==================== FOLLOW LISTS ====================

def get_followers(
    db: Session, 
    user_id: UUID, 
    page: int = 1, 
    page_size: int = 20
) -> FollowListResponse:
    """Get list of users following the specified user"""
    
    offset = (page - 1) * page_size
    
    # Get followers with user info
    followers_query = db.query(User).join(
        UserFollow, User.id == UserFollow.follower_id
    ).filter(UserFollow.following_id == user_id)
    
    total_count = followers_query.count()
    followers = followers_query.offset(offset).limit(page_size).all()
    
    # Convert to UserBasicInfo
    users = [UserBasicInfo.model_validate(user) for user in followers]
    
    return FollowListResponse(
        users=users,
        total_count=total_count,
        page=page,
        page_size=page_size,
        has_next=offset + page_size < total_count,
        has_previous=page > 1
    )


def get_following(
    db: Session, 
    user_id: UUID, 
    page: int = 1, 
    page_size: int = 20
) -> FollowListResponse:
    """Get list of users that the specified user is following"""
    
    offset = (page - 1) * page_size
    
    # Get following with user info
    following_query = db.query(User).join(
        UserFollow, User.id == UserFollow.following_id
    ).filter(UserFollow.follower_id == user_id)
    
    total_count = following_query.count()
    following = following_query.offset(offset).limit(page_size).all()
    
    # Convert to UserBasicInfo
    users = [UserBasicInfo.model_validate(user) for user in following]
    
    return FollowListResponse(
        users=users,
        total_count=total_count,
        page=page,
        page_size=page_size,
        has_next=offset + page_size < total_count,
        has_previous=page > 1
    )


def get_mutual_follows(
    db: Session, 
    user_id: UUID, 
    page: int = 1, 
    page_size: int = 20
) -> FollowListResponse:
    """Get list of mutual follows (users who follow each other)"""
    
    offset = (page - 1) * page_size
    
    # Get mutual follows
    mutual_query = db.query(User).join(
        UserFollow, User.id == UserFollow.following_id
    ).filter(
        and_(
            UserFollow.follower_id == user_id,
            UserFollow.following_id.in_(
                db.query(UserFollow.follower_id).filter(
                    UserFollow.following_id == user_id
                )
            )
        )
    )
    
    total_count = mutual_query.count()
    mutual_follows = mutual_query.offset(offset).limit(page_size).all()
    
    # Convert to UserBasicInfo
    users = [UserBasicInfo.model_validate(user) for user in mutual_follows]
    
    return FollowListResponse(
        users=users,
        total_count=total_count,
        page=page,
        page_size=page_size,
        has_next=offset + page_size < total_count,
        has_previous=page > 1
    )


# ==================== FOLLOW STATISTICS ====================

def get_user_follow_stats(db: Session, user_id: UUID) -> UserFollowStats:
    """Get follow statistics for a user"""
    
    # Count followers
    followers_count = db.query(UserFollow).filter(
        UserFollow.following_id == user_id
    ).count()
    
    # Count following
    following_count = db.query(UserFollow).filter(
        UserFollow.follower_id == user_id
    ).count()
    
    # Count mutual follows
    mutual_follows_count = db.query(UserFollow).filter(
        and_(
            UserFollow.follower_id == user_id,
            UserFollow.following_id.in_(
                db.query(UserFollow.follower_id).filter(
                    UserFollow.following_id == user_id
                )
            )
        )
    ).count()
    
    return UserFollowStats(
        user_id=user_id,
        followers_count=followers_count,
        following_count=following_count,
        mutual_follows_count=mutual_follows_count
    )


# ==================== FOLLOW SUGGESTIONS ====================

def get_follow_suggestions(
    db: Session, 
    user_id: UUID, 
    limit: int = 10
) -> List[FollowSuggestion]:
    """Get follow suggestions for a user"""
    
    suggestions = []
    
    # Get users that current user is not following
    not_following_query = db.query(User).filter(
        and_(
            User.id != user_id,
            ~User.id.in_(
                db.query(UserFollow.following_id).filter(
                    UserFollow.follower_id == user_id
                )
            )
        )
    )
    
    # Suggest users with mutual followers
    mutual_followers_query = not_following_query.join(
        UserFollow, User.id == UserFollow.following_id
    ).filter(
        UserFollow.follower_id.in_(
            db.query(UserFollow.following_id).filter(
                UserFollow.follower_id == user_id
            )
        )
    ).group_by(User.id).order_by(
        desc(func.count(UserFollow.follower_id))
    ).limit(limit)
    
    for user in mutual_followers_query.all():
        mutual_count = db.query(UserFollow).filter(
            and_(
                UserFollow.following_id == user.id,
                UserFollow.follower_id.in_(
                    db.query(UserFollow.following_id).filter(
                        UserFollow.follower_id == user_id
                    )
                )
            )
        ).count()
        
        suggestions.append(FollowSuggestion(
            user=UserBasicInfo.model_validate(user),
            reason="mutual_followers",
            mutual_followers_count=mutual_count
        ))
    
    return suggestions


# ==================== BULK OPERATIONS ====================

def bulk_follow(db: Session, follower_id: UUID, user_ids: List[UUID]) -> dict:
    """Follow multiple users at once"""
    
    successful_follows = []
    failed_follows = []
    
    for user_id in user_ids:
        try:
            follow_data = UserFollowCreate(following_id=user_id)
            create_follow(db, follower_id, follow_data)
            successful_follows.append(user_id)
        except Exception as e:
            failed_follows.append({"user_id": user_id, "error": str(e)})
    
    return {
        "successful_follows": successful_follows,
        "failed_follows": failed_follows,
        "total_processed": len(user_ids),
        "success_count": len(successful_follows),
        "failure_count": len(failed_follows)
    }


def bulk_unfollow(db: Session, follower_id: UUID, user_ids: List[UUID]) -> dict:
    """Unfollow multiple users at once"""
    
    successful_unfollows = []
    failed_unfollows = []
    
    for user_id in user_ids:
        try:
            delete_follow(db, follower_id, user_id)
            successful_unfollows.append(user_id)
        except Exception as e:
            failed_unfollows.append({"user_id": user_id, "error": str(e)})
    
    return {
        "successful_unfollows": successful_unfollows,
        "failed_unfollows": failed_unfollows,
        "total_processed": len(user_ids),
        "success_count": len(successful_unfollows),
        "failure_count": len(failed_unfollows)
    }
