"""
Routes for Institute-Mentor Collaborations
Institute perspective for managing collaborations and invitations
"""

from typing import Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from uuid import UUID

# Import CRUD functions
from Cruds.Mentors.CollaborationCrud import (
    create_collaboration, get_collaboration_by_id, update_collaboration,
    delete_collaboration, list_collaborations
)
from Cruds.Institute.Mentor import get_mentors

# Import Schemas
from Schemas.Mentors.MentorInstitutes import (
    CollaborationCreate, CollaborationUpdate, CollaborationDetails,
    CollaborationListResponse
)
from Schemas.Mentors.Mentor import MentorListResponse

# Import dependencies
from config.session import get_db
from config.security import oauth2_scheme
from config.deps import get_current_user
from config.permission import require_type

router = APIRouter()


# === INSTITUTE COLLABORATION MANAGEMENT ===

@router.get("/collaborations", response_model=CollaborationListResponse)
def list_institute_collaborations(
    status_filter: Optional[str] = Query(None, description="Filter by status"),
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """List all collaborations for the institute"""
    current_user = get_current_user(token, db)
    
    return list_collaborations(
        db=db,
        user_id=current_user.id,
        user_type="institute",
        status_filter=status_filter,
        page=page,
        size=size
    )


@router.get("/collaborations/{collaboration_id}", response_model=CollaborationDetails)
def get_institute_collaboration(
    collaboration_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """Get specific collaboration details"""
    current_user = get_current_user(token, db)
    collaboration = get_collaboration_by_id(db, collaboration_id)
    
    if not collaboration:
        raise HTTPException(status_code=404, detail="Collaboration not found")
    
    # Check if institute has access to this collaboration
    if collaboration.institute.id != current_user.id:
        raise HTTPException(status_code=403, detail="Access denied")
    
    return collaboration


@router.put("/collaborations/{collaboration_id}", response_model=CollaborationDetails)
def update_institute_collaboration(
    collaboration_id: UUID,
    update_data: CollaborationUpdate,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """Update collaboration details from institute side"""
    current_user = get_current_user(token, db)
    
    # Check access first
    collaboration = get_collaboration_by_id(db, collaboration_id)
    if not collaboration:
        raise HTTPException(status_code=404, detail="Collaboration not found")
    
    if collaboration.institute.id != current_user.id:
        raise HTTPException(status_code=403, detail="Access denied")
    
    return update_collaboration(db, collaboration_id, update_data)


@router.post("/collaborations/{collaboration_id}/end")
def end_collaboration(
    collaboration_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """End collaboration from institute side"""
    current_user = get_current_user(token, db)
    
    # Check access first
    collaboration = get_collaboration_by_id(db, collaboration_id)
    if not collaboration:
        raise HTTPException(status_code=404, detail="Collaboration not found")
    
    if collaboration.institute.id != current_user.id:
        raise HTTPException(status_code=403, detail="Access denied")
    
    # Update status to ended instead of deleting
    from datetime import datetime, timezone
    update_data = CollaborationUpdate(
        status="ended",
        end_date=datetime.now(timezone.utc)
    )
    
    return update_collaboration(db, collaboration_id, update_data)



# === MENTOR DISCOVERY FOR INSTITUTES ===

@router.get("/mentors/search")
def search_mentors_for_collaboration(
    subject: Optional[str] = Query(None, description="Filter by subject expertise"),
    min_experience: Optional[int] = Query(None, description="Minimum years of experience"),
    max_hourly_rate: Optional[float] = Query(None, description="Maximum hourly rate"),
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """Search for mentors available for collaboration"""
    current_user = get_current_user(token, db)
    
    # Import mentor search function
    from Cruds.Institute.Mentor import get_mentors
    
    return get_mentors(
        db=db,
        page=page,
        size=size,
        subject_filter=subject,
        min_experience=min_experience,
        max_hourly_rate=max_hourly_rate
    )


@router.get("/mentors/{mentor_id}")
def get_mentor_details_for_institute(
    mentor_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """Get detailed mentor information for collaboration consideration"""
    current_user = get_current_user(token, db)
    
    from Cruds.Institute.Mentor import get_mentor_with_profile_by_id
    mentor = get_mentor_with_profile_by_id(db, mentor_id)
    
    if not mentor:
        raise HTTPException(status_code=404, detail="Mentor not found")
    
    return mentor
