"""
Main Event Management Router for EduFair Platform

This module combines all event-related routes and provides a unified entry point
for event management across all user types.
"""

from fastapi import APIRouter

# Import all event-related routers
from Routes.Users.EventDashboard import router as user_dashboard_router
from Routes.Users.EventActions import router as user_actions_router
from Routes.Institute.EventControl import router as institute_control_router
from Routes.Universal.EventAccess import router as universal_access_router

# Create main router
main_router = APIRouter()

# Include all sub-routers with appropriate prefixes and tags

# Universal access routes (works for all user types)
main_router.include_router(
    universal_access_router,
    prefix="/universal",
    tags=["Universal Event Access"],
    responses={
        404: {"description": "Not found"},
        403: {"description": "Not authorized"},
        401: {"description": "Authentication required"}
    }
)

# User-specific event routes
main_router.include_router(
    user_dashboard_router,
    prefix="/users",
    tags=["User Event Dashboard"],
    responses={
        404: {"description": "Not found"},
        403: {"description": "Not authorized"},
        401: {"description": "Authentication required"}
    }
)

# User event actions (cancel, download tickets, etc.)
main_router.include_router(
    user_actions_router,
    prefix="/users",
    tags=["User Event Actions"],
    responses={
        404: {"description": "Not found"},
        403: {"description": "Not authorized"},
        401: {"description": "Authentication required"}
    }
)

# Institute event control routes
main_router.include_router(
    institute_control_router,
    prefix="/institute",
    tags=["Institute Event Control"],
    responses={
        404: {"description": "Not found"},
        403: {"description": "Not authorized - Institute access required"},
        401: {"description": "Authentication required"}
    }
)


# ==================== ROUTE DOCUMENTATION ====================

@main_router.get("/routes/documentation")
def get_event_routes_documentation():
    """
    Get comprehensive documentation of all available event management routes.
    
    Provides guidance on which routes to use based on user type and use case.
    """
    
    return {
        "event_management_api": {
            "version": "1.0.0",
            "description": "Comprehensive event management system for EduFair platform",
            "base_url": "/api/events",
            
            "route_categories": {
                "universal_access": {
                    "prefix": "/universal",
                    "description": "Routes that work for all user types with appropriate filtering",
                    "key_endpoints": [
                        "GET /universal/my/events/dashboard - Universal event dashboard",
                        "GET /universal/my/events - List user's events with filtering",
                        "GET /universal/my/events/stats - Event participation statistics",
                        "GET /universal/my/events/{registration_id} - Event details",
                        "GET /universal/my/events/guidance - User type specific guidance"
                    ],
                    "supported_user_types": ["student", "teacher", "sponsor", "institute"]
                },
                
                "user_dashboard": {
                    "prefix": "/users",
                    "description": "Detailed user event dashboard and history",
                    "key_endpoints": [
                        "GET /users/dashboard - Comprehensive dashboard",
                        "GET /users/events - Paginated event list",
                        "GET /users/events/upcoming - Upcoming events",
                        "GET /users/events/past - Past events",
                        "GET /users/stats - Detailed statistics"
                    ],
                    "supported_user_types": ["student", "teacher", "sponsor"]
                },
                
                "user_actions": {
                    "prefix": "/users",
                    "description": "User actions on their event registrations",
                    "key_endpoints": [
                        "POST /users/registrations/{id}/cancel - Cancel registration",
                        "GET /users/registrations/{id}/ticket - Download ticket",
                        "GET /users/history - Complete event history",
                        "GET /users/registrations/{id}/status - Registration status"
                    ],
                    "supported_user_types": ["student", "teacher", "sponsor"]
                },
                
                "institute_control": {
                    "prefix": "/institute",
                    "description": "Complete event management for institutes",
                    "key_endpoints": [
                        "GET /institute/events/{id}/registrations - Manage registrations",
                        "POST /institute/events/{id}/registrations/control - Bulk actions",
                        "GET /institute/events/{id}/analytics - Event analytics",
                        "POST /institute/events/{id}/cancel - Cancel event",
                        "POST /institute/events/{id}/registrations/{id}/refund - Process refund",
                        "POST /institute/events/{id}/registrations/{id}/transfer - Transfer ticket"
                    ],
                    "supported_user_types": ["institute"]
                }
            },
            
            "user_type_recommendations": {
                "student": {
                    "primary_routes": [
                        "/universal/my/events/dashboard",
                        "/users/events/upcoming",
                        "/users/registrations/{id}/cancel",
                        "/users/registrations/{id}/ticket"
                    ],
                    "description": "Focus on personal event management and participation"
                },
                
                "teacher": {
                    "primary_routes": [
                        "/universal/my/events/dashboard",
                        "/users/events",
                        "/users/stats",
                        "/users/history"
                    ],
                    "description": "Professional development tracking and event participation"
                },
                
                "sponsor": {
                    "primary_routes": [
                        "/universal/my/events/dashboard",
                        "/universal/my/events/stats",
                        "/users/events"
                    ],
                    "description": "Sponsorship tracking and event participation management"
                },
                
                "institute": {
                    "primary_routes": [
                        "/institute/events/{id}/registrations",
                        "/institute/events/{id}/analytics",
                        "/institute/events/{id}/registrations/control",
                        "/institute/events/{id}/cancel"
                    ],
                    "description": "Complete event organization and management capabilities"
                }
            },
            
            "common_workflows": {
                "user_event_participation": [
                    "1. GET /universal/my/events/dashboard - View dashboard",
                    "2. GET /users/events/upcoming - Check upcoming events",
                    "3. GET /users/registrations/{id}/ticket - Download tickets",
                    "4. POST /users/registrations/{id}/cancel - Cancel if needed"
                ],
                
                "institute_event_management": [
                    "1. GET /institute/events/{id}/registrations - View registrations",
                    "2. GET /institute/events/{id}/analytics - Check analytics",
                    "3. POST /institute/events/{id}/registrations/control - Manage registrations",
                    "4. POST /institute/events/{id}/registrations/{id}/refund - Process refunds"
                ],
                
                "event_discovery": [
                    "1. GET /universal/my/events/guidance - Get user type guidance",
                    "2. GET /universal/my/events/summary - Quick overview",
                    "3. GET /universal/my/events - Detailed event list",
                    "4. GET /universal/my/events/{id} - Event details"
                ]
            },
            
            "authentication": {
                "required": True,
                "method": "Bearer Token",
                "header": "Authorization: Bearer <token>",
                "note": "All endpoints require valid authentication token"
            },
            
            "permissions": {
                "universal_routes": "Available to all authenticated users",
                "user_routes": "Available to students, teachers, and sponsors",
                "institute_routes": "Available only to institute users",
                "admin_routes": "Available only to admin users (in admin endpoints)"
            },
            
            "response_formats": {
                "success": "JSON with appropriate schema",
                "error": "JSON with error details and HTTP status codes",
                "pagination": "Includes total_count, page, per_page, has_next, has_prev",
                "filtering": "Supports multiple filter parameters and search"
            }
        }
    }


@main_router.get("/health")
def event_management_health_check():
    """
    Health check endpoint for event management system.
    """
    return {
        "status": "healthy",
        "service": "Event Management API",
        "version": "1.0.0",
        "available_routes": {
            "universal": "✓ Available",
            "users": "✓ Available", 
            "institute": "✓ Available",
            "documentation": "✓ Available"
        },
        "features": {
            "user_dashboard": "✓ Enabled",
            "event_actions": "✓ Enabled",
            "institute_control": "✓ Enabled",
            "universal_access": "✓ Enabled",
            "refund_processing": "✓ Enabled",
            "ticket_transfers": "✓ Enabled",
            "analytics": "✓ Enabled"
        }
    }
