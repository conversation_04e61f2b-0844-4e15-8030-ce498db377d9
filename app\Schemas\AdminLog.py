"""
Admin Log Schemas for EduFair Platform

This module contains Pydantic schemas for admin logging functionality.
"""

from pydantic import BaseModel, Field
from uuid import UUID
from datetime import datetime
from typing import Optional, List, Dict, Any
from enum import Enum


# ==================== LOG ACTIONS ====================

class LogAction(str, Enum):
    """Admin log actions"""
    # User actions
    USER_CREATE = "USER_CREATE"
    USER_UPDATE = "USER_UPDATE"
    USER_DELETE = "USER_DELETE"
    USER_LOGIN = "USER_LOGIN"
    USER_LOGOUT = "USER_LOGOUT"
    USER_VERIFY = "USER_VERIFY"
    USER_SUSPEND = "USER_SUSPEND"
    USER_ACTIVATE = "USER_ACTIVATE"
    
    # Event actions
    EVENT_CREATE = "EVENT_CREATE"
    EVENT_UPDATE = "EVENT_UPDATE"
    EVENT_DELETE = "EVENT_DELETE"
    EVENT_PUBLISH = "EVENT_PUBLISH"
    EVENT_CANCEL = "EVENT_CANCEL"
    
    # Registration actions
    REGISTRATION_CREATE = "REGISTRATION_CREATE"
    REGISTRATION_CANCEL = "REGISTRATION_CANCEL"
    REGISTRATION_CONFIRM = "REGISTRATION_CONFIRM"
    
    # Follow actions
    FOLLOW_CREATE = "FOLLOW_CREATE"
    FOLLOW_DELETE = "FOLLOW_DELETE"
    
    # Chat actions
    MESSAGE_SEND = "MESSAGE_SEND"
    MESSAGE_DELETE = "MESSAGE_DELETE"
    
    # System actions
    SYSTEM_BACKUP = "SYSTEM_BACKUP"
    SYSTEM_MAINTENANCE = "SYSTEM_MAINTENANCE"
    SYSTEM_UPDATE = "SYSTEM_UPDATE"
    
    # Admin actions
    ADMIN_ACCESS = "ADMIN_ACCESS"
    ADMIN_EXPORT = "ADMIN_EXPORT"
    ADMIN_IMPORT = "ADMIN_IMPORT"


class ResourceType(str, Enum):
    """Resource types for logging"""
    USER = "user"
    EVENT = "event"
    REGISTRATION = "registration"
    FOLLOW = "follow"
    MESSAGE = "message"
    NOTIFICATION = "notification"
    SUBSCRIPTION = "subscription"
    TASK = "task"
    EXAM = "exam"
    INSTITUTE = "institute"
    MENTOR = "mentor"
    SYSTEM = "system"


# ==================== BASE SCHEMAS ====================

class AdminLogBase(BaseModel):
    """Base schema for admin log"""
    action: LogAction
    resource_type: ResourceType
    resource_id: Optional[UUID] = None
    details: Optional[Dict[str, Any]] = None


class AdminLogCreate(AdminLogBase):
    """Schema for creating an admin log entry"""
    user_id: Optional[UUID] = None
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None


# ==================== RESPONSE SCHEMAS ====================

class AdminLogResponse(BaseModel):
    """Schema for admin log response"""
    id: UUID
    user_id: Optional[UUID] = None
    action: str
    resource_type: str
    resource_id: Optional[UUID] = None
    details: Optional[Dict[str, Any]] = None
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None
    timestamp: datetime
    
    class Config:
        from_attributes = True


class AdminLogWithUser(AdminLogResponse):
    """Admin log with user information"""
    username: Optional[str] = None
    user_email: Optional[str] = None
    user_type: Optional[str] = None


# ==================== LIST AND FILTER SCHEMAS ====================

class AdminLogFilter(BaseModel):
    """Filter options for admin logs"""
    user_id: Optional[UUID] = None
    action: Optional[LogAction] = None
    resource_type: Optional[ResourceType] = None
    resource_id: Optional[UUID] = None
    date_from: Optional[datetime] = None
    date_to: Optional[datetime] = None
    ip_address: Optional[str] = None
    search_query: Optional[str] = None


class AdminLogListRequest(BaseModel):
    """Request for admin log list"""
    filters: Optional[AdminLogFilter] = None
    page: int = Field(1, ge=1)
    page_size: int = Field(50, ge=1, le=1000)
    sort_by: str = Field("timestamp", description="Field to sort by")
    sort_order: str = Field("desc", description="Sort order: asc or desc")


class AdminLogListResponse(BaseModel):
    """Response for admin log list"""
    logs: List[AdminLogWithUser]
    total_count: int
    page: int
    page_size: int
    has_next: bool
    has_previous: bool
    filters_applied: Optional[AdminLogFilter] = None


# ==================== STATISTICS SCHEMAS ====================

class AdminLogStats(BaseModel):
    """Admin log statistics"""
    total_logs: int = 0
    logs_today: int = 0
    logs_this_week: int = 0
    logs_this_month: int = 0
    most_active_user: Optional[str] = None
    most_common_action: Optional[str] = None
    most_affected_resource: Optional[str] = None


class ActionStats(BaseModel):
    """Statistics for specific action"""
    action: str
    count: int
    percentage: float
    last_occurrence: Optional[datetime] = None


class ResourceStats(BaseModel):
    """Statistics for specific resource type"""
    resource_type: str
    count: int
    percentage: float
    most_common_action: Optional[str] = None


class UserActivityStats(BaseModel):
    """User activity statistics"""
    user_id: UUID
    username: str
    total_actions: int
    actions_today: int
    actions_this_week: int
    actions_this_month: int
    most_common_action: Optional[str] = None
    last_activity: Optional[datetime] = None


class AdminLogDashboard(BaseModel):
    """Admin log dashboard data"""
    overview: AdminLogStats
    top_actions: List[ActionStats]
    top_resources: List[ResourceStats]
    most_active_users: List[UserActivityStats]
    recent_logs: List[AdminLogWithUser]


# ==================== EXPORT SCHEMAS ====================

class AdminLogExportRequest(BaseModel):
    """Request for exporting admin logs"""
    filters: Optional[AdminLogFilter] = None
    format: str = Field("csv", description="Export format: csv, json, xlsx")
    include_user_details: bool = True
    date_range_required: bool = True


class AdminLogExportResponse(BaseModel):
    """Response for admin log export"""
    export_id: UUID
    status: str  # "processing", "completed", "failed"
    download_url: Optional[str] = None
    file_size: Optional[int] = None
    record_count: int
    created_at: datetime
    expires_at: Optional[datetime] = None


# ==================== AUDIT TRAIL SCHEMAS ====================

class AuditTrailRequest(BaseModel):
    """Request for audit trail of specific resource"""
    resource_type: ResourceType
    resource_id: UUID
    include_related: bool = False


class AuditTrailResponse(BaseModel):
    """Response for audit trail"""
    resource_type: str
    resource_id: UUID
    audit_logs: List[AdminLogWithUser]
    total_changes: int
    first_change: Optional[datetime] = None
    last_change: Optional[datetime] = None
    change_frequency: float = 0.0  # Changes per day


# ==================== SECURITY MONITORING ====================

class SecurityEvent(BaseModel):
    """Security-related event"""
    event_type: str  # "suspicious_login", "multiple_failures", "unusual_activity"
    severity: str    # "low", "medium", "high", "critical"
    description: str
    user_id: Optional[UUID] = None
    ip_address: Optional[str] = None
    timestamp: datetime
    details: Optional[Dict[str, Any]] = None


class SecurityAlert(BaseModel):
    """Security alert"""
    alert_id: UUID
    alert_type: str
    severity: str
    title: str
    description: str
    affected_users: List[UUID] = []
    related_logs: List[UUID] = []
    status: str = "active"  # "active", "investigating", "resolved"
    created_at: datetime
    resolved_at: Optional[datetime] = None


class SecurityDashboard(BaseModel):
    """Security monitoring dashboard"""
    active_alerts: List[SecurityAlert]
    recent_security_events: List[SecurityEvent]
    failed_login_attempts: int = 0
    suspicious_activities: int = 0
    blocked_ips: List[str] = []
    security_score: float = 100.0  # 0-100 security score
