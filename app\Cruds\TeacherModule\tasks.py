from uuid import uuid4, UUID
from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError
from fastapi import HTTPEx<PERSON>, UploadFile
from typing import List, Optional
from datetime import datetime
import json

# Import Models
from Models.Tasks import Task, TaskAttachment, StudentTaskAttachment, TaskStudents, TaskClassroom, TaskClassroomStudent, TaskChapter, TaskTopic, TaskSubTopic, TaskStatus
from Models.Chapter import Chapter, Topic, SubTopic
from Models.users import Subject, User
from Models.Classroom import Classroom, StudentClassroom

# Import Schemas
from Schemas.TeacherModule.tasks import (
    MyTasksMinimal, StudentTaskDetailedOut, StudentTaskMinimalOut, StudentTaskOut, MyTasks, TaskCreate, TaskOut, TaskUpdate, TaskListOut, TaskWithDetailsOut,
    SubjectCreate, SubjectOut, SubjectUpdate, SubjectListOut, SubjectWithHierarchyOut,
    ChapterCreate, ChapterOut, ChapterUpdate, ChapterListOut, ChapterWithTopicsOut,
    TopicCreate, TopicOut, TopicUpdate, TopicListOut, TopicWithSubTopicsOut,
    SubTopicCreate, SubTopicOut, SubTopicUpdate, SubTopicListOut,
    TaskAttachmentOut, StudentTaskAttachmentOut, TaskStudentOut, TaskClassroomOut, TaskClassroomStudentOut,
    TaskChapterOut, TaskTopicOut, TaskSubTopicOut, TaskCreateWithAssignments,
    TaskCreateForStudent, TaskCreateForClassroom, TaskCreateForMultipleStudents, TaskCreateForMultipleClassrooms,
    TaskSubmissionCreate, TaskSubmissionOut, TaskSubmissionDetailOut, TaskGradeCreate, TaskGradeOut, StudentGradeOut, StudentGradesListOut, TaskAttachmentUploadOut,
    TaskEditDetailsOut, ClassroomAssignmentOut, StudentAssignmentOut, TaskEditUpdate, TeacherTaskMinimalOut, TeacherTaskListOut,
    StudentSubmissionOut, TaskSubmissionsListOut, StudentSubmissionDetailOut, BatchGradeCreate, BatchGradeOut, SubmissionFilterParams,
    TaskCreateWithFilesResponse, TaskSubmissionWithFilesResponse
)

# Import file storage service
from services.file_storage import file_storage
from config.config import settings


# ==================== SUBJECT CRUD ====================

def create_subject(db: Session, subject_create: SubjectCreate) -> SubjectOut:
    """Create a new subject"""
    try:
        # Check if subject with same name already exists
        existing_subject = db.query(Subject).filter(Subject.name == subject_create.name).first()
        if existing_subject:
            raise HTTPException(status_code=400, detail="Subject with this name already exists.")

        new_subject = Subject(
            id=uuid4(),
            name=subject_create.name
        )
        db.add(new_subject)
        db.commit()
        db.refresh(new_subject)
        return SubjectOut.model_validate(new_subject)

    except HTTPException:
        db.rollback()
        raise
    except SQLAlchemyError as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Database error occurred: {e}")
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Unexpected error occurred: {e}")


def get_subject_by_id(db: Session, subject_id: UUID) -> SubjectOut:
    """Get subject by ID"""
    subject = db.query(Subject).filter(Subject.id == subject_id).first()
    if not subject:
        raise HTTPException(status_code=404, detail="Subject not found.")
    return SubjectOut.model_validate(subject)


def get_all_subjects(db: Session, skip: int = 0, limit: int = 100) -> SubjectListOut:
    """Get all subjects with pagination"""
    subjects = db.query(Subject).offset(skip).limit(limit).all()
    total = db.query(Subject).count()
    return SubjectListOut(
        subjects=[SubjectOut.model_validate(subject) for subject in subjects],
        total=total
    )


def update_subject(db: Session, subject_id: UUID, subject_update: SubjectUpdate) -> SubjectOut:
    """Update subject"""
    try:
        subject = db.query(Subject).filter(Subject.id == subject_id).first()
        if not subject:
            raise HTTPException(status_code=404, detail="Subject not found.")

        if subject_update.name is not None:
            # Check if new name conflicts with existing subject
            existing_subject = db.query(Subject).filter(
                Subject.name == subject_update.name,
                Subject.id != subject_id
            ).first()
            if existing_subject:
                raise HTTPException(status_code=400, detail="Subject with this name already exists.")
            subject.name = subject_update.name

        db.commit()
        db.refresh(subject)
        return SubjectOut.model_validate(subject)

    except HTTPException:
        db.rollback()
        raise
    except SQLAlchemyError as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Database error occurred: {e}")
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Unexpected error occurred: {e}")


def delete_subject(db: Session, subject_id: UUID) -> None:
    """Delete subject"""
    try:
        subject = db.query(Subject).filter(Subject.id == subject_id).first()
        if not subject:
            raise HTTPException(status_code=404, detail="Subject not found.")
        
        db.delete(subject)
        db.commit()

    except HTTPException:
        db.rollback()
        raise
    except SQLAlchemyError as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Database error occurred: {e}")
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Unexpected error occurred: {e}")


def get_subject_with_hierarchy(db: Session, subject_id: UUID) -> SubjectWithHierarchyOut:
    """Get subject with full chapter/topic/subtopic hierarchy"""
    subject = db.query(Subject).filter(Subject.id == subject_id).first()
    if not subject:
        raise HTTPException(status_code=404, detail="Subject not found.")
    return SubjectWithHierarchyOut.model_validate(subject)


# ==================== CHAPTER CRUD ====================

def create_chapter(db: Session, chapter_create: ChapterCreate) -> ChapterOut:
    """Create a new chapter"""
    try:
        # Verify subject exists
        subject = db.query(Subject).filter(Subject.id == chapter_create.subject_id).first()
        if not subject:
            raise HTTPException(status_code=404, detail="Subject not found.")

        new_chapter = Chapter(
            id=uuid4(),
            name=chapter_create.name,
            description=chapter_create.description,
            subject_id=chapter_create.subject_id
        )
        db.add(new_chapter)
        db.commit()
        db.refresh(new_chapter)
        return ChapterOut.from_orm(new_chapter)

    except HTTPException:
        db.rollback()
        raise
    except SQLAlchemyError as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Database error occurred: {e}")
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Unexpected error occurred: {e}")


def get_chapter_by_id(db: Session, chapter_id: UUID) -> ChapterOut:
    """Get chapter by ID"""
    chapter = db.query(Chapter).filter(Chapter.id == chapter_id).first()
    if not chapter:
        raise HTTPException(status_code=404, detail="Chapter not found.")
    return ChapterOut.model_validate(chapter)


def get_chapters_by_subject(db: Session, subject_id: UUID, skip: int = 0, limit: int = 100) -> ChapterListOut:
    """Get chapters by subject ID"""
    chapters = db.query(Chapter).filter(Chapter.subject_id == subject_id).offset(skip).limit(limit).all()
    total = db.query(Chapter).filter(Chapter.subject_id == subject_id).count()
    return ChapterListOut(
        chapters=[ChapterOut.from_orm(chapter) for chapter in chapters],
        total=total
    )


def get_all_chapters(db: Session, skip: int = 0, limit: int = 100) -> ChapterListOut:
    """Get all chapters with pagination"""
    chapters = db.query(Chapter).offset(skip).limit(limit).all()
    total = db.query(Chapter).count()
    return ChapterListOut(
        chapters=[ChapterOut.from_orm(chapter) for chapter in chapters],
        total=total
    )


def update_chapter(db: Session, chapter_id: UUID, chapter_update: ChapterUpdate) -> ChapterOut:
    """Update chapter"""
    try:
        chapter = db.query(Chapter).filter(Chapter.id == chapter_id).first()
        if not chapter:
            raise HTTPException(status_code=404, detail="Chapter not found.")

        if chapter_update.name is not None:
            chapter.name = chapter_update.name
        if chapter_update.description is not None:
            chapter.description = chapter_update.description

        db.commit()
        db.refresh(chapter)
        return ChapterOut.from_orm(chapter)

    except HTTPException:
        db.rollback()
        raise
    except SQLAlchemyError as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Database error occurred: {e}")
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Unexpected error occurred: {e}")


def delete_chapter(db: Session, chapter_id: UUID) -> None:
    """Delete chapter"""
    try:
        chapter = db.query(Chapter).filter(Chapter.id == chapter_id).first()
        if not chapter:
            raise HTTPException(status_code=404, detail="Chapter not found.")
        
        db.delete(chapter)
        db.commit()

    except HTTPException:
        db.rollback()
        raise
    except SQLAlchemyError as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Database error occurred: {e}")
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Unexpected error occurred: {e}")


def get_chapter_with_topics(db: Session, chapter_id: UUID) -> ChapterWithTopicsOut:
    """Get chapter with topics and subtopics"""
    chapter = db.query(Chapter).filter(Chapter.id == chapter_id).first()
    if not chapter:
        raise HTTPException(status_code=404, detail="Chapter not found.")
    return ChapterWithTopicsOut.model_validate(chapter)


# ==================== TOPIC CRUD ====================

def create_topic(db: Session, topic_create: TopicCreate) -> TopicOut:
    """Create a new topic"""
    try:
        # Verify chapter exists
        chapter = db.query(Chapter).filter(Chapter.id == topic_create.chapter_id).first()
        if not chapter:
            raise HTTPException(status_code=404, detail="Chapter not found.")

        new_topic = Topic(
            id=uuid4(),
            name=topic_create.name,
            description=topic_create.description,
            chapter_id=topic_create.chapter_id
        )
        db.add(new_topic)
        db.commit()
        db.refresh(new_topic)
        return TopicOut.from_orm(new_topic)

    except HTTPException:
        db.rollback()
        raise
    except SQLAlchemyError as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Database error occurred: {e}")
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Unexpected error occurred: {e}")


def get_topic_by_id(db: Session, topic_id: UUID) -> TopicOut:
    """Get topic by ID"""
    topic = db.query(Topic).filter(Topic.id == topic_id).first()
    if not topic:
        raise HTTPException(status_code=404, detail="Topic not found.")
    return TopicOut.from_orm(topic)


def get_topics_by_chapter(db: Session, chapter_id: UUID, skip: int = 0, limit: int = 100) -> TopicListOut:
    """Get topics by chapter ID"""
    topics = db.query(Topic).filter(Topic.chapter_id == chapter_id).offset(skip).limit(limit).all()
    total = db.query(Topic).filter(Topic.chapter_id == chapter_id).count()
    return TopicListOut(
        topics=[TopicOut.from_orm(topic) for topic in topics],
        total=total
    )


def get_all_topics(db: Session, skip: int = 0, limit: int = 100) -> TopicListOut:
    """Get all topics with pagination"""
    topics = db.query(Topic).offset(skip).limit(limit).all()
    total = db.query(Topic).count()
    return TopicListOut(
        topics=[TopicOut.from_orm(topic) for topic in topics],
        total=total
    )


def update_topic(db: Session, topic_id: UUID, topic_update: TopicUpdate) -> TopicOut:
    """Update topic"""
    try:
        topic = db.query(Topic).filter(Topic.id == topic_id).first()
        if not topic:
            raise HTTPException(status_code=404, detail="Topic not found.")

        if topic_update.name is not None:
            topic.name = topic_update.name
        if topic_update.description is not None:
            topic.description = topic_update.description

        db.commit()
        db.refresh(topic)
        return TopicOut.from_orm(topic)

    except HTTPException:
        db.rollback()
        raise
    except SQLAlchemyError as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Database error occurred: {e}")
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Unexpected error occurred: {e}")


def delete_topic(db: Session, topic_id: UUID) -> None:
    """Delete topic"""
    try:
        topic = db.query(Topic).filter(Topic.id == topic_id).first()
        if not topic:
            raise HTTPException(status_code=404, detail="Topic not found.")
        
        db.delete(topic)
        db.commit()

    except HTTPException:
        db.rollback()
        raise
    except SQLAlchemyError as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Database error occurred: {e}")
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Unexpected error occurred: {e}")


def get_topic_with_subtopics(db: Session, topic_id: UUID) -> TopicWithSubTopicsOut:
    """Get topic with subtopics"""
    topic = db.query(Topic).filter(Topic.id == topic_id).first()
    if not topic:
        raise HTTPException(status_code=404, detail="Topic not found.")
    return TopicWithSubTopicsOut.model_validate(topic)


# ==================== SUBTOPIC CRUD ====================

def create_subtopic(db: Session, subtopic_create: SubTopicCreate) -> SubTopicOut:
    """Create a new subtopic"""
    try:
        # Verify topic exists
        topic = db.query(Topic).filter(Topic.id == subtopic_create.topic_id).first()
        if not topic:
            raise HTTPException(status_code=404, detail="Topic not found.")

        new_subtopic = SubTopic(
            id=uuid4(),
            name=subtopic_create.name,
            description=subtopic_create.description,
            topic_id=subtopic_create.topic_id
        )
        db.add(new_subtopic)
        db.commit()
        db.refresh(new_subtopic)
        return SubTopicOut.from_orm(new_subtopic)

    except HTTPException:
        db.rollback()
        raise
    except SQLAlchemyError as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Database error occurred: {e}")
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Unexpected error occurred: {e}")


def get_subtopic_by_id(db: Session, subtopic_id: UUID) -> SubTopicOut:
    """Get subtopic by ID"""
    subtopic = db.query(SubTopic).filter(SubTopic.id == subtopic_id).first()
    if not subtopic:
        raise HTTPException(status_code=404, detail="SubTopic not found.")
    return SubTopicOut.from_orm(subtopic)


def get_subtopics_by_topic(db: Session, topic_id: UUID, skip: int = 0, limit: int = 100) -> SubTopicListOut:
    """Get subtopics by topic ID"""
    subtopics = db.query(SubTopic).filter(SubTopic.topic_id == topic_id).offset(skip).limit(limit).all()
    total = db.query(SubTopic).filter(SubTopic.topic_id == topic_id).count()
    return SubTopicListOut(
        subtopics=[SubTopicOut.from_orm(subtopic) for subtopic in subtopics],
        total=total
    )


def get_all_subtopics(db: Session, skip: int = 0, limit: int = 100) -> SubTopicListOut:
    """Get all subtopics with pagination"""
    subtopics = db.query(SubTopic).offset(skip).limit(limit).all()
    total = db.query(SubTopic).count()
    return SubTopicListOut(
        subtopics=[SubTopicOut.from_orm(subtopic) for subtopic in subtopics],
        total=total
    )


def update_subtopic(db: Session, subtopic_id: UUID, subtopic_update: SubTopicUpdate) -> SubTopicOut:
    """Update subtopic"""
    try:
        subtopic = db.query(SubTopic).filter(SubTopic.id == subtopic_id).first()
        if not subtopic:
            raise HTTPException(status_code=404, detail="SubTopic not found.")

        if subtopic_update.name is not None:
            subtopic.name = subtopic_update.name
        if subtopic_update.description is not None:
            subtopic.description = subtopic_update.description

        db.commit()
        db.refresh(subtopic)
        return SubTopicOut.from_orm(subtopic)

    except HTTPException:
        db.rollback()
        raise
    except SQLAlchemyError as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Database error occurred: {e}")
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Unexpected error occurred: {e}")


def delete_subtopic(db: Session, subtopic_id: UUID) -> None:
    """Delete subtopic"""
    try:
        subtopic = db.query(SubTopic).filter(SubTopic.id == subtopic_id).first()
        if not subtopic:
            raise HTTPException(status_code=404, detail="SubTopic not found.")
        
        db.delete(subtopic)
        db.commit()

    except HTTPException:
        db.rollback()
        raise
    except SQLAlchemyError as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Database error occurred: {e}")
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Unexpected error occurred: {e}")


# ==================== TASK CRUD ====================

def create_task(db: Session, task_create: TaskCreate) -> TaskOut:
    """Create a new task"""
    try:
        # Verify subject exists if provided
        if task_create.subject_id:
            subject = db.query(Subject).filter(Subject.id == task_create.subject_id).first()
            if not subject:
                raise HTTPException(status_code=404, detail="Subject not found.")

        # Verify chapters exist if provided
        if task_create.chapter_ids:
            chapters = db.query(Chapter).filter(Chapter.id.in_(task_create.chapter_ids)).all()
            if len(chapters) != len(task_create.chapter_ids):
                raise HTTPException(status_code=404, detail="One or more chapters not found.")

        # Verify topics exist if provided
        if task_create.topic_ids:
            topics = db.query(Topic).filter(Topic.id.in_(task_create.topic_ids)).all()
            if len(topics) != len(task_create.topic_ids):
                raise HTTPException(status_code=404, detail="One or more topics not found.")

        # Verify subtopics exist if provided
        if task_create.subtopic_ids:
            subtopics = db.query(SubTopic).filter(SubTopic.id.in_(task_create.subtopic_ids)).all()
            if len(subtopics) != len(task_create.subtopic_ids):
                raise HTTPException(status_code=404, detail="One or more subtopics not found.")

        new_task = Task(
            id=uuid4(),
            name=task_create.name,
            description=task_create.description,
            status=TaskStatus(task_create.status.value),
            subject_id=task_create.subject_id,
            deadline=task_create.deadline,
            accept_after_deadline=task_create.accept_after_deadline
        )
        db.add(new_task)
        db.flush()

        # Create junction table entries for chapters
        for chapter_id in task_create.chapter_ids:
            task_chapter = TaskChapter(
                task_id=new_task.id,
                chapter_id=chapter_id
            )
            db.add(task_chapter)

        # Create junction table entries for topics
        for topic_id in task_create.topic_ids:
            task_topic = TaskTopic(
                task_id=new_task.id,
                topic_id=topic_id
            )
            db.add(task_topic)

        # Create junction table entries for subtopics
        for subtopic_id in task_create.subtopic_ids:
            task_subtopic = TaskSubTopic(
                task_id=new_task.id,
                subtopic_id=subtopic_id
            )
            db.add(task_subtopic)

        db.flush()
        db.refresh(new_task)
        db.commit()
        return TaskOut.model_validate(new_task)

    except HTTPException:
        db.rollback()
        raise
    except SQLAlchemyError as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Database error occurred: {e}")
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Unexpected error occurred: {e}")


def get_task_by_id(db: Session, task_id: UUID) -> TaskOut:
    """Get task by ID with attachments included"""
    try:
        task = db.query(Task).filter(Task.id == task_id).first()
        if not task:
            raise HTTPException(status_code=404, detail="Task not found.")

        # Get attachments for this task
        task_attachments = db.query(TaskAttachment).filter(TaskAttachment.task_id == task_id).all()

        # Convert task to dict and add attachments
        task_data = TaskOut.model_validate(task)
        task_dict = task_data.dict()
        task_dict['attachments'] = [TaskAttachmentOut.model_validate(att) for att in task_attachments]

        return TaskOut(**task_dict)

    except SQLAlchemyError as e:
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")


def get_task_with_details(db: Session, task_id: UUID) -> TaskWithDetailsOut:
    """Get a task with all its details including attachments"""
    try:
        task = db.query(Task).filter(Task.id == task_id).first()
        if not task:
            raise HTTPException(status_code=404, detail="Task not found.")

        # Get all attachments
        task_attachments = db.query(TaskAttachment).filter(TaskAttachment.task_id == task_id).all()
        student_attachments = db.query(StudentTaskAttachment).filter(StudentTaskAttachment.task_id == task_id).all()

        # Convert to TaskWithDetailsOut
        task_data = TaskOut.model_validate(task)

        return TaskWithDetailsOut(
            **task_data.dict(),
            teacher_attachments=[TaskAttachmentOut.model_validate(att) for att in task_attachments],
            student_attachments=[StudentTaskAttachmentOut.model_validate(att) for att in student_attachments]
        )

    except SQLAlchemyError as e:
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")


def get_task_for_editing(db: Session, task_id: UUID) -> TaskEditDetailsOut:
    """Get task details specifically formatted for teacher editing with assignment information"""
    try:
        task = db.query(Task).filter(Task.id == task_id).first()
        if not task:
            raise HTTPException(status_code=404, detail="Task not found.")

        # Get classroom assignments
        task_classrooms = db.query(TaskClassroom).filter(TaskClassroom.task_id == task_id).all()
        classroom_assignments = []
        for tc in task_classrooms:
            if tc.classroom:
                classroom_assignments.append(ClassroomAssignmentOut(
                    classroom_id=tc.classroom_id,
                    classroom_name=tc.classroom.name
                ))

        # Get individual student assignments
        task_students = db.query(TaskStudents).filter(TaskStudents.task_id == task_id).all()
        student_assignments = []
        for ts in task_students:
            if ts.student:
                student_assignments.append(StudentAssignmentOut(
                    student_id=ts.student_id,
                    student_name=f"{ts.student.first_name} {ts.student.last_name}",
                    student_email=ts.student.email
                ))

        # Determine assignment type
        assignment_type = "individual"
        if classroom_assignments and student_assignments:
            assignment_type = "mixed"
        elif classroom_assignments:
            assignment_type = "classroom"

        # Get teacher attachments only
        teacher_attachments = db.query(TaskAttachment).filter(
            TaskAttachment.task_id == task_id,
            TaskAttachment.student_id.is_(None)  # Teacher attachments have null student_id
        ).all()

        # Get chapters, topics, subtopics
        task_chapters = db.query(TaskChapter).filter(TaskChapter.task_id == task_id).all()
        chapters = []
        for tc in task_chapters:
            if tc.chapter:
                chapters.append(TaskChapterOut.model_validate(tc.chapter))

        task_topics = db.query(TaskTopic).filter(TaskTopic.task_id == task_id).all()
        topics = []
        for tt in task_topics:
            if tt.topic:
                topics.append(TaskTopicOut.model_validate(tt.topic))

        task_subtopics = db.query(TaskSubTopic).filter(TaskSubTopic.task_id == task_id).all()
        subtopics = []
        for ts in task_subtopics:
            if ts.subtopic:
                subtopics.append(TaskSubTopicOut.model_validate(ts.subtopic))

        return TaskEditDetailsOut(
            id=task.id,
            name=task.name,
            description=task.description,
            status=task.status,
            deadline=task.deadline,
            accept_after_deadline=task.accept_after_deadline,
            created_at=task.created_at,
            updated_at=task.updated_at,
            subject_id=task.subject_id,
            subject=SubjectOut.model_validate(task.subject) if task.subject else None,
            assigned_to_classrooms=classroom_assignments,
            assigned_to_students=student_assignments,
            assignment_type=assignment_type,
            teacher_attachments=[TaskAttachmentOut.model_validate(att) for att in teacher_attachments],
            chapters=chapters,
            topics=topics,
            subtopics=subtopics
        )

    except SQLAlchemyError as e:
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")


def get_tasks_by_subject(db: Session, subject_id: UUID, skip: int = 0, limit: int = 100) -> TaskListOut:
    """Get tasks by subject ID"""
    tasks = db.query(Task).filter(Task.subject_id == subject_id).offset(skip).limit(limit).all()
    total = db.query(Task).filter(Task.subject_id == subject_id).count()
    return TaskListOut(
        tasks=[TaskOut.from_orm(task) for task in tasks],
        total=total
    )


def get_tasks_by_chapter(db: Session, chapter_id: UUID, skip: int = 0, limit: int = 100) -> TaskListOut:
    """Get tasks by chapter ID through junction table"""
    task_chapters = db.query(TaskChapter).filter(TaskChapter.chapter_id == chapter_id).offset(skip).limit(limit).all()
    task_ids = [tc.task_id for tc in task_chapters]
    tasks = db.query(Task).filter(Task.id.in_(task_ids)).all()
    total = db.query(TaskChapter).filter(TaskChapter.chapter_id == chapter_id).count()
    return TaskListOut(
        tasks=[TaskOut.from_orm(task) for task in tasks],
        total=total
    )


def get_tasks_by_topic(db: Session, topic_id: UUID, skip: int = 0, limit: int = 100) -> TaskListOut:
    """Get tasks by topic ID through junction table"""
    task_topics = db.query(TaskTopic).filter(TaskTopic.topic_id == topic_id).offset(skip).limit(limit).all()
    task_ids = [tt.task_id for tt in task_topics]
    tasks = db.query(Task).filter(Task.id.in_(task_ids)).all()
    total = db.query(TaskTopic).filter(TaskTopic.topic_id == topic_id).count()
    return TaskListOut(
        tasks=[TaskOut.from_orm(task) for task in tasks],
        total=total
    )


def get_tasks_by_subtopic(db: Session, subtopic_id: UUID, skip: int = 0, limit: int = 100) -> TaskListOut:
    """Get tasks by subtopic ID through junction table"""
    task_subtopics = db.query(TaskSubTopic).filter(TaskSubTopic.subtopic_id == subtopic_id).offset(skip).limit(limit).all()
    task_ids = [ts.task_id for ts in task_subtopics]
    tasks = db.query(Task).filter(Task.id.in_(task_ids)).all()
    total = db.query(TaskSubTopic).filter(TaskSubTopic.subtopic_id == subtopic_id).count()
    return TaskListOut(
        tasks=[TaskOut.from_orm(task) for task in tasks],
        total=total
    )


def get_all_tasks(db: Session, skip: int = 0, limit: int = 100) -> TaskListOut:
    """Get all tasks with pagination"""
    tasks = db.query(Task).offset(skip).limit(limit).all()
    total = db.query(Task).count()
    return TaskListOut(
        tasks=[TaskOut.from_orm(task) for task in tasks],
        total=total
    )


def get_all_tasks_minimal_for_teacher(
    db: Session,
    subject_id: Optional[UUID] = None,
    chapter_id: Optional[UUID] = None,
    topic_id: Optional[UUID] = None,
    subtopic_id: Optional[UUID] = None,
    skip: int = 0,
    limit: int = 100
) -> TeacherTaskListOut:
    """Get minimal task information for teachers with filtering - optimized for performance"""
    try:
        # Build the query with filters
        query = db.query(Task)

        if subject_id is not None:
            query = query.filter(Task.subject_id == subject_id)
        if chapter_id is not None:
            query = query.join(TaskChapter).filter(TaskChapter.chapter_id == chapter_id)
        if topic_id is not None:
            query = query.join(TaskTopic).filter(TaskTopic.topic_id == topic_id)
        if subtopic_id is not None:
            query = query.join(TaskSubTopic).filter(TaskSubTopic.subtopic_id == subtopic_id)

        # Get tasks with pagination
        tasks = query.offset(skip).limit(limit).all()
        total = query.count()

        # Build minimal task objects
        task_objects = []
        for task in tasks:
            # Get assignment counts
            classroom_count = db.query(TaskClassroom).filter(TaskClassroom.task_id == task.id).count()
            student_count = db.query(TaskStudents).filter(TaskStudents.task_id == task.id).count()

            # Determine assignment type
            assignment_type = "none"
            if classroom_count > 0 and student_count > 0:
                assignment_type = "mixed"
            elif classroom_count > 0:
                assignment_type = "classroom"
            elif student_count > 0:
                assignment_type = "individual"

            # Get submission counts
            total_submissions = db.query(TaskStudents).filter(
                TaskStudents.task_id == task.id,
                TaskStudents.submission_date.isnot(None)
            ).count()

            pending_submissions = student_count - total_submissions

            # Check if task is fully graded
            is_graded = _is_task_fully_graded(db, task.id)

            # Create minimal task object
            task_minimal = TeacherTaskMinimalOut(
                id=task.id,
                name=task.name,
                description=task.description,
                status=task.status,
                graded=is_graded,
                deadline=task.deadline,
                accept_after_deadline=task.accept_after_deadline,
                created_at=task.created_at,
                updated_at=task.updated_at,
                subject_name=task.subject.name if task.subject else None,
                assignment_type=assignment_type,
                total_assigned_students=student_count,
                total_assigned_classrooms=classroom_count,
                total_submissions=total_submissions,
                pending_submissions=max(0, pending_submissions)
            )
            task_objects.append(task_minimal)

        return TeacherTaskListOut(tasks=task_objects, total=total)

    except SQLAlchemyError as e:
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")


def update_task(db: Session, task_id: UUID, task_update: TaskUpdate) -> TaskOut:
    """Update task"""
    try:
        with db.begin():
            task = db.query(Task).filter(Task.id == task_id).first()
            if not task:
                raise HTTPException(status_code=404, detail="Task not found.")

            # Verify subject exists if provided
            if task_update.subject_id is not None:
                if task_update.subject_id:
                    subject = db.query(Subject).filter(Subject.id == task_update.subject_id).first()
                    if not subject:
                        raise HTTPException(status_code=404, detail="Subject not found.")
                task.subject_id = task_update.subject_id

            if task_update.name is not None:
                task.name = task_update.name
            if task_update.description is not None:
                task.description = task_update.description
            if task_update.status is not None:
                task.status = TaskStatus(task_update.status.value)
            if task_update.deadline is not None:
                task.deadline = task_update.deadline
            if task_update.accept_after_deadline is not None:
                task.accept_after_deadline = task_update.accept_after_deadline

            db.flush()
            db.refresh(task)
            return TaskOut.from_orm(task)

    except HTTPException:
        raise
    except SQLAlchemyError as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Database error occurred: {e}")
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Unexpected error occurred: {e}")


def update_task_for_teacher(db: Session, task_id: UUID, task_update: TaskEditUpdate) -> TaskEditDetailsOut:
    """Enhanced task update function for teachers with assignment management"""
    try:
        # Verify task exists
        task = db.query(Task).filter(Task.id == task_id).first()
        if not task:
            raise HTTPException(status_code=404, detail="Task not found.")

        # Update basic task fields
        if task_update.deadline is not None:
            task.deadline = task_update.deadline
        if task_update.accept_after_deadline is not None:
            task.accept_after_deadline = task_update.accept_after_deadline

        # Handle classroom assignments
        if task_update.add_classroom_ids:
            # Verify classrooms exist
            classrooms = db.query(Classroom).filter(Classroom.id.in_(task_update.add_classroom_ids)).all()
            if len(classrooms) != len(task_update.add_classroom_ids):
                raise HTTPException(status_code=404, detail="One or more classrooms not found.")

            # Check for existing assignments
            existing_assignments = db.query(TaskClassroom).filter(
                TaskClassroom.task_id == task_id,
                TaskClassroom.classroom_id.in_(task_update.add_classroom_ids)
            ).all()
            if existing_assignments:
                existing_ids = [ea.classroom_id for ea in existing_assignments]
                raise HTTPException(status_code=400, detail=f"Task already assigned to classrooms: {existing_ids}")

            # Add new classroom assignments
            for classroom_id in task_update.add_classroom_ids:
                task_classroom = TaskClassroom(
                    task_id=task_id,
                    classroom_id=classroom_id
                )
                db.add(task_classroom)

        if task_update.remove_classroom_ids:
            # Remove classroom assignments
            db.query(TaskClassroom).filter(
                TaskClassroom.task_id == task_id,
                TaskClassroom.classroom_id.in_(task_update.remove_classroom_ids)
            ).delete(synchronize_session=False)

        # Handle student assignments
        if task_update.add_student_ids:
            # Verify students exist
            students = db.query(User).filter(
                User.id.in_(task_update.add_student_ids),
                User.user_type == "student"
            ).all()
            if len(students) != len(task_update.add_student_ids):
                raise HTTPException(status_code=404, detail="One or more students not found.")

            # Check for existing assignments
            existing_assignments = db.query(TaskStudents).filter(
                TaskStudents.task_id == task_id,
                TaskStudents.student_id.in_(task_update.add_student_ids)
            ).all()
            if existing_assignments:
                existing_ids = [ea.student_id for ea in existing_assignments]
                raise HTTPException(status_code=400, detail=f"Task already assigned to students: {existing_ids}")

            # Add new student assignments
            for student_id in task_update.add_student_ids:
                task_student = TaskStudents(
                    task_id=task_id,
                    student_id=student_id
                )
                db.add(task_student)

        if task_update.remove_student_ids:
            # Remove student assignments
            db.query(TaskStudents).filter(
                TaskStudents.task_id == task_id,
                TaskStudents.student_id.in_(task_update.remove_student_ids)
            ).delete(synchronize_session=False)

        # Commit all changes
        db.commit()
        db.refresh(task)

        # Return the updated task with full details
        return get_task_for_editing(db, task_id)

    except HTTPException:
        raise
    except SQLAlchemyError as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Database error occurred: {e}")
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Unexpected error occurred: {e}")


def delete_task(db: Session, task_id: UUID) -> None:
    """Delete task"""
    try:
        with db.begin():
            task = db.query(Task).filter(Task.id == task_id).first()
            if not task:
                raise HTTPException(status_code=404, detail="Task not found.")
            
            db.delete(task)
            db.flush()

    except HTTPException:
        raise
    except SQLAlchemyError as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Database error occurred: {e}")
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Unexpected error occurred: {e}")


# ==================== TASK CLASSROOM & STUDENT MANAGEMENT ====================

def assign_task_to_classroom(db: Session, task_id: UUID, classroom_id: UUID) -> TaskClassroomOut:
    """Assign task to an entire classroom"""
    try:
        with db.begin():
            # Verify task and classroom exist
            task = db.query(Task).filter(Task.id == task_id).first()
            if not task:
                raise HTTPException(status_code=404, detail="Task not found.")

            classroom = db.query(Classroom).filter(Classroom.id == classroom_id).first()
            if not classroom:
                raise HTTPException(status_code=404, detail="Classroom not found.")

            # Check if relationship already exists
            existing = db.query(TaskClassroom).filter(
                TaskClassroom.task_id == task_id,
                TaskClassroom.classroom_id == classroom_id
            ).first()
            if existing:
                raise HTTPException(status_code=400, detail="Task already assigned to this classroom.")

            task_classroom = TaskClassroom(
                task_id=task_id,
                classroom_id=classroom_id
            )
            db.add(task_classroom)
            db.flush()
            return TaskClassroomOut.from_orm(task_classroom)

    except HTTPException:
        raise
    except SQLAlchemyError as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Database error occurred: {e}")
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Unexpected error occurred: {e}")


def remove_task_from_classroom(db: Session, task_id: UUID, classroom_id: UUID) -> None:
    """Remove task assignment from classroom"""
    try:
        with db.begin():
            task_classroom = db.query(TaskClassroom).filter(
                TaskClassroom.task_id == task_id,
                TaskClassroom.classroom_id == classroom_id
            ).first()
            if not task_classroom:
                raise HTTPException(status_code=404, detail="Task not assigned to this classroom.")

            db.delete(task_classroom)
            db.flush()

    except HTTPException:
        raise
    except SQLAlchemyError as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Database error occurred: {e}")
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Unexpected error occurred: {e}")


def assign_task_to_student(db: Session, task_id: UUID, student_id: UUID) -> TaskStudentOut:
    """Assign task to an individual student"""
    try:
        with db.begin():
            # Verify task and student exist
            task = db.query(Task).filter(Task.id == task_id).first()
            if not task:
                raise HTTPException(status_code=404, detail="Task not found.")

            student = db.query(User).filter(User.id == student_id, User.user_type == "student").first()
            if not student:
                raise HTTPException(status_code=404, detail="Student not found.")

            # Check if relationship already exists
            existing = db.query(TaskStudents).filter(
                TaskStudents.task_id == task_id,
                TaskStudents.student_id == student_id
            ).first()
            if existing:
                raise HTTPException(status_code=400, detail="Task already assigned to this student.")

            task_student = TaskStudents(
                task_id=task_id,
                student_id=student_id
            )
            db.add(task_student)
            db.flush()
            return TaskStudentOut.from_orm(task_student)

    except HTTPException:
        raise
    except SQLAlchemyError as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Database error occurred: {e}")
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Unexpected error occurred: {e}")


def remove_task_from_student(db: Session, task_id: UUID, student_id: UUID) -> None:
    """Remove task assignment from student"""
    try:
        with db.begin():
            task_student = db.query(TaskStudents).filter(
                TaskStudents.task_id == task_id,
                TaskStudents.student_id == student_id
            ).first()
            if not task_student:
                raise HTTPException(status_code=404, detail="Task not assigned to this student.")

            db.delete(task_student)
            db.flush()

    except HTTPException:
        raise
    except SQLAlchemyError as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Database error occurred: {e}")
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Unexpected error occurred: {e}")


def assign_task_to_classroom_student(db: Session, task_id: UUID, classroom_id: UUID, student_id: UUID) -> TaskClassroomStudentOut:
    """Assign task to a specific student within a classroom"""
    try:
        with db.begin():
            # Verify task, classroom, and student exist
            task = db.query(Task).filter(Task.id == task_id).first()
            if not task:
                raise HTTPException(status_code=404, detail="Task not found.")

            classroom = db.query(Classroom).filter(Classroom.id == classroom_id).first()
            if not classroom:
                raise HTTPException(status_code=404, detail="Classroom not found.")

            student = db.query(User).filter(User.id == student_id, User.user_type == "student").first()
            if not student:
                raise HTTPException(status_code=404, detail="Student not found.")

            # Check if relationship already exists
            existing = db.query(TaskClassroomStudent).filter(
                TaskClassroomStudent.task_id == task_id,
                TaskClassroomStudent.classroom_id == classroom_id,
                TaskClassroomStudent.student_id == student_id
            ).first()
            if existing:
                raise HTTPException(status_code=400, detail="Task already assigned to this student in this classroom.")

            task_classroom_student = TaskClassroomStudent(
                task_id=task_id,
                classroom_id=classroom_id,
                student_id=student_id
            )
            db.add(task_classroom_student)
            db.flush()
            return TaskClassroomStudentOut.from_orm(task_classroom_student)

    except HTTPException:
        raise
    except SQLAlchemyError as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Database error occurred: {e}")
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Unexpected error occurred: {e}")


def remove_task_from_classroom_student(db: Session, task_id: UUID, classroom_id: UUID, student_id: UUID) -> None:
    """Remove task assignment from a specific student within a classroom"""
    try:
        with db.begin():
            task_classroom_student = db.query(TaskClassroomStudent).filter(
                TaskClassroomStudent.task_id == task_id,
                TaskClassroomStudent.classroom_id == classroom_id,
                TaskClassroomStudent.student_id == student_id
            ).first()
            if not task_classroom_student:
                raise HTTPException(status_code=404, detail="Task not assigned to this student in this classroom.")

            db.delete(task_classroom_student)
            db.flush()

    except HTTPException:
        raise
    except SQLAlchemyError as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Database error occurred: {e}")
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Unexpected error occurred: {e}")


def get_tasks_by_classroom(db: Session, classroom_id: UUID, skip: int = 0, limit: int = 100) -> TaskListOut:
    """Get tasks assigned to a specific classroom"""
    task_classrooms = db.query(TaskClassroom).filter(TaskClassroom.classroom_id == classroom_id).offset(skip).limit(limit).all()
    task_ids = [tc.task_id for tc in task_classrooms]
    tasks = db.query(Task).filter(Task.id.in_(task_ids)).all()
    total = db.query(TaskClassroom).filter(TaskClassroom.classroom_id == classroom_id).count()
    return TaskListOut(
        tasks=[TaskOut.from_orm(task) for task in tasks],
        total=total
    )


def get_tasks_by_student(db: Session, student_id: UUID, skip: int = 0, limit: int = 100) -> MyTasks:
    """Get tasks assigned to a specific student with only their own attachments for security"""
    # Get individual task assignments for the student
    task_students = db.query(TaskStudents).filter(TaskStudents.student_id == student_id).all()
    individual_task_ids = [ts.task_id for ts in task_students]

    # Get classroom-based task assignments
    classroom_task_ids = db.query(TaskClassroom.task_id).join(
        StudentClassroom,
        TaskClassroom.classroom_id == StudentClassroom.classroom_id
    ).filter(
        StudentClassroom.student_id == student_id
    ).all()
    classroom_task_ids = [ct.task_id for ct in classroom_task_ids]

    # Combine all task IDs and remove duplicates
    all_task_ids = list(set(individual_task_ids + classroom_task_ids))

    if not all_task_ids:
        return MyTasks(tasks=[], total=0, student_attachments=[])

    # Apply pagination to the combined task list
    paginated_task_ids = all_task_ids[skip:skip + limit]

    # Get the tasks
    tasks = db.query(Task).filter(Task.id.in_(paginated_task_ids)).all()

    # Get student's own attachments for these tasks
    student_attachments = db.query(StudentTaskAttachment).filter(
        StudentTaskAttachment.student_id == student_id,
        StudentTaskAttachment.task_id.in_(paginated_task_ids)
    ).all()

    # Create task objects with proper relationship handling
    task_objects = []
    for task in tasks:
        # Get chapters for this task
        task_chapters = db.query(TaskChapter).filter(TaskChapter.task_id == task.id).all()
        chapters = []
        for tc in task_chapters:
            if tc.chapter:
                chapters.append(ChapterOut.model_validate(tc.chapter))

        # Get topics for this task
        task_topics = db.query(TaskTopic).filter(TaskTopic.task_id == task.id).all()
        topics = []
        for tt in task_topics:
            if tt.topic:
                topics.append(TopicOut.model_validate(tt.topic))

        # Get subtopics for this task
        task_subtopics = db.query(TaskSubTopic).filter(TaskSubTopic.task_id == task.id).all()
        subtopics = []
        for ts in task_subtopics:
            if ts.subtopic:
                subtopics.append(SubTopicOut.model_validate(ts.subtopic))

        task_dict = {
            'id': task.id,
            'name': task.name,
            'description': task.description,
            'status': task.status,
            'deadline': task.deadline,
            'accept_after_deadline': task.accept_after_deadline,
            'created_at': task.created_at,
            'updated_at': task.updated_at,
            'subject_id': task.subject_id,
            'subject': SubjectOut.model_validate(task.subject) if task.subject else None,
            'chapters': chapters,
            'topics': topics,
            'subtopics': subtopics,
        }
        task_objects.append(StudentTaskOut(**task_dict))

    total = len(all_task_ids)  # Total count of all tasks (individual + classroom)

    return MyTasks(
        tasks=task_objects,
        total=total,
        student_attachments=[StudentTaskAttachmentOut.model_validate(att) for att in student_attachments]
    )


# ==================== TASK ATTACHMENT CRUD ====================

def get_task_attachments(db: Session, task_id: UUID) -> List[TaskAttachmentOut]:
    """Get all attachments for a specific task"""
    try:
        attachments = db.query(TaskAttachment).filter(TaskAttachment.task_id == task_id).all()
        return [TaskAttachmentOut.from_orm(attachment) for attachment in attachments]
    except SQLAlchemyError as e:
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")


def get_student_task_attachments(db: Session, task_id: UUID, student_id: UUID) -> List[StudentTaskAttachmentOut]:
    """Get all attachments for a specific task by a specific student"""
    try:
        attachments = db.query(StudentTaskAttachment).filter(
            StudentTaskAttachment.task_id == task_id,
            StudentTaskAttachment.student_id == student_id
        ).all()
        return [StudentTaskAttachmentOut.from_orm(attachment) for attachment in attachments]
    except SQLAlchemyError as e:
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")


def get_all_student_attachments_for_task(db: Session, task_id: UUID) -> List[StudentTaskAttachmentOut]:
    """Get all student attachments for a specific task (teacher view)"""
    try:
        attachments = db.query(StudentTaskAttachment).filter(
            StudentTaskAttachment.task_id == task_id
        ).all()
        return [StudentTaskAttachmentOut.from_orm(attachment) for attachment in attachments]
    except SQLAlchemyError as e:
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")


def delete_task_attachment(db: Session, attachment_id: UUID) -> bool:
    """Delete a task attachment"""
    try:
        attachment = db.query(TaskAttachment).filter(TaskAttachment.id == attachment_id).first()
        if not attachment:
            raise HTTPException(status_code=404, detail="Attachment not found")

        db.delete(attachment)
        db.commit()
        return True
    except SQLAlchemyError as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")


def delete_student_task_attachment(db: Session, attachment_id: UUID, student_id: UUID = None) -> bool:
    """Delete a student task attachment (with optional student permission check)"""
    try:
        query = db.query(StudentTaskAttachment).filter(StudentTaskAttachment.id == attachment_id)

        # If student_id is provided, ensure the student can only delete their own attachments
        if student_id:
            query = query.filter(StudentTaskAttachment.student_id == student_id)

        attachment = query.first()
        if not attachment:
            raise HTTPException(status_code=404, detail="Attachment not found")

        db.delete(attachment)
        db.commit()
        return True
    except SQLAlchemyError as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")


def get_tasks_by_classroom_student(db: Session, classroom_id: UUID, student_id: UUID, skip: int = 0, limit: int = 100) -> TaskListOut:
    """Get tasks assigned to a specific student within a classroom"""
    task_classroom_students = db.query(TaskClassroomStudent).filter(
        TaskClassroomStudent.classroom_id == classroom_id,
        TaskClassroomStudent.student_id == student_id
    ).offset(skip).limit(limit).all()
    task_ids = [tcs.task_id for tcs in task_classroom_students]
    tasks = db.query(Task).filter(Task.id.in_(task_ids)).all()
    total = db.query(TaskClassroomStudent).filter(
        TaskClassroomStudent.classroom_id == classroom_id,
        TaskClassroomStudent.student_id == student_id
    ).count()
    return TaskListOut(
        tasks=[TaskOut.from_orm(task) for task in tasks],
        total=total
    )


def get_students_by_task(db: Session, task_id: UUID, skip: int = 0, limit: int = 100) -> List[TaskStudentOut]:
    """Get all students assigned to a specific task"""
    task_students = db.query(TaskStudents).filter(TaskStudents.task_id == task_id).offset(skip).limit(limit).all()
    return [TaskStudentOut.from_orm(ts) for ts in task_students]


def get_classrooms_by_task(db: Session, task_id: UUID, skip: int = 0, limit: int = 100) -> List[TaskClassroomOut]:
    """Get all classrooms assigned to a specific task"""
    task_classrooms = db.query(TaskClassroom).filter(TaskClassroom.task_id == task_id).offset(skip).limit(limit).all()
    return [TaskClassroomOut.from_orm(tc) for tc in task_classrooms]


def get_classroom_students_by_task(db: Session, task_id: UUID, skip: int = 0, limit: int = 100) -> List[TaskClassroomStudentOut]:
    """Get all classroom-student assignments for a specific task"""
    task_classroom_students = db.query(TaskClassroomStudent).filter(TaskClassroomStudent.task_id == task_id).offset(skip).limit(limit).all()
    return [TaskClassroomStudentOut.from_orm(tcs) for tcs in task_classroom_students]


# ==================== BULK ASSIGNMENT FUNCTIONS ====================

def assign_task_to_multiple_students(db: Session, task_id: UUID, student_ids: List[UUID]) -> List[TaskStudentOut]:
    """Assign task to multiple students at once"""
    try:
        with db.begin():
            # Verify task exists
            task = db.query(Task).filter(Task.id == task_id).first()
            if not task:
                raise HTTPException(status_code=404, detail="Task not found.")

            # Verify all students exist
            students = db.query(User).filter(
                User.id.in_(student_ids),
                User.user_type == "student"
            ).all()
            if len(students) != len(student_ids):
                raise HTTPException(status_code=404, detail="One or more students not found.")

            # Check for existing assignments
            existing_assignments = db.query(TaskStudents).filter(
                TaskStudents.task_id == task_id,
                TaskStudents.student_id.in_(student_ids)
            ).all()
            if existing_assignments:
                existing_ids = [ea.student_id for ea in existing_assignments]
                raise HTTPException(status_code=400, detail=f"Task already assigned to students: {existing_ids}")

            # Create new assignments
            task_students = []
            for student_id in student_ids:
                task_student = TaskStudents(
                    task_id=task_id,
                    student_id=student_id
                )
                db.add(task_student)
                task_students.append(task_student)

            db.flush()
            return [TaskStudentOut.from_orm(ts) for ts in task_students]

    except HTTPException:
        raise
    except SQLAlchemyError as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Database error occurred: {e}")
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Unexpected error occurred: {e}")


def assign_task_to_multiple_classrooms(db: Session, task_id: UUID, classroom_ids: List[UUID]) -> List[TaskClassroomOut]:
    """Assign task to multiple classrooms at once"""
    try:
        with db.begin():
            # Verify task exists
            task = db.query(Task).filter(Task.id == task_id).first()
            if not task:
                raise HTTPException(status_code=404, detail="Task not found.")

            # Verify all classrooms exist
            classrooms = db.query(Classroom).filter(Classroom.id.in_(classroom_ids)).all()
            if len(classrooms) != len(classroom_ids):
                raise HTTPException(status_code=404, detail="One or more classrooms not found.")

            # Check for existing assignments
            existing_assignments = db.query(TaskClassroom).filter(
                TaskClassroom.task_id == task_id,
                TaskClassroom.classroom_id.in_(classroom_ids)
            ).all()
            if existing_assignments:
                existing_ids = [ea.classroom_id for ea in existing_assignments]
                raise HTTPException(status_code=400, detail=f"Task already assigned to classrooms: {existing_ids}")

            # Create new assignments
            task_classrooms = []
            for classroom_id in classroom_ids:
                task_classroom = TaskClassroom(
                    task_id=task_id,
                    classroom_id=classroom_id
                )
                db.add(task_classroom)
                task_classrooms.append(task_classroom)

            db.flush()
            return [TaskClassroomOut.from_orm(tc) for tc in task_classrooms]

    except HTTPException:
        raise
    except SQLAlchemyError as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Database error occurred: {e}")
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Unexpected error occurred: {e}")


# ==================== COMBINED TASK CREATION AND ASSIGNMENT ====================

def create_task_with_assignments(
    db: Session, 
    task_create: TaskCreateWithAssignments, 
    classroom_ids: List[UUID] = None,
    student_ids: List[UUID] = None,
    classroom_student_assignments: List[dict] = None
) -> TaskOut:
    """Create a task and assign it to classrooms and/or students simultaneously"""
    try:
        # Verify subject exists if provided
        if task_create.subject_id:
            subject = db.query(Subject).filter(Subject.id == task_create.subject_id).first()
            if not subject:
                raise HTTPException(status_code=404, detail="Subject not found.")

        # Verify chapters exist if provided
        if task_create.chapter_ids:
            chapters = db.query(Chapter).filter(Chapter.id.in_(task_create.chapter_ids)).all()
            if len(chapters) != len(task_create.chapter_ids):
                raise HTTPException(status_code=404, detail="One or more chapters not found.")

        # Verify topics exist if provided
        if task_create.topic_ids:
            topics = db.query(Topic).filter(Topic.id.in_(task_create.topic_ids)).all()
            if len(topics) != len(task_create.topic_ids):
                raise HTTPException(status_code=404, detail="One or more topics not found.")

        # Verify subtopics exist if provided
        if task_create.subtopic_ids:
            subtopics = db.query(SubTopic).filter(SubTopic.id.in_(task_create.subtopic_ids)).all()
            if len(subtopics) != len(task_create.subtopic_ids):
                raise HTTPException(status_code=404, detail="One or more subtopics not found.")

        # Create the task
        new_task = Task(
            id=uuid4(),
            name=task_create.name,
            description=task_create.description,
            status=TaskStatus(task_create.status.value),
            subject_id=task_create.subject_id,
            deadline=task_create.deadline,
            accept_after_deadline=task_create.accept_after_deadline
        )
        db.add(new_task)
        db.flush()

        # Create junction table entries for chapters
        for chapter_id in task_create.chapter_ids:
            task_chapter = TaskChapter(
                task_id=new_task.id,
                chapter_id=chapter_id
            )
            db.add(task_chapter)

        # Create junction table entries for topics
        for topic_id in task_create.topic_ids:
            task_topic = TaskTopic(
                task_id=new_task.id,
                topic_id=topic_id
            )
            db.add(task_topic)

        # Create junction table entries for subtopics
        for subtopic_id in task_create.subtopic_ids:
            task_subtopic = TaskSubTopic(
                task_id=new_task.id,
                subtopic_id=subtopic_id
            )
            db.add(task_subtopic)

        # Assign to classrooms if provided
        if classroom_ids:
            # Verify all classrooms exist
            classrooms = db.query(Classroom).filter(Classroom.id.in_(classroom_ids)).all()
            if len(classrooms) != len(classroom_ids):
                raise HTTPException(status_code=404, detail="One or more classrooms not found.")

            for classroom_id in classroom_ids:
                task_classroom = TaskClassroom(
                    task_id=new_task.id,
                    classroom_id=classroom_id
                )
                db.add(task_classroom)

        # Assign to students if provided
        if student_ids:
            # Verify all students exist
            students = db.query(User).filter(
                User.id.in_(student_ids),
                User.user_type == "student"
            ).all()
            if len(students) != len(student_ids):
                raise HTTPException(status_code=404, detail="One or more students not found.")

            for student_id in student_ids:
                task_student = TaskStudents(
                    task_id=new_task.id,
                    student_id=student_id
                )
                db.add(task_student)

        # Assign to specific classroom-student combinations if provided
        if classroom_student_assignments:
            for assignment in classroom_student_assignments:
                classroom_id = assignment.get("classroom_id")
                student_id = assignment.get("student_id")
                
                if not classroom_id or not student_id:
                    raise HTTPException(status_code=400, detail="Invalid classroom-student assignment format")

                # Verify classroom and student exist
                classroom = db.query(Classroom).filter(Classroom.id == classroom_id).first()
                if not classroom:
                    raise HTTPException(status_code=404, detail=f"Classroom {classroom_id} not found.")

                student = db.query(User).filter(User.id == student_id, User.user_type == "student").first()
                if not student:
                    raise HTTPException(status_code=404, detail=f"Student {student_id} not found.")

                task_classroom_student = TaskClassroomStudent(
                    task_id=new_task.id,
                    classroom_id=classroom_id,
                    student_id=student_id
                )
                db.add(task_classroom_student)

        db.flush()
        db.refresh(new_task)
        db.commit()
        return TaskOut.from_orm(new_task)

    except HTTPException:
        db.rollback()
        raise
    except SQLAlchemyError as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Database error occurred: {e}")
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Unexpected error occurred: {e}")


def create_task_for_student(db: Session, task_create: TaskCreateForStudent) -> TaskOut:
    """Create a task and assign it to a single student"""
    try:
        # Verify subject exists if provided
        if task_create.subject_id:
            subject = db.query(Subject).filter(Subject.id == task_create.subject_id).first()
            if not subject:
                raise HTTPException(status_code=404, detail="Subject not found.")

        # Verify chapters exist if provided
        if task_create.chapter_ids:
            chapters = db.query(Chapter).filter(Chapter.id.in_(task_create.chapter_ids)).all()
            if len(chapters) != len(task_create.chapter_ids):
                raise HTTPException(status_code=404, detail="One or more chapters not found.")

        # Verify topics exist if provided
        if task_create.topic_ids:
            topics = db.query(Topic).filter(Topic.id.in_(task_create.topic_ids)).all()
            if len(topics) != len(task_create.topic_ids):
                raise HTTPException(status_code=404, detail="One or more topics not found.")

        # Verify subtopics exist if provided
        if task_create.subtopic_ids:
            subtopics = db.query(SubTopic).filter(SubTopic.id.in_(task_create.subtopic_ids)).all()
            if len(subtopics) != len(task_create.subtopic_ids):
                raise HTTPException(status_code=404, detail="One or more subtopics not found.")

        # Verify student exists
        student = db.query(User).filter(User.id == task_create.student_id, User.user_type == "student").first()
        if not student:
            raise HTTPException(status_code=404, detail="Student not found.")

        # Create the task
        new_task = Task(
            id=uuid4(),
            name=task_create.name,
            description=task_create.description,
            status=TaskStatus(task_create.status.value),
            subject_id=task_create.subject_id,
            deadline=task_create.deadline,
            accept_after_deadline=task_create.accept_after_deadline
        )
        db.add(new_task)
        db.flush()

        # Create junction table entries for chapters
        for chapter_id in task_create.chapter_ids:
            task_chapter = TaskChapter(
                task_id=new_task.id,
                chapter_id=chapter_id
            )
            db.add(task_chapter)

        # Create junction table entries for topics
        for topic_id in task_create.topic_ids:
            task_topic = TaskTopic(
                task_id=new_task.id,
                topic_id=topic_id
            )
            db.add(task_topic)

        # Create junction table entries for subtopics
        for subtopic_id in task_create.subtopic_ids:
            task_subtopic = TaskSubTopic(
                task_id=new_task.id,
                subtopic_id=subtopic_id
            )
            db.add(task_subtopic)

        # Assign to student
        task_student = TaskStudents(
            task_id=new_task.id,
            student_id=task_create.student_id
        )
        db.add(task_student)

        db.flush()
        db.refresh(new_task)
        db.commit()
        return TaskOut.model_validate(new_task)

    except HTTPException:
        db.rollback()
        raise
    except SQLAlchemyError as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Database error occurred: {e}")
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Unexpected error occurred: {e}")


def create_task_for_classroom(db: Session, task_create: TaskCreateForClassroom) -> TaskOut:
    """Create a task and assign it to a single classroom"""
    try:
        # Verify subject exists if provided
        if task_create.subject_id:
            subject = db.query(Subject).filter(Subject.id == task_create.subject_id).first()
            if not subject:
                raise HTTPException(status_code=404, detail="Subject not found.")

        # Verify chapters exist if provided
        if task_create.chapter_ids:
            chapters = db.query(Chapter).filter(Chapter.id.in_(task_create.chapter_ids)).all()
            if len(chapters) != len(task_create.chapter_ids):
                raise HTTPException(status_code=404, detail="One or more chapters not found.")

        # Verify topics exist if provided
        if task_create.topic_ids:
            topics = db.query(Topic).filter(Topic.id.in_(task_create.topic_ids)).all()
            if len(topics) != len(task_create.topic_ids):
                raise HTTPException(status_code=404, detail="One or more topics not found.")

        # Verify subtopics exist if provided
        if task_create.subtopic_ids:
            subtopics = db.query(SubTopic).filter(SubTopic.id.in_(task_create.subtopic_ids)).all()
            if len(subtopics) != len(task_create.subtopic_ids):
                raise HTTPException(status_code=404, detail="One or more subtopics not found.")

        # Verify classroom exists
        classroom = db.query(Classroom).filter(Classroom.id == task_create.classroom_id).first()
        if not classroom:
            raise HTTPException(status_code=404, detail="Classroom not found.")

        # Create the task
        new_task = Task(
            id=uuid4(),
            name=task_create.name,
            description=task_create.description,
            status=TaskStatus(task_create.status.value),
            subject_id=task_create.subject_id,
            deadline=task_create.deadline,
            accept_after_deadline=task_create.accept_after_deadline
        )
        db.add(new_task)
        db.flush()

        # Create junction table entries for chapters
        for chapter_id in task_create.chapter_ids:
            task_chapter = TaskChapter(
                task_id=new_task.id,
                chapter_id=chapter_id
            )
            db.add(task_chapter)

        # Create junction table entries for topics
        for topic_id in task_create.topic_ids:
            task_topic = TaskTopic(
                task_id=new_task.id,
                topic_id=topic_id
            )
            db.add(task_topic)

        # Create junction table entries for subtopics
        for subtopic_id in task_create.subtopic_ids:
            task_subtopic = TaskSubTopic(
                task_id=new_task.id,
                subtopic_id=subtopic_id
            )
            db.add(task_subtopic)

        # Assign to classroom
        task_classroom = TaskClassroom(
            task_id=new_task.id,
            classroom_id=task_create.classroom_id
        )
        db.add(task_classroom)

        db.flush()
        db.refresh(new_task)
        db.commit()
        return TaskOut.model_validate(new_task)

    except HTTPException:
        db.rollback()
        raise
    except SQLAlchemyError as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Database error occurred: {e}")
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Unexpected error occurred: {e}")


def create_task_for_multiple_students(db: Session, task_create: TaskCreateForMultipleStudents) -> TaskOut:
    """Create a task and assign it to multiple students"""
    try:
        # Verify subject exists if provided
        if task_create.subject_id:
            subject = db.query(Subject).filter(Subject.id == task_create.subject_id).first()
            if not subject:
                raise HTTPException(status_code=404, detail="Subject not found.")

        # Verify chapters exist if provided
        if task_create.chapter_ids:
            chapters = db.query(Chapter).filter(Chapter.id.in_(task_create.chapter_ids)).all()
            if len(chapters) != len(task_create.chapter_ids):
                raise HTTPException(status_code=404, detail="One or more chapters not found.")

        # Verify topics exist if provided
        if task_create.topic_ids:
            topics = db.query(Topic).filter(Topic.id.in_(task_create.topic_ids)).all()
            if len(topics) != len(task_create.topic_ids):
                raise HTTPException(status_code=404, detail="One or more topics not found.")

        # Verify subtopics exist if provided
        if task_create.subtopic_ids:
            subtopics = db.query(SubTopic).filter(SubTopic.id.in_(task_create.subtopic_ids)).all()
            if len(subtopics) != len(task_create.subtopic_ids):
                raise HTTPException(status_code=404, detail="One or more subtopics not found.")

        # Verify all students exist
        students = db.query(User).filter(
            User.id.in_(task_create.student_ids),
            User.user_type == "student"
        ).all()
        if len(students) != len(task_create.student_ids):
            raise HTTPException(status_code=404, detail="One or more students not found.")

        # Create the task
        new_task = Task(
            id=uuid4(),
            name=task_create.name,
            description=task_create.description,
            status=TaskStatus(task_create.status.value),
            subject_id=task_create.subject_id,
            deadline=task_create.deadline,
            accept_after_deadline=task_create.accept_after_deadline
        )
        db.add(new_task)
        db.flush()

        # Create junction table entries for chapters
        for chapter_id in task_create.chapter_ids:
            task_chapter = TaskChapter(
                task_id=new_task.id,
                chapter_id=chapter_id
            )
            db.add(task_chapter)

        # Create junction table entries for topics
        for topic_id in task_create.topic_ids:
            task_topic = TaskTopic(
                task_id=new_task.id,
                topic_id=topic_id
            )
            db.add(task_topic)

        # Create junction table entries for subtopics
        for subtopic_id in task_create.subtopic_ids:
            task_subtopic = TaskSubTopic(
                task_id=new_task.id,
                subtopic_id=subtopic_id
            )
            db.add(task_subtopic)

        # Assign to students
        for student_id in task_create.student_ids:
            task_student = TaskStudents(
                task_id=new_task.id,
                student_id=student_id
            )
            db.add(task_student)

        db.flush()
        db.refresh(new_task)
        db.commit()
        return TaskOut.model_validate(new_task)

    except HTTPException:
        db.rollback()
        raise
    except SQLAlchemyError as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Database error occurred: {e}")
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Unexpected error occurred: {e}")


def create_task_for_multiple_classrooms(db: Session, task_create: TaskCreateForMultipleClassrooms) -> TaskOut:
    """Create a task and assign it to multiple classrooms"""
    try:
        # Verify subject exists if provided
        if task_create.subject_id:
            subject = db.query(Subject).filter(Subject.id == task_create.subject_id).first()
            if not subject:
                raise HTTPException(status_code=404, detail="Subject not found.")

        # Verify chapters exist if provided
        if task_create.chapter_ids:
            chapters = db.query(Chapter).filter(Chapter.id.in_(task_create.chapter_ids)).all()
            if len(chapters) != len(task_create.chapter_ids):
                raise HTTPException(status_code=404, detail="One or more chapters not found.")

        # Verify topics exist if provided
        if task_create.topic_ids:
            topics = db.query(Topic).filter(Topic.id.in_(task_create.topic_ids)).all()
            if len(topics) != len(task_create.topic_ids):
                raise HTTPException(status_code=404, detail="One or more topics not found.")

        # Verify subtopics exist if provided
        if task_create.subtopic_ids:
            subtopics = db.query(SubTopic).filter(SubTopic.id.in_(task_create.subtopic_ids)).all()
            if len(subtopics) != len(task_create.subtopic_ids):
                raise HTTPException(status_code=404, detail="One or more subtopics not found.")

        # Verify all classrooms exist
        classrooms = db.query(Classroom).filter(Classroom.id.in_(task_create.classroom_ids)).all()
        if len(classrooms) != len(task_create.classroom_ids):
            raise HTTPException(status_code=404, detail="One or more classrooms not found.")

        # Create the task
        new_task = Task(
            id=uuid4(),
            name=task_create.name,
            description=task_create.description,
            status=TaskStatus(task_create.status.value),
            subject_id=task_create.subject_id,
            deadline=task_create.deadline,
            accept_after_deadline=task_create.accept_after_deadline
        )
        db.add(new_task)
        db.flush()

        # Create junction table entries for chapters
        for chapter_id in task_create.chapter_ids:
            task_chapter = TaskChapter(
                task_id=new_task.id,
                chapter_id=chapter_id
            )
            db.add(task_chapter)

        # Create junction table entries for topics
        for topic_id in task_create.topic_ids:
            task_topic = TaskTopic(
                task_id=new_task.id,
                topic_id=topic_id
            )
            db.add(task_topic)

        # Create junction table entries for subtopics
        for subtopic_id in task_create.subtopic_ids:
            task_subtopic = TaskSubTopic(
                task_id=new_task.id,
                subtopic_id=subtopic_id
            )
            db.add(task_subtopic)

        # Assign to classrooms
        for classroom_id in task_create.classroom_ids:
            task_classroom = TaskClassroom(
                task_id=new_task.id,
                classroom_id=classroom_id
            )
            db.add(task_classroom)

        db.flush()
        db.refresh(new_task)
        db.commit()
        return TaskOut.model_validate(new_task)

    except HTTPException:
        db.rollback()
        raise
    except SQLAlchemyError as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Database error occurred: {e}")
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Unexpected error occurred: {e}")


# ==================== SEARCH AND FILTER FUNCTIONS ====================

def get_tasks_by_student_minimal(db: Session, student_id: UUID, skip: int = 0, limit: int = 100) -> MyTasksMinimal:
    """Get minimal task info for student - used in task lists"""
    # Get individual task assignments for the student
    task_students = db.query(TaskStudents).filter(TaskStudents.student_id == student_id).all()
    individual_task_ids = [ts.task_id for ts in task_students]

    # Get classroom-based task assignments
    classroom_task_ids = db.query(TaskClassroom.task_id).join(
        StudentClassroom,
        TaskClassroom.classroom_id == StudentClassroom.classroom_id
    ).filter(
        StudentClassroom.student_id == student_id
    ).all()
    classroom_task_ids = [ct.task_id for ct in classroom_task_ids]

    # Combine all task IDs and remove duplicates
    all_task_ids = list(set(individual_task_ids + classroom_task_ids))

    # Apply pagination
    paginated_task_ids = all_task_ids[skip:skip + limit]

    tasks = db.query(Task).filter(Task.id.in_(paginated_task_ids)).all()
    
    task_objects = []
    for task in tasks:
        # Check if this specific student's submission is graded
        task_student = db.query(TaskStudents).filter(
            TaskStudents.task_id == task.id,
            TaskStudents.student_id == student_id
        ).first()

        is_graded = False
        grade = None
        marks = None
        total_marks = None
        percentage = None
        teacher_notes = None

        if task_student and task_student.submission_date and task_student.grade is not None:
            is_graded = True
            grade = task_student.grade
            marks = getattr(task_student, 'marks', None)
            total_marks = getattr(task_student, 'total_marks', None)
            percentage = getattr(task_student, 'percentage', None)
            teacher_notes = getattr(task_student, 'teacher_notes', None)

        task_objects.append(StudentTaskMinimalOut(
            id=task.id,
            name=task.name,
            description=task.description,
            deadline=task.deadline,
            status=task.status,
            graded=is_graded,
            grade=grade,
            marks=marks,
            total_marks=total_marks,
            percentage=percentage,
            teacher_notes=teacher_notes,
            accept_after_deadline=task.accept_after_deadline,
            subject=SubjectOut.model_validate(task.subject) if task.subject else None
        ))
    
    total = len(all_task_ids)  # Total count of all tasks (individual + classroom)
    
    return MyTasksMinimal(tasks=task_objects, total=total)


def get_task_by_id_for_student(db: Session, task_id: UUID, student_id: UUID) -> StudentTaskDetailedOut:
    """Get detailed task info for student including all attachments"""
    try:
        # Check if student is assigned to this task (individual assignment)
        task_student = db.query(TaskStudents).filter(
            TaskStudents.task_id == task_id,
            TaskStudents.student_id == student_id
        ).first()

        # If not found in individual assignments, check classroom assignments
        if not task_student:
            # Check if student is in a classroom assigned to this task
            classroom_assignment = db.query(TaskClassroom).join(
                StudentClassroom,
                TaskClassroom.classroom_id == StudentClassroom.classroom_id
            ).filter(
                TaskClassroom.task_id == task_id,
                StudentClassroom.student_id == student_id
            ).first()

            if not classroom_assignment:
                raise HTTPException(status_code=404, detail="Task not found or not assigned to you.")

            # Create virtual task_student for classroom-assigned student
            task_student = type('obj', (object,), {
                'task_id': task_id,
                'student_id': student_id,
                'submission_date': None,
                'grade': None,
                'submission_text': None,
                'submission_notes': None,
                'feedback': None
            })()

        task = db.query(Task).filter(Task.id == task_id).first()
        if not task:
            raise HTTPException(status_code=404, detail="Task not found.")

        # Get teacher attachments
        teacher_attachments = db.query(TaskAttachment).filter(TaskAttachment.task_id == task_id).all()
        
        # Get student's own attachments
        student_attachments = db.query(StudentTaskAttachment).filter(
            StudentTaskAttachment.task_id == task_id,
            StudentTaskAttachment.student_id == student_id
        ).all()

        # Check if this student's submission is graded
        is_graded = False
        grade = None
        marks = None
        total_marks = None
        percentage = None
        teacher_notes = None

        if task_student and task_student.submission_date and task_student.grade is not None:
            is_graded = True
            grade = task_student.grade
            marks = getattr(task_student, 'marks', None)
            total_marks = getattr(task_student, 'total_marks', None)
            percentage = getattr(task_student, 'percentage', None)
            teacher_notes = getattr(task_student, 'teacher_notes', None)

        return StudentTaskDetailedOut(
            id=task.id,
            name=task.name,
            description=task.description,
            status=task.status,
            graded=is_graded,
            grade=grade,
            marks=marks,
            total_marks=total_marks,
            percentage=percentage,
            teacher_notes=teacher_notes,
            deadline=task.deadline,
            accept_after_deadline=task.accept_after_deadline,
            created_at=task.created_at,
            updated_at=task.updated_at,
            subject=SubjectOut.model_validate(task.subject) if task.subject else None,
            chapters=[ChapterOut.model_validate(tc.chapter) for tc in getattr(task, 'chapters', [])],
            topics=[TopicOut.model_validate(tt.topic) for tt in getattr(task, 'topics', [])],
            subtopics=[SubTopicOut.model_validate(ts.subtopic) for ts in getattr(task, 'subtopics', [])],
            teacher_attachments=[TaskAttachmentOut.model_validate(att) for att in teacher_attachments],
            my_attachments=[StudentTaskAttachmentOut.model_validate(att) for att in student_attachments]
        )

    except SQLAlchemyError as e:
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")


# ==================== NEW TASK SUBMISSION AND GRADING CRUD ====================

def submit_task(db: Session, task_id: UUID, student_id: UUID, submission_text: Optional[str] = None):
    """Submit a task by a student"""
    try:
        # Check if task exists
        task = db.query(Task).filter(Task.id == task_id).first()
        if not task:
            raise HTTPException(status_code=404, detail="Task not found")

        # Check if student is assigned to this task (individual assignment)
        task_student = db.query(TaskStudents).filter(
            TaskStudents.task_id == task_id,
            TaskStudents.student_id == student_id
        ).first()

        # If not found in individual assignments, check classroom assignments
        if not task_student:
            # Check if student is in a classroom assigned to this task
            classroom_assignment = db.query(TaskClassroom).join(
                StudentClassroom,
                TaskClassroom.classroom_id == StudentClassroom.classroom_id
            ).filter(
                TaskClassroom.task_id == task_id,
                StudentClassroom.student_id == student_id
            ).first()

            if not classroom_assignment:
                raise HTTPException(status_code=403, detail="Student is not assigned to this task")

            # Create individual assignment for classroom-assigned student
            task_student = TaskStudents(
                task_id=task_id,
                student_id=student_id,
                submission_date=None,
                grade=None,
                submission_text=None,
                submission_notes=None,
                feedback=None
            )
            db.add(task_student)
            db.flush()

        # Check if task is already submitted
        if task_student.submission_date:
            raise HTTPException(status_code=400, detail="Task already submitted")

        # Check deadline if accept_after_deadline is False
        if task.deadline and not task.accept_after_deadline:
            if datetime.utcnow() > task.deadline:
                raise HTTPException(status_code=400, detail="Task deadline has passed")

        # Update submission
        task_student.submission_date = datetime.utcnow()

        # Update task status to IN_PROGRESS when first submission is made
        if task.status == TaskStatus.PENDING:
            task.status = TaskStatus.IN_PROGRESS

        # Check if all students have submitted to mark task as COMPLETED
        _update_task_status_if_all_submitted(db, task_id)

        db.commit()
        db.refresh(task_student)

        return {
            "task_id": task_id,
            "student_id": student_id,
            "submission_date": task_student.submission_date,
            "submission_text": submission_text,
            "grade": task_student.grade
        }

    except HTTPException:
        db.rollback()
        raise
    except SQLAlchemyError as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")


def submit_task_comprehensive(db: Session, task_id: UUID, student_id: UUID, submission_text: Optional[str] = None, submission_notes: Optional[str] = None):
    """Submit a task with comprehensive submission data including text, notes, and attachments"""
    try:
        # Check if task exists
        task = db.query(Task).filter(Task.id == task_id).first()
        if not task:
            raise HTTPException(status_code=404, detail="Task not found")

        # Check if student is assigned to this task (individual assignment)
        task_student = db.query(TaskStudents).filter(
            TaskStudents.task_id == task_id,
            TaskStudents.student_id == student_id
        ).first()

        # If not found in individual assignments, check classroom assignments
        if not task_student:
            # Check if student is in a classroom assigned to this task
            classroom_assignment = db.query(TaskClassroom).join(
                StudentClassroom,
                TaskClassroom.classroom_id == StudentClassroom.classroom_id
            ).filter(
                TaskClassroom.task_id == task_id,
                StudentClassroom.student_id == student_id
            ).first()

            if not classroom_assignment:
                raise HTTPException(status_code=403, detail="Student is not assigned to this task")

            # Create individual assignment for classroom-assigned student
            task_student = TaskStudents(
                task_id=task_id,
                student_id=student_id,
                submission_date=None,
                grade=None,
                submission_text=None,
                submission_notes=None,
                feedback=None
            )
            db.add(task_student)
            db.flush()

        # Check if task is already submitted
        if task_student.submission_date:
            raise HTTPException(status_code=400, detail="Task already submitted")

        # Check deadline if accept_after_deadline is False
        if task.deadline and not task.accept_after_deadline:
            if datetime.utcnow() > task.deadline:
                raise HTTPException(status_code=400, detail="Task deadline has passed")

        # Update submission with comprehensive data
        task_student.submission_date = datetime.utcnow()
        task_student.submission_text = submission_text
        task_student.submission_notes = submission_notes

        # Update task status to IN_PROGRESS when first submission is made
        if task.status == TaskStatus.PENDING:
            task.status = TaskStatus.IN_PROGRESS

        # Check if all students have submitted to mark task as COMPLETED
        _update_task_status_if_all_submitted(db, task_id)

        db.commit()
        db.refresh(task_student)

        # Get all student attachments for this task
        student_attachments = db.query(StudentTaskAttachment).filter(
            StudentTaskAttachment.task_id == task_id,
            StudentTaskAttachment.student_id == student_id
        ).all()

        # Get teacher attachments for this task
        teacher_attachments = db.query(TaskAttachment).filter(
            TaskAttachment.task_id == task_id,
            TaskAttachment.student_id.is_(None)  # Teacher attachments have null student_id
        ).all()

        # Get subject information
        subject = None
        if task.subject_id:
            subject = db.query(Subject).filter(Subject.id == task.subject_id).first()

        # Return comprehensive submission details
        return {
            "task_id": task_id,
            "task_name": task.name,
            "task_description": task.description,
            "student_id": student_id,
            "submission_date": task_student.submission_date,
            "submission_text": submission_text,
            "submission_notes": submission_notes,
            "grade": task_student.grade,
            "feedback": task_student.feedback,
            "deadline": task.deadline,
            "subject": SubjectOut.model_validate(subject) if subject else None,
            "attachments": [StudentTaskAttachmentOut.model_validate(att) for att in student_attachments],
            "teacher_attachments": [TaskAttachmentOut.model_validate(att) for att in teacher_attachments]
        }

    except HTTPException:
        db.rollback()
        raise
    except SQLAlchemyError as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")


def grade_task(db: Session, task_id: UUID, student_id: UUID, grade: int, feedback: Optional[str], teacher_id: UUID, marks: Optional[int] = None, total_marks: Optional[int] = None, teacher_notes: Optional[str] = None):
    """Grade a student's task submission"""
    try:
        # Check if task exists
        task = db.query(Task).filter(Task.id == task_id).first()
        if not task:
            raise HTTPException(status_code=404, detail="Task not found")

        # Check if student is assigned to this task
        task_student = db.query(TaskStudents).filter(
            TaskStudents.task_id == task_id,
            TaskStudents.student_id == student_id
        ).first()

        if not task_student:
            raise HTTPException(status_code=404, detail="Student assignment not found for this task")

        # Check if task is submitted
        if not task_student.submission_date:
            raise HTTPException(status_code=400, detail="Task has not been submitted yet")

        # Validate grade range
        if grade < 0 or grade > 100:
            raise HTTPException(status_code=400, detail="Grade must be between 0 and 100")

        # Update grade and feedback
        task_student.grade = grade
        task_student.feedback = feedback

        # Update new grading fields
        if marks is not None and total_marks is not None:
            task_student.marks = marks
            task_student.total_marks = total_marks
            task_student.percentage = round((marks / total_marks) * 100, 2) if total_marks > 0 else 0.0
        else:
            # Fallback: use grade as both marks and percentage
            task_student.marks = grade
            task_student.total_marks = 100
            task_student.percentage = float(grade)

        if teacher_notes is not None:
            task_student.teacher_notes = teacher_notes

        # Check if all students are now graded to update task status
        _update_task_status_if_all_submitted(db, task_id)

        db.commit()
        db.refresh(task_student)

        return {
            "task_id": task_id,
            "student_id": student_id,
            "grade": grade,
            "feedback": feedback,
            "graded_at": datetime.utcnow(),
            "graded_by": teacher_id
        }

    except HTTPException:
        db.rollback()
        raise
    except SQLAlchemyError as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")


def get_student_grades(db: Session, student_id: UUID, skip: int = 0, limit: int = 100):
    """Get all grades for a student"""
    try:
        # Get all graded tasks for the student
        query = db.query(TaskStudents, Task, Subject).join(
            Task, TaskStudents.task_id == Task.id
        ).outerjoin(
            Subject, Task.subject_id == Subject.id
        ).filter(
            TaskStudents.student_id == student_id,
            TaskStudents.grade.isnot(None)
        ).order_by(TaskStudents.submission_date.desc())

        total = query.count()
        graded_tasks = query.offset(skip).limit(limit).all()

        grades = []
        for task_student, task, subject in graded_tasks:
            grade_data = {
                "task_id": task.id,
                "task_name": task.name,
                "task_description": task.description,
                "subject": SubjectOut.model_validate(subject) if subject else None,
                "grade": task_student.grade,
                "feedback": task_student.feedback,
                "submission_date": task_student.submission_date,
                "graded_at": task_student.submission_date,  # Using submission_date as graded_at for now
                "deadline": task.deadline
            }
            grades.append(grade_data)

        return {
            "grades": grades,
            "total": total
        }

    except SQLAlchemyError as e:
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")


def get_task_submission_details(db: Session, task_id: UUID, student_id: UUID):
    """Get detailed submission information for a specific task and student"""
    try:
        # Check if task exists
        task = db.query(Task).filter(Task.id == task_id).first()
        if not task:
            raise HTTPException(status_code=404, detail="Task not found")

        # Check if student is assigned to this task (individual assignment)
        task_student = db.query(TaskStudents).filter(
            TaskStudents.task_id == task_id,
            TaskStudents.student_id == student_id
        ).first()

        # If not found in individual assignments, check classroom assignments
        if not task_student:
            # Check if student is in a classroom assigned to this task
            classroom_assignment = db.query(TaskClassroom).join(
                StudentClassroom,
                TaskClassroom.classroom_id == StudentClassroom.classroom_id
            ).filter(
                TaskClassroom.task_id == task_id,
                StudentClassroom.student_id == student_id
            ).first()

            if not classroom_assignment:
                raise HTTPException(status_code=404, detail="Student assignment not found for this task")

            # Create virtual task_student for classroom-assigned student
            task_student = type('obj', (object,), {
                'task_id': task_id,
                'student_id': student_id,
                'submission_date': None,
                'grade': None,
                'submission_text': None,
                'submission_notes': None,
                'feedback': None
            })()

        # Check if task has been submitted
        if not task_student.submission_date:
            raise HTTPException(status_code=404, detail="Task has not been submitted yet")

        # Get all student attachments for this task
        student_attachments = db.query(StudentTaskAttachment).filter(
            StudentTaskAttachment.task_id == task_id,
            StudentTaskAttachment.student_id == student_id
        ).all()

        # Get teacher attachments for this task
        teacher_attachments = db.query(TaskAttachment).filter(
            TaskAttachment.task_id == task_id,
            TaskAttachment.student_id.is_(None)  # Teacher attachments have null student_id
        ).all()

        # Get subject information
        subject = None
        if task.subject_id:
            subject = db.query(Subject).filter(Subject.id == task.subject_id).first()

        # Return comprehensive submission details
        return {
            "task_id": task_id,
            "task_name": task.name,
            "task_description": task.description,
            "student_id": student_id,
            "submission_date": task_student.submission_date,
            "submission_text": task_student.submission_text,
            "submission_notes": task_student.submission_notes,
            "grade": task_student.grade,
            "feedback": task_student.feedback,
            "deadline": task.deadline,
            "subject": SubjectOut.model_validate(subject) if subject else None,
            "attachments": [StudentTaskAttachmentOut.model_validate(att) for att in student_attachments],
            "teacher_attachments": [TaskAttachmentOut.model_validate(att) for att in teacher_attachments]
        }

    except HTTPException:
        raise
    except SQLAlchemyError as e:
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")


# ===== ENHANCED SUBMISSION VIEWING AND GRADING FUNCTIONS =====

def get_task_submissions(db: Session, task_id: UUID, status_filter: Optional[str] = None) -> TaskSubmissionsListOut:
    """Get all submissions for a task with filtering options"""
    try:
        # Verify task exists
        task = db.query(Task).filter(Task.id == task_id).first()
        if not task:
            raise HTTPException(status_code=404, detail="Task not found.")

        # Get all students assigned to this task (both individual and classroom assignments)
        # Individual assignments
        individual_students = db.query(TaskStudents).filter(TaskStudents.task_id == task_id).all()

        # Classroom assignments - get all students in assigned classrooms
        classroom_assignments = db.query(TaskClassroom).filter(TaskClassroom.task_id == task_id).all()
        classroom_students = []
        for tc in classroom_assignments:
            if tc.classroom:
                # Get all students in this classroom through StudentClassroom junction table
                students_in_classroom = db.query(User).join(StudentClassroom).filter(
                    StudentClassroom.classroom_id == tc.classroom_id,
                    User.user_type == "student"
                ).all()
                for student in students_in_classroom:
                    # Check if this student already has an individual assignment
                    existing = next((ts for ts in individual_students if ts.student_id == student.id), None)
                    if not existing:
                        # Create a virtual TaskStudents entry for classroom-assigned students
                        virtual_assignment = type('obj', (object,), {
                            'task_id': task_id,
                            'student_id': student.id,
                            'submission_date': None,
                            'grade': None,
                            'submission_text': None,
                            'submission_notes': None,
                            'feedback': None
                        })()
                        classroom_students.append(virtual_assignment)

        # Combine all student assignments
        all_assignments = individual_students + classroom_students

        # Build submission list
        submissions = []
        total_submitted = 0
        total_graded = 0
        total_late = 0

        for assignment in all_assignments:
            # Get student details
            student = db.query(User).filter(User.id == assignment.student_id).first()
            if not student:
                continue

            # Calculate submission status
            is_submitted = assignment.submission_date is not None
            is_graded = assignment.grade is not None
            is_late = False
            days_late = None

            if is_submitted and task.deadline:
                if assignment.submission_date > task.deadline:
                    is_late = True
                    days_late = (assignment.submission_date.date() - task.deadline.date()).days

            # Get attachment count
            attachment_count = db.query(TaskAttachment).filter(
                TaskAttachment.task_id == task_id,
                TaskAttachment.student_id == assignment.student_id
            ).count()

            # Get grader information
            graded_by_name = None
            if assignment.grade is not None and hasattr(assignment, 'graded_by'):
                grader = db.query(User).filter(User.id == assignment.graded_by).first()
                if grader:
                    graded_by_name = grader.username

            # Apply status filter
            if status_filter:
                if status_filter == "submitted" and not is_submitted:
                    continue
                elif status_filter == "pending" and is_submitted:
                    continue
                elif status_filter == "graded" and not is_graded:
                    continue
                elif status_filter == "late" and not is_late:
                    continue

            submission = StudentSubmissionOut(
                student_id=assignment.student_id,
                student_name=student.username,
                student_email=student.email,
                submission_date=assignment.submission_date,
                submission_text=assignment.submission_text,
                submission_notes=assignment.submission_notes,
                grade=assignment.grade,
                feedback=assignment.feedback,
                graded_at=getattr(assignment, 'graded_at', None),
                graded_by=getattr(assignment, 'graded_by', None),
                graded_by_name=graded_by_name,
                is_submitted=is_submitted,
                is_graded=is_graded,
                is_late=is_late,
                days_late=days_late,
                attachment_count=attachment_count
            )
            submissions.append(submission)

            # Update counters
            if is_submitted:
                total_submitted += 1
            if is_graded:
                total_graded += 1
            if is_late:
                total_late += 1

        total_pending = len(submissions) - total_submitted

        return TaskSubmissionsListOut(
            task_id=task.id,
            task_name=task.name,
            task_description=task.description,
            deadline=task.deadline,
            accept_after_deadline=task.accept_after_deadline,
            subject_name=task.subject.name if task.subject else None,
            total_assigned=len(submissions),
            total_submitted=total_submitted,
            total_graded=total_graded,
            total_pending=total_pending,
            total_late=total_late,
            submissions=submissions
        )

    except HTTPException:
        raise
    except SQLAlchemyError as e:
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Unexpected error: {str(e)}")


def get_student_submission_detail(db: Session, task_id: UUID, student_id: UUID) -> StudentSubmissionDetailOut:
    """Get detailed view of a single student's submission for grading"""
    try:
        # Verify task exists
        task = db.query(Task).filter(Task.id == task_id).first()
        if not task:
            raise HTTPException(status_code=404, detail="Task not found.")

        # Verify student exists
        student = db.query(User).filter(User.id == student_id, User.user_type == "student").first()
        if not student:
            raise HTTPException(status_code=404, detail="Student not found.")

        # Check if student is assigned to this task
        task_student = db.query(TaskStudents).filter(
            TaskStudents.task_id == task_id,
            TaskStudents.student_id == student_id
        ).first()

        # If not found in individual assignments, check classroom assignments
        if not task_student:
            # Check if student is in a classroom assigned to this task
            classroom_assignment = db.query(TaskClassroom).join(StudentClassroom).filter(
                TaskClassroom.task_id == task_id,
                StudentClassroom.student_id == student_id,
                StudentClassroom.classroom_id == TaskClassroom.classroom_id
            ).first()

            if not classroom_assignment:
                raise HTTPException(status_code=403, detail="Student is not assigned to this task.")

            # Create virtual task_student for classroom-assigned student
            task_student = type('obj', (object,), {
                'task_id': task_id,
                'student_id': student_id,
                'submission_date': None,
                'grade': None,
                'submission_text': None,
                'submission_notes': None,
                'feedback': None
            })()

        # Calculate submission status
        is_submitted = task_student.submission_date is not None
        is_late = False
        days_late = None

        if is_submitted and task.deadline:
            if task_student.submission_date > task.deadline:
                is_late = True
                days_late = (task_student.submission_date.date() - task.deadline.date()).days

        # Get grader information
        graded_by_name = None
        if task_student.grade is not None and hasattr(task_student, 'graded_by'):
            grader = db.query(User).filter(User.id == task_student.graded_by).first()
            if grader:
                graded_by_name = grader.username

        # Get student attachments
        student_attachments = db.query(TaskAttachment).filter(
            TaskAttachment.task_id == task_id,
            TaskAttachment.student_id == student_id
        ).all()

        # Get teacher attachments (task materials)
        teacher_attachments = db.query(TaskAttachment).filter(
            TaskAttachment.task_id == task_id,
            TaskAttachment.student_id.is_(None)
        ).all()

        return StudentSubmissionDetailOut(
            task_id=task.id,
            task_name=task.name,
            task_description=task.description,
            deadline=task.deadline,
            accept_after_deadline=task.accept_after_deadline,
            subject=SubjectOut.model_validate(task.subject) if task.subject else None,
            student_id=student.id,
            student_name=student.username,
            student_email=student.email,
            submission_date=task_student.submission_date,
            submission_text=task_student.submission_text,
            submission_notes=task_student.submission_notes,
            is_submitted=is_submitted,
            is_late=is_late,
            days_late=days_late,
            grade=task_student.grade,
            feedback=task_student.feedback,
            graded_at=getattr(task_student, 'graded_at', None),
            graded_by=getattr(task_student, 'graded_by', None),
            graded_by_name=graded_by_name,
            is_graded=task_student.grade is not None,
            student_attachments=[StudentTaskAttachmentOut.model_validate(att) for att in student_attachments],
            teacher_attachments=[TaskAttachmentOut.model_validate(att) for att in teacher_attachments]
        )

    except HTTPException:
        raise
    except SQLAlchemyError as e:
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Unexpected error: {str(e)}")


def grade_student_submission(db: Session, task_id: UUID, student_id: UUID, grade: int, feedback: Optional[str], teacher_id: UUID, marks: Optional[int] = None, total_marks: Optional[int] = None, teacher_notes: Optional[str] = None) -> TaskGradeOut:
    """Grade a single student's submission with enhanced functionality"""
    try:
        # Verify task exists
        task = db.query(Task).filter(Task.id == task_id).first()
        if not task:
            raise HTTPException(status_code=404, detail="Task not found.")

        # Verify student exists
        student = db.query(User).filter(User.id == student_id, User.user_type == "student").first()
        if not student:
            raise HTTPException(status_code=404, detail="Student not found.")

        # Check if student is assigned to this task
        task_student = db.query(TaskStudents).filter(
            TaskStudents.task_id == task_id,
            TaskStudents.student_id == student_id
        ).first()

        if not task_student:
            # Check if student is in a classroom assigned to this task
            classroom_assignment = db.query(TaskClassroom).join(StudentClassroom).filter(
                TaskClassroom.task_id == task_id,
                StudentClassroom.student_id == student_id,
                StudentClassroom.classroom_id == TaskClassroom.classroom_id
            ).first()

            if not classroom_assignment:
                raise HTTPException(status_code=403, detail="Student is not assigned to this task.")

            # Create individual assignment for classroom-assigned student
            task_student = TaskStudents(
                task_id=task_id,
                student_id=student_id,
                submission_date=None,
                grade=None,
                submission_text=None,
                submission_notes=None,
                feedback=None
            )
            db.add(task_student)
            db.flush()

        # Validate grade
        if grade < 0 or grade > 100:
            raise HTTPException(status_code=400, detail="Grade must be between 0 and 100.")

        # Update grade and feedback
        task_student.grade = grade
        task_student.feedback = feedback

        # Update new grading fields
        if marks is not None and total_marks is not None:
            task_student.marks = marks
            task_student.total_marks = total_marks
            task_student.percentage = round((marks / total_marks) * 100, 2) if total_marks > 0 else 0.0
        else:
            # Fallback: use grade as both marks and percentage
            task_student.marks = grade
            task_student.total_marks = 100
            task_student.percentage = float(grade)

        if teacher_notes is not None:
            task_student.teacher_notes = teacher_notes

        # Add grading metadata if the model supports it
        current_time = datetime.utcnow()
        if hasattr(task_student, 'graded_at'):
            task_student.graded_at = current_time
        if hasattr(task_student, 'graded_by'):
            task_student.graded_by = teacher_id

        # Check if all students are now graded to update task status
        _update_task_status_if_all_submitted(db, task_id)

        db.commit()
        db.refresh(task_student)

        return TaskGradeOut(
            task_id=task_id,
            student_id=student_id,
            grade=grade,
            marks=task_student.marks or grade,
            total_marks=task_student.total_marks or 100,
            percentage=task_student.percentage or float(grade),
            feedback=feedback,
            teacher_notes=teacher_notes,
            graded_at=current_time,
            graded_by=teacher_id
        )

    except HTTPException:
        raise
    except SQLAlchemyError as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Unexpected error: {str(e)}")


def batch_grade_submissions(db: Session, task_id: UUID, grades_data: List[dict], teacher_id: UUID) -> BatchGradeOut:
    """Grade multiple submissions at once"""
    try:
        # Verify task exists
        task = db.query(Task).filter(Task.id == task_id).first()
        if not task:
            raise HTTPException(status_code=404, detail="Task not found.")

        successful_grades = []
        failed_grades = []

        for grade_item in grades_data:
            try:
                student_id = UUID(grade_item.get('student_id'))
                grade = int(grade_item.get('grade'))
                feedback = grade_item.get('feedback', '')

                # Validate grade
                if grade < 0 or grade > 100:
                    failed_grades.append({
                        'student_id': str(student_id),
                        'error': 'Grade must be between 0 and 100'
                    })
                    continue

                # Get or create task student assignment
                task_student = db.query(TaskStudents).filter(
                    TaskStudents.task_id == task_id,
                    TaskStudents.student_id == student_id
                ).first()

                if not task_student:
                    # Check if student is in a classroom assigned to this task
                    classroom_assignment = db.query(TaskClassroom).join(StudentClassroom).filter(
                        TaskClassroom.task_id == task_id,
                        StudentClassroom.student_id == student_id,
                        StudentClassroom.classroom_id == TaskClassroom.classroom_id
                    ).first()

                    if not classroom_assignment:
                        failed_grades.append({
                            'student_id': str(student_id),
                            'error': 'Student is not assigned to this task'
                        })
                        continue

                    # Create individual assignment for classroom-assigned student
                    task_student = TaskStudents(
                        task_id=task_id,
                        student_id=student_id,
                        submission_date=None,
                        grade=None,
                        submission_text=None,
                        submission_notes=None,
                        feedback=None
                    )
                    db.add(task_student)
                    db.flush()

                # Update grade and feedback
                task_student.grade = grade
                task_student.feedback = feedback

                # Add grading metadata if the model supports it
                current_time = datetime.utcnow()
                if hasattr(task_student, 'graded_at'):
                    task_student.graded_at = current_time
                if hasattr(task_student, 'graded_by'):
                    task_student.graded_by = teacher_id

                successful_grades.append(TaskGradeOut(
                    task_id=task_id,
                    student_id=student_id,
                    grade=grade,
                    feedback=feedback,
                    graded_at=current_time,
                    graded_by=teacher_id
                ))

            except Exception as e:
                failed_grades.append({
                    'student_id': grade_item.get('student_id', 'unknown'),
                    'error': str(e)
                })

        # Check if all students are now graded to update task status
        _update_task_status_if_all_submitted(db, task_id)

        # Commit all changes at once
        db.commit()

        return BatchGradeOut(
            task_id=task_id,
            total_graded=len(successful_grades),
            successful_grades=successful_grades,
            failed_grades=failed_grades
        )

    except HTTPException:
        raise
    except SQLAlchemyError as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Unexpected error: {str(e)}")


# ===== NEW TASK CREATION FUNCTIONS WITH FILE UPLOAD SUPPORT =====

async def _parse_form_data(
    name: str,
    description: Optional[str],
    deadline: Optional[str],
    accept_after_deadline: bool,
    subject_id: Optional[str],
    chapter_ids: str,
    topic_ids: str,
    subtopic_ids: str
) -> dict:
    """Parse form data into proper types"""
    try:
        # Parse JSON strings
        parsed_chapter_ids = json.loads(chapter_ids) if chapter_ids else []
        parsed_topic_ids = json.loads(topic_ids) if topic_ids else []
        parsed_subtopic_ids = json.loads(subtopic_ids) if subtopic_ids else []

        # Parse deadline
        parsed_deadline = None
        if deadline:
            try:
                parsed_deadline = datetime.fromisoformat(deadline.replace('Z', '+00:00'))
            except ValueError:
                raise HTTPException(status_code=400, detail="Invalid deadline format")

        # Parse subject_id
        parsed_subject_id = None
        if subject_id:
            try:
                parsed_subject_id = UUID(subject_id)
            except ValueError:
                raise HTTPException(status_code=400, detail="Invalid subject_id format")

        return {
            'name': name,
            'description': description,
            'deadline': parsed_deadline,
            'accept_after_deadline': accept_after_deadline,
            'subject_id': parsed_subject_id,
            'chapter_ids': [UUID(id) for id in parsed_chapter_ids],
            'topic_ids': [UUID(id) for id in parsed_topic_ids],
            'subtopic_ids': [UUID(id) for id in parsed_subtopic_ids]
        }
    except json.JSONDecodeError:
        raise HTTPException(status_code=400, detail="Invalid JSON format in IDs")
    except ValueError as e:
        raise HTTPException(status_code=400, detail=f"Invalid UUID format: {str(e)}")


async def _upload_task_files(files: List[UploadFile], task_id: UUID, db: Session) -> List[dict]:
    """Upload files and create task attachments"""
    uploaded_files = []

    for file in files:
        if file.filename:  # Skip empty files
            try:
                # Save the file
                file_path = await file_storage.save_task_attachment(
                    file, "system", str(task_id)
                )

                # Create database record
                attachment = TaskAttachment(
                    id=uuid4(),
                    file_url=file_path,
                    file_name=file.filename,
                    task_id=task_id,
                    student_id=None  # Teacher attachment
                )

                db.add(attachment)

                # Add to response
                download_url = f"{settings.STATIC_FILES_URL}/{file_path}"
                uploaded_files.append({
                    "attachment_id": str(attachment.id),
                    "file_name": file.filename,
                    "download_url": download_url
                })

            except Exception as e:
                raise HTTPException(status_code=500, detail=f"Failed to upload file {file.filename}: {str(e)}")

    return uploaded_files


async def create_task_for_student_with_files(
    db: Session,
    name: str,
    description: Optional[str],
    deadline: Optional[str],
    accept_after_deadline: bool,
    student_id: str,
    subject_id: Optional[str],
    chapter_ids: str,
    topic_ids: str,
    subtopic_ids: str,
    files: List[UploadFile]
) -> TaskCreateWithFilesResponse:
    """Create a task for a student with optional file attachments"""
    try:
        # Parse form data
        task_data = await _parse_form_data(
            name, description, deadline, accept_after_deadline,
            subject_id, chapter_ids, topic_ids, subtopic_ids
        )

        # Parse student_id
        try:
            parsed_student_id = UUID(student_id)
        except ValueError:
            raise HTTPException(status_code=400, detail="Invalid student_id format")

        # Create task schema
        task_create = TaskCreateForStudent(
            student_id=parsed_student_id,
            **task_data
        )

        # Create the task
        task = create_task_for_student(db, task_create)

        # Upload files if any
        uploaded_files = await _upload_task_files(files, task.id, db)

        # Commit all changes
        db.commit()

        return TaskCreateWithFilesResponse(
            task=task,
            uploaded_files=uploaded_files
        )

    except HTTPException:
        db.rollback()
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Failed to create task with files: {str(e)}")


async def create_task_for_classroom_with_files(
    db: Session,
    name: str,
    description: Optional[str],
    deadline: Optional[str],
    accept_after_deadline: bool,
    classroom_id: str,
    subject_id: Optional[str],
    chapter_ids: str,
    topic_ids: str,
    subtopic_ids: str,
    files: List[UploadFile]
) -> TaskCreateWithFilesResponse:
    """Create a task for a classroom with optional file attachments"""
    try:
        # Parse form data
        task_data = await _parse_form_data(
            name, description, deadline, accept_after_deadline,
            subject_id, chapter_ids, topic_ids, subtopic_ids
        )

        # Parse classroom_id
        try:
            parsed_classroom_id = UUID(classroom_id)
        except ValueError:
            raise HTTPException(status_code=400, detail="Invalid classroom_id format")

        # Create task schema
        task_create = TaskCreateForClassroom(
            classroom_id=parsed_classroom_id,
            **task_data
        )

        # Create the task
        task = create_task_for_classroom(db, task_create)

        # Upload files if any
        uploaded_files = await _upload_task_files(files, task.id, db)

        # Commit all changes
        db.commit()

        return TaskCreateWithFilesResponse(
            task=task,
            uploaded_files=uploaded_files
        )

    except HTTPException:
        db.rollback()
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Failed to create task with files: {str(e)}")


async def create_task_for_multiple_students_with_files(
    db: Session,
    name: str,
    description: Optional[str],
    deadline: Optional[str],
    accept_after_deadline: bool,
    student_ids: str,
    subject_id: Optional[str],
    chapter_ids: str,
    topic_ids: str,
    subtopic_ids: str,
    files: List[UploadFile]
) -> TaskCreateWithFilesResponse:
    """Create a task for multiple students with optional file attachments"""
    try:
        # Parse form data
        task_data = await _parse_form_data(
            name, description, deadline, accept_after_deadline,
            subject_id, chapter_ids, topic_ids, subtopic_ids
        )

        # Parse student_ids
        try:
            parsed_student_ids = [UUID(id) for id in json.loads(student_ids)]
        except (json.JSONDecodeError, ValueError):
            raise HTTPException(status_code=400, detail="Invalid student_ids format")

        # Create task schema
        task_create = TaskCreateForMultipleStudents(
            student_ids=parsed_student_ids,
            **task_data
        )

        # Create the task
        task = create_task_for_multiple_students(db, task_create)

        # Upload files if any
        uploaded_files = await _upload_task_files(files, task.id, db)

        # Commit all changes
        db.commit()

        return TaskCreateWithFilesResponse(
            task=task,
            uploaded_files=uploaded_files
        )

    except HTTPException:
        db.rollback()
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Failed to create task with files: {str(e)}")


async def create_task_for_multiple_classrooms_with_files(
    db: Session,
    name: str,
    description: Optional[str],
    deadline: Optional[str],
    accept_after_deadline: bool,
    classroom_ids: str,
    subject_id: Optional[str],
    chapter_ids: str,
    topic_ids: str,
    subtopic_ids: str,
    files: List[UploadFile]
) -> TaskCreateWithFilesResponse:
    """Create a task for multiple classrooms with optional file attachments"""
    try:
        # Parse form data
        task_data = await _parse_form_data(
            name, description, deadline, accept_after_deadline,
            subject_id, chapter_ids, topic_ids, subtopic_ids
        )

        # Parse classroom_ids
        try:
            parsed_classroom_ids = [UUID(id) for id in json.loads(classroom_ids)]
        except (json.JSONDecodeError, ValueError):
            raise HTTPException(status_code=400, detail="Invalid classroom_ids format")

        # Create task schema
        task_create = TaskCreateForMultipleClassrooms(
            classroom_ids=parsed_classroom_ids,
            **task_data
        )

        # Create the task
        task = create_task_for_multiple_classrooms(db, task_create)

        # Upload files if any
        uploaded_files = await _upload_task_files(files, task.id, db)

        # Commit all changes
        db.commit()

        return TaskCreateWithFilesResponse(
            task=task,
            uploaded_files=uploaded_files
        )

    except HTTPException:
        db.rollback()
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Failed to create task with files: {str(e)}")


# ===== TASK SUBMISSION WITH FILES =====

async def _upload_student_task_files(files: List[UploadFile], task_id: UUID, student_id: UUID, db: Session) -> List[dict]:
    """Upload student submission files and create student task attachments"""
    uploaded_files = []

    for file in files:
        if file.filename:  # Skip empty files
            try:
                # Save the file
                file_path = await file_storage.save_task_attachment(
                    file, str(student_id), str(task_id)
                )

                # Create database record for student attachment
                attachment = StudentTaskAttachment(
                    id=uuid4(),
                    file_url=file_path,
                    file_name=file.filename,
                    task_id=task_id,
                    student_id=student_id,
                    submission_date=datetime.utcnow()
                )

                db.add(attachment)

                # Add to response
                download_url = f"{settings.STATIC_FILES_URL}/{file_path}"
                uploaded_files.append({
                    "attachment_id": str(attachment.id),
                    "file_name": file.filename,
                    "download_url": download_url,
                    "submission_date": attachment.submission_date.isoformat()
                })

            except Exception as e:
                raise HTTPException(status_code=500, detail=f"Failed to upload file {file.filename}: {str(e)}")

    return uploaded_files


async def submit_task_with_files(
    db: Session,
    task_id: UUID,
    student_id: UUID,
    submission_text: Optional[str],
    submission_notes: Optional[str],
    files: List[UploadFile]
) -> TaskSubmissionWithFilesResponse:
    """Submit a task with text and file attachments"""
    try:
        # First submit the task using existing function
        submission = submit_task_comprehensive(
            db, task_id, student_id, submission_text, submission_notes
        )

        # Upload files if any
        uploaded_files = await _upload_student_task_files(files, task_id, student_id, db)

        # Commit all changes
        db.commit()

        return TaskSubmissionWithFilesResponse(
            submission=submission,
            uploaded_files=uploaded_files
        )

    except HTTPException:
        db.rollback()
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Failed to submit task with files: {str(e)}")


def get_student_task_attachments_with_data(db: Session, task_id: UUID, student_id: UUID):
    """Get student's task attachments with binary file data"""
    try:
        # Get attachments from database
        attachments = db.query(StudentTaskAttachment).filter(
            StudentTaskAttachment.task_id == task_id,
            StudentTaskAttachment.student_id == student_id
        ).all()

        result = []
        for attachment in attachments:
            try:
                # Get file binary data
                file_data = file_storage.get_file_as_base64(attachment.file_url)

                attachment_data = {
                    "id": attachment.id,
                    "file_url": attachment.file_url,
                    "file_name": attachment.file_name,
                    "task_id": attachment.task_id,
                    "student_id": attachment.student_id,
                    "submission_date": attachment.submission_date,
                    "download_url": f"/static/{attachment.file_url}",
                    # Binary data
                    "file_data": file_data["data"],
                    "content_type": file_data["content_type"],
                    "size_bytes": file_data["size_bytes"],
                    "data_url": file_data["data_url"]
                }
                result.append(attachment_data)
            except Exception as e:
                # If file can't be read, include attachment info without binary data
                logger.warning(f"Could not read file {attachment.file_url}: {str(e)}")
                attachment_data = {
                    "id": attachment.id,
                    "file_url": attachment.file_url,
                    "file_name": attachment.file_name,
                    "task_id": attachment.task_id,
                    "student_id": attachment.student_id,
                    "submission_date": attachment.submission_date,
                    "download_url": f"/static/{attachment.file_url}",
                    "file_data": None,
                    "content_type": None,
                    "size_bytes": 0,
                    "data_url": None,
                    "error": "File not accessible"
                }
                result.append(attachment_data)

        return result

    except SQLAlchemyError as e:
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")


def get_task_attachments_with_data(db: Session, task_id: UUID):
    """Get teacher's task attachments with binary file data"""
    try:
        # Get attachments from database
        attachments = db.query(TaskAttachment).filter(
            TaskAttachment.task_id == task_id
        ).all()

        result = []
        for attachment in attachments:
            try:
                # Get file binary data
                file_data = file_storage.get_file_as_base64(attachment.file_url)

                attachment_data = {
                    "id": attachment.id,
                    "file_url": attachment.file_url,
                    "file_name": attachment.file_name,
                    "task_id": attachment.task_id,
                    "upload_date": attachment.upload_date,
                    "download_url": f"/static/{attachment.file_url}",
                    # Binary data
                    "file_data": file_data["data"],
                    "content_type": file_data["content_type"],
                    "size_bytes": file_data["size_bytes"],
                    "data_url": file_data["data_url"]
                }
                result.append(attachment_data)
            except Exception as e:
                # If file can't be read, include attachment info without binary data
                logger.warning(f"Could not read file {attachment.file_url}: {str(e)}")
                attachment_data = {
                    "id": attachment.id,
                    "file_url": attachment.file_url,
                    "file_name": attachment.file_name,
                    "task_id": attachment.task_id,
                    "upload_date": attachment.upload_date,
                    "download_url": f"/static/{attachment.file_url}",
                    "file_data": None,
                    "content_type": None,
                    "size_bytes": 0,
                    "data_url": None,
                    "error": "File not accessible"
                }
                result.append(attachment_data)

        return result

    except SQLAlchemyError as e:
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")


def get_task_by_id_for_student_with_data(db: Session, task_id: UUID, student_id: UUID):
    """Get detailed task info for student including all attachments with binary data"""
    try:
        # Check if student is assigned to this task (individual assignment)
        task_student = db.query(TaskStudents).filter(
            TaskStudents.task_id == task_id,
            TaskStudents.student_id == student_id
        ).first()

        # If not found in individual assignments, check classroom assignments
        if not task_student:
            # Check if student is in a classroom assigned to this task
            classroom_assignment = db.query(TaskClassroom).join(
                StudentClassroom,
                TaskClassroom.classroom_id == StudentClassroom.classroom_id
            ).filter(
                TaskClassroom.task_id == task_id,
                StudentClassroom.student_id == student_id
            ).first()

            if not classroom_assignment:
                raise HTTPException(status_code=403, detail="Student is not assigned to this task.")

            # Create virtual task_student for classroom-assigned student
            task_student = type('obj', (object,), {
                'task_id': task_id,
                'student_id': student_id,
                'submission_date': None,
                'grade': None,
                'submission_text': None,
                'submission_notes': None,
                'feedback': None
            })()

        # Get task details
        task = db.query(Task).filter(Task.id == task_id).first()
        if not task:
            raise HTTPException(status_code=404, detail="Task not found.")

        # Get subject information
        subject = None
        if task.subject_id:
            subject = db.query(Subject).filter(Subject.id == task.subject_id).first()

        # Get chapters
        task_chapters = db.query(TaskChapter).filter(TaskChapter.task_id == task_id).all()
        chapters = []
        for tc in task_chapters:
            chapter = db.query(Chapter).filter(Chapter.id == tc.chapter_id).first()
            if chapter:
                chapters.append(ChapterOut.model_validate(chapter))

        # Get topics
        task_topics = db.query(TaskTopic).filter(TaskTopic.task_id == task_id).all()
        topics = []
        for tt in task_topics:
            topic = db.query(Topic).filter(Topic.id == tt.topic_id).first()
            if topic:
                topics.append(TopicOut.model_validate(topic))

        # Get subtopics
        task_subtopics = db.query(TaskSubTopic).filter(TaskSubTopic.task_id == task_id).all()
        subtopics = []
        for ts in task_subtopics:
            subtopic = db.query(SubTopic).filter(SubTopic.id == ts.subtopic_id).first()
            if subtopic:
                subtopics.append(SubTopicOut.model_validate(subtopic))

        # Get teacher attachments with binary data
        teacher_attachments = get_task_attachments_with_data(db, task_id)

        # Get student's own attachments with binary data
        my_attachments = get_student_task_attachments_with_data(db, task_id, student_id)

        # Build response
        return {
            "id": task.id,
            "name": task.name,
            "description": task.description,
            "status": task.status.value,
            "deadline": task.deadline,
            "accept_after_deadline": task.accept_after_deadline,
            "created_at": task.created_at,
            "updated_at": task.updated_at,
            "subject": SubjectOut.model_validate(subject) if subject else None,
            "chapters": chapters,
            "topics": topics,
            "subtopics": subtopics,
            "teacher_attachments": teacher_attachments,
            "my_attachments": my_attachments
        }

    except HTTPException:
        raise
    except SQLAlchemyError as e:
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")


def get_student_task_submission_complete(db: Session, task_id: UUID, student_id: UUID):
    """Get complete task submission data for a student including binary file data"""
    try:
        # Check if student is assigned to this task (individual assignment)
        task_student = db.query(TaskStudents).filter(
            TaskStudents.task_id == task_id,
            TaskStudents.student_id == student_id
        ).first()

        # If not found in individual assignments, check classroom assignments
        if not task_student:
            # Check if student is in a classroom assigned to this task
            classroom_assignment = db.query(TaskClassroom).join(
                StudentClassroom,
                TaskClassroom.classroom_id == StudentClassroom.classroom_id
            ).filter(
                TaskClassroom.task_id == task_id,
                StudentClassroom.student_id == student_id
            ).first()

            if not classroom_assignment:
                raise HTTPException(status_code=403, detail="Student is not assigned to this task")

            # Create virtual task_student for classroom-assigned student (no submission yet)
            task_student = type('obj', (object,), {
                'task_id': task_id,
                'student_id': student_id,
                'submission_date': None,
                'grade': None,
                'submission_text': None,
                'submission_notes': None,
                'feedback': None
            })()

        # Get task details
        task = db.query(Task).filter(Task.id == task_id).first()
        if not task:
            raise HTTPException(status_code=404, detail="Task not found")

        # Get subject information
        subject = None
        if task.subject_id:
            subject = db.query(Subject).filter(Subject.id == task.subject_id).first()

        # Get student information
        student = db.query(User).filter(User.id == student_id).first()
        if not student:
            raise HTTPException(status_code=404, detail="Student not found")

        # Get chapters
        task_chapters = db.query(TaskChapter).filter(TaskChapter.task_id == task_id).all()
        chapters = []
        for tc in task_chapters:
            chapter = db.query(Chapter).filter(Chapter.id == tc.chapter_id).first()
            if chapter:
                chapters.append(ChapterOut.model_validate(chapter))

        # Get topics
        task_topics = db.query(TaskTopic).filter(TaskTopic.task_id == task_id).all()
        topics = []
        for tt in task_topics:
            topic = db.query(Topic).filter(Topic.id == tt.topic_id).first()
            if topic:
                topics.append(TopicOut.model_validate(topic))

        # Get subtopics
        task_subtopics = db.query(TaskSubTopic).filter(TaskSubTopic.task_id == task_id).all()
        subtopics = []
        for ts in task_subtopics:
            subtopic = db.query(SubTopic).filter(SubTopic.id == ts.subtopic_id).first()
            if subtopic:
                subtopics.append(SubTopicOut.model_validate(subtopic))

        # Get teacher attachments with binary data
        teacher_attachments = get_task_attachments_with_data(db, task_id)

        # Get student's submission attachments with binary data
        submission_attachments = get_student_task_attachments_with_data(db, task_id, student_id)

        # Calculate submission status
        is_submitted = task_student.submission_date is not None
        is_graded = task_student.grade is not None
        is_late = False
        days_late = None

        if task.deadline and task_student.submission_date:
            if task_student.submission_date > task.deadline:
                is_late = True
                days_late = (task_student.submission_date.date() - task.deadline.date()).days

        # Build comprehensive response
        return {
            # Task information
            "task_id": task.id,
            "task_name": task.name,
            "task_description": task.description,
            "task_status": task.status.value,
            "deadline": task.deadline,
            "accept_after_deadline": task.accept_after_deadline,
            "created_at": task.created_at,
            "updated_at": task.updated_at,
            "subject": SubjectOut.model_validate(subject) if subject else None,
            "chapters": chapters,
            "topics": topics,
            "subtopics": subtopics,

            # Student information
            "student_id": student.id,
            "student_name": student.username,
            "student_email": student.email,

            # Submission details
            "submission_date": task_student.submission_date,
            "submission_text": task_student.submission_text,
            "submission_notes": task_student.submission_notes,
            "is_submitted": is_submitted,
            "is_late": is_late,
            "days_late": days_late,

            # Grading information
            "grade": task_student.grade,
            "feedback": task_student.feedback,
            "is_graded": is_graded,

            # Attachments with binary data
            "teacher_attachments": teacher_attachments,
            "submission_attachments": submission_attachments,
            "attachment_count": len(submission_attachments)
        }

    except HTTPException:
        raise
    except SQLAlchemyError as e:
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")


def _update_task_status_if_all_submitted(db: Session, task_id: UUID):
    """Helper function to update task status to COMPLETED if all assigned students have submitted"""
    try:
        # Get the task
        task = db.query(Task).filter(Task.id == task_id).first()
        if not task:
            return

        # Count total assigned students (individual + classroom assignments)
        # Individual assignments
        individual_assignments = db.query(TaskStudents).filter(TaskStudents.task_id == task_id).all()
        individual_student_ids = {ts.student_id for ts in individual_assignments}

        # Classroom assignments - get all students in assigned classrooms
        classroom_assignments = db.query(TaskClassroom).filter(TaskClassroom.task_id == task_id).all()
        classroom_student_ids = set()

        for tc in classroom_assignments:
            classroom_students = db.query(StudentClassroom).filter(
                StudentClassroom.classroom_id == tc.classroom_id
            ).all()
            classroom_student_ids.update(sc.student_id for sc in classroom_students)

        # Total unique students assigned to this task
        all_assigned_student_ids = individual_student_ids.union(classroom_student_ids)
        total_assigned = len(all_assigned_student_ids)

        if total_assigned == 0:
            return  # No students assigned

        # Count submitted students
        submitted_students = db.query(TaskStudents).filter(
            TaskStudents.task_id == task_id,
            TaskStudents.submission_date.isnot(None)
        ).all()
        submitted_student_ids = {ts.student_id for ts in submitted_students}

        # Check if all assigned students have submitted
        if submitted_student_ids >= all_assigned_student_ids:  # All assigned students have submitted
            task.status = TaskStatus.COMPLETED

    except Exception as e:
        # Don't raise exception here as this is a helper function
        # Just log the error and continue
        logger.warning(f"Failed to update task status for task {task_id}: {str(e)}")


def _is_task_fully_graded(db: Session, task_id: UUID) -> bool:
    """Helper function to check if all submitted students have been graded"""
    try:
        # Get all students who have submitted
        submitted_students = db.query(TaskStudents).filter(
            TaskStudents.task_id == task_id,
            TaskStudents.submission_date.isnot(None)
        ).all()

        if not submitted_students:
            return False  # No submissions, so not graded

        # Check if all submitted students have grades
        graded_students = db.query(TaskStudents).filter(
            TaskStudents.task_id == task_id,
            TaskStudents.submission_date.isnot(None),
            TaskStudents.grade.isnot(None)
        ).all()

        # Task is fully graded if all submitted students have grades
        return len(graded_students) == len(submitted_students)

    except Exception as e:
        logger.warning(f"Failed to check grading status for task {task_id}: {str(e)}")
        return False
