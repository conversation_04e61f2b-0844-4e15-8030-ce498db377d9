"""
Institute Event Control CRUD Operations for EduFair Platform

This module contains CRUD operations for institute event management including:
- Registration management and control
- Refund and cancellation processing
- Event analytics and reporting
- Bulk operations on registrations
"""

from datetime import datetime, timezone, timedelta
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, or_, func, desc, asc
from fastapi import HTTPException, status
from typing import List, Optional, Dict, Any
from uuid import UUID
from decimal import Decimal

# Import Models
from Models.Events import (
    Event, EventRegistration, EventTicket, EventPayment,
    RegistrationStatusEnum, PaymentStatusEnum, EventStatusEnum
)
from Models.users import User, UserTypeEnum

# Import Schemas
from Schemas.Institute.EventControl import (
    InstituteRegistrationControl, RegistrationControlResult, BulkRegistrationControlResponse,
    InstituteEventAnalytics, EventRegistrationSummary, InstituteEventRegistrationsResponse,
    EventCancellationRequest, EventCancellationResponse, TicketTransferRequest, TicketTransferResponse,
    RegistrationAction, RefundType, RefundReason
)


def verify_institute_event_access(db: Session, event_id: UUID, institute_id: UUID) -> Event:
    """Verify that institute has access to manage the event"""
    event = db.query(Event).filter(Event.id == event_id).first()
    
    if not event:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Event not found"
        )
    
    if event.organizer_id != institute_id and event.institute_id != institute_id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to manage this event"
        )
    
    return event


def get_institute_event_registrations(
    db: Session,
    event_id: UUID,
    institute_id: UUID,
    skip: int = 0,
    limit: int = 100,
    status_filter: Optional[List[RegistrationStatusEnum]] = None,
    search_query: Optional[str] = None
) -> InstituteEventRegistrationsResponse:
    """Get all registrations for an institute's event"""
    
    # Verify access
    event = verify_institute_event_access(db, event_id, institute_id)
    
    # Build query
    query = db.query(EventRegistration).options(
        joinedload(EventRegistration.ticket),
        joinedload(EventRegistration.user)
    ).filter(EventRegistration.event_id == event_id)
    
    # Apply filters
    if status_filter:
        query = query.filter(EventRegistration.status.in_(status_filter))
    
    if search_query:
        search_term = f"%{search_query}%"
        query = query.join(User).filter(
            or_(
                User.username.ilike(search_term),
                User.email.ilike(search_term),
                EventRegistration.registration_number.ilike(search_term)
            )
        )
    
    # Get total count
    total_count = query.count()
    
    # Get paginated results
    registrations = query.order_by(desc(EventRegistration.registered_at)).offset(skip).limit(limit).all()
    
    # Build registration summaries
    registration_summaries = []
    confirmed_count = 0
    pending_count = 0
    cancelled_count = 0
    attended_count = 0
    total_revenue = Decimal('0.00')
    pending_revenue = Decimal('0.00')
    
    for registration in registrations:
        user = registration.user
        ticket = registration.ticket
        
        # Calculate control flags
        can_refund = (
            registration.status in [RegistrationStatusEnum.CONFIRMED, RegistrationStatusEnum.PENDING] and
            registration.payment_status == PaymentStatusEnum.COMPLETED
        )
        
        can_transfer = (
            registration.status in [RegistrationStatusEnum.CONFIRMED, RegistrationStatusEnum.PENDING] and
            event.start_datetime > datetime.now(timezone.utc) + timedelta(hours=24)
        )
        
        can_modify = (
            registration.status in [RegistrationStatusEnum.CONFIRMED, RegistrationStatusEnum.PENDING] and
            event.start_datetime > datetime.now(timezone.utc) + timedelta(hours=48)
        )
        
        # Get payment info
        payment = db.query(EventPayment).filter(
            EventPayment.registration_id == registration.id
        ).first()
        
        summary = EventRegistrationSummary(
            registration_id=registration.id,
            registration_number=registration.registration_number,
            user_id=user.id,
            user_name=user.username,
            user_email=user.email,
            user_type=user.user_type.value if hasattr(user.user_type, 'value') else str(user.user_type),
            
            status=registration.status,
            quantity=registration.quantity,
            total_amount=registration.total_amount,
            currency=registration.currency,
            registered_at=registration.registered_at,
            confirmed_at=registration.confirmed_at,
            attended_at=registration.attended_at,
            cancelled_at=registration.cancelled_at,
            
            payment_status=registration.payment_status,
            payment_reference=registration.payment_reference,
            payment_method=payment.payment_method.value if payment and hasattr(payment.payment_method, 'value') else None,
            
            ticket_id=ticket.id if ticket else None,
            ticket_name=ticket.name if ticket else "Free Event",
            ticket_price=ticket.price if ticket else Decimal('0.00'),
            
            attendee_info=registration.attendee_info,
            special_requirements=registration.special_requirements,
            emergency_contact=registration.emergency_contact,
            
            can_refund=can_refund,
            can_transfer=can_transfer,
            can_modify=can_modify,
            
            qr_code=registration.qr_code,
            check_in_code=registration.check_in_code
        )
        
        registration_summaries.append(summary)
        
        # Count by status
        if registration.status == RegistrationStatusEnum.CONFIRMED:
            confirmed_count += 1
        elif registration.status == RegistrationStatusEnum.PENDING:
            pending_count += 1
        elif registration.status == RegistrationStatusEnum.CANCELLED:
            cancelled_count += 1
        elif registration.status == RegistrationStatusEnum.ATTENDED:
            attended_count += 1
        
        # Calculate revenue
        if registration.payment_status == PaymentStatusEnum.COMPLETED:
            total_revenue += registration.total_amount
        elif registration.payment_status == PaymentStatusEnum.PENDING:
            pending_revenue += registration.total_amount
    
    return InstituteEventRegistrationsResponse(
        event_id=event_id,
        event_title=event.title,
        registrations=registration_summaries,
        total_count=total_count,
        page=skip // limit + 1,
        per_page=limit,
        has_next=skip + limit < total_count,
        has_prev=skip > 0,
        confirmed_count=confirmed_count,
        pending_count=pending_count,
        cancelled_count=cancelled_count,
        attended_count=attended_count,
        total_revenue=total_revenue,
        pending_revenue=pending_revenue
    )


def process_registration_refund(
    db: Session,
    registration: EventRegistration,
    refund_type: RefundType,
    refund_amount: Optional[Decimal] = None,
    refund_reason: RefundReason = RefundReason.INSTITUTE_DECISION
) -> tuple[bool, Decimal, str]:
    """Process refund for a registration"""
    
    # Get payment record
    payment = db.query(EventPayment).filter(
        EventPayment.registration_id == registration.id,
        EventPayment.status == PaymentStatusEnum.COMPLETED
    ).first()
    
    if not payment:
        return False, Decimal('0.00'), "No completed payment found"
    
    # Calculate refund amount
    if refund_type == RefundType.FULL:
        actual_refund_amount = payment.amount
    elif refund_type == RefundType.PARTIAL:
        if refund_amount is None:
            return False, Decimal('0.00'), "Partial refund amount not specified"
        actual_refund_amount = min(refund_amount, payment.amount)
    else:  # RefundType.NONE
        actual_refund_amount = Decimal('0.00')
    
    try:
        # Update payment record
        payment.status = PaymentStatusEnum.REFUNDED
        payment.refund_amount = actual_refund_amount
        payment.refund_reason = refund_reason.value
        payment.refunded_at = datetime.now(timezone.utc)
        
        # Update registration status
        registration.status = RegistrationStatusEnum.CANCELLED
        registration.cancelled_at = datetime.now(timezone.utc)
        registration.payment_status = PaymentStatusEnum.REFUNDED
        
        # Update ticket availability
        if registration.ticket_id:
            ticket = db.query(EventTicket).filter(EventTicket.id == registration.ticket_id).first()
            if ticket:
                ticket.sold_quantity = max(0, ticket.sold_quantity - registration.quantity)
                ticket.available_quantity = ticket.total_quantity - ticket.sold_quantity
        
        db.commit()
        return True, actual_refund_amount, f"Refund of {actual_refund_amount} processed successfully"
        
    except Exception as e:
        db.rollback()
        return False, Decimal('0.00'), f"Refund processing failed: {str(e)}"


def control_registrations(
    db: Session,
    event_id: UUID,
    institute_id: UUID,
    control_request: InstituteRegistrationControl,
    processed_by: str
) -> BulkRegistrationControlResponse:
    """Process bulk registration control actions"""
    
    # Verify access
    event = verify_institute_event_access(db, event_id, institute_id)
    
    results = []
    successful_actions = 0
    failed_actions = 0
    total_refund_amount = Decimal('0.00')
    notifications_sent = 0
    
    for registration_id in control_request.registration_ids:
        try:
            # Get registration
            registration = db.query(EventRegistration).filter(
                EventRegistration.id == registration_id,
                EventRegistration.event_id == event_id
            ).first()
            
            if not registration:
                result = RegistrationControlResult(
                    registration_id=registration_id,
                    action_performed=control_request.action,
                    success=False,
                    message="Registration not found",
                    error_code="NOT_FOUND"
                )
                results.append(result)
                failed_actions += 1
                continue
            
            # Process action
            if control_request.action == RegistrationAction.APPROVE:
                registration.status = RegistrationStatusEnum.CONFIRMED
                registration.confirmed_at = datetime.now(timezone.utc)
                message = "Registration approved"
                
            elif control_request.action == RegistrationAction.REJECT:
                registration.status = RegistrationStatusEnum.CANCELLED
                registration.cancelled_at = datetime.now(timezone.utc)
                message = "Registration rejected"
                
            elif control_request.action == RegistrationAction.CANCEL:
                registration.status = RegistrationStatusEnum.CANCELLED
                registration.cancelled_at = datetime.now(timezone.utc)
                message = "Registration cancelled"
                
            elif control_request.action == RegistrationAction.REFUND:
                success, refund_amount, refund_message = process_registration_refund(
                    db, registration, 
                    control_request.refund_type or RefundType.FULL,
                    control_request.refund_amount,
                    control_request.refund_reason or RefundReason.INSTITUTE_DECISION
                )
                if success:
                    total_refund_amount += refund_amount
                    message = refund_message
                else:
                    result = RegistrationControlResult(
                        registration_id=registration_id,
                        action_performed=control_request.action,
                        success=False,
                        message=refund_message,
                        error_code="REFUND_FAILED"
                    )
                    results.append(result)
                    failed_actions += 1
                    continue
                
            elif control_request.action == RegistrationAction.MARK_ATTENDED:
                registration.status = RegistrationStatusEnum.ATTENDED
                registration.attended_at = datetime.now(timezone.utc)
                message = "Marked as attended"
                
            else:
                result = RegistrationControlResult(
                    registration_id=registration_id,
                    action_performed=control_request.action,
                    success=False,
                    message="Action not implemented",
                    error_code="NOT_IMPLEMENTED"
                )
                results.append(result)
                failed_actions += 1
                continue
            
            # Commit changes for this registration
            db.commit()
            
            # Create success result
            result = RegistrationControlResult(
                registration_id=registration_id,
                action_performed=control_request.action,
                success=True,
                message=message,
                new_status=registration.status,
                notification_sent=control_request.send_notification
            )
            
            if control_request.action == RegistrationAction.REFUND:
                result.refund_amount = refund_amount
            
            results.append(result)
            successful_actions += 1
            
            # Count notifications (would be sent in real implementation)
            if control_request.send_notification:
                notifications_sent += 1
                
        except Exception as e:
            db.rollback()
            result = RegistrationControlResult(
                registration_id=registration_id,
                action_performed=control_request.action,
                success=False,
                message=f"Processing failed: {str(e)}",
                error_code="PROCESSING_ERROR",
                error_details=str(e)
            )
            results.append(result)
            failed_actions += 1
    
    return BulkRegistrationControlResponse(
        total_processed=len(control_request.registration_ids),
        successful_actions=successful_actions,
        failed_actions=failed_actions,
        results=results,
        total_refund_amount=total_refund_amount,
        notifications_sent=notifications_sent,
        processed_by=processed_by
    )


def get_institute_event_analytics(
    db: Session,
    event_id: UUID,
    institute_id: UUID
) -> InstituteEventAnalytics:
    """Get comprehensive analytics for an institute's event"""

    # Verify access
    event = verify_institute_event_access(db, event_id, institute_id)

    # Get all registrations for the event
    registrations = db.query(EventRegistration).filter(
        EventRegistration.event_id == event_id
    ).all()

    # Initialize metrics
    total_registrations = len(registrations)
    confirmed_registrations = 0
    pending_registrations = 0
    cancelled_registrations = 0
    attended_registrations = 0

    total_revenue = Decimal('0.00')
    pending_revenue = Decimal('0.00')
    refunded_amount = Decimal('0.00')

    registrations_by_day = {}
    revenue_by_day = {}
    registrations_by_user_type = {}

    for registration in registrations:
        # Count by status
        if registration.status == RegistrationStatusEnum.CONFIRMED:
            confirmed_registrations += 1
        elif registration.status == RegistrationStatusEnum.PENDING:
            pending_registrations += 1
        elif registration.status == RegistrationStatusEnum.CANCELLED:
            cancelled_registrations += 1
        elif registration.status == RegistrationStatusEnum.ATTENDED:
            attended_registrations += 1

        # Calculate revenue
        if registration.payment_status == PaymentStatusEnum.COMPLETED:
            total_revenue += registration.total_amount
        elif registration.payment_status == PaymentStatusEnum.PENDING:
            pending_revenue += registration.total_amount
        elif registration.payment_status == PaymentStatusEnum.REFUNDED:
            refunded_amount += registration.total_amount

        # Group by registration date
        reg_date = registration.registered_at.date().isoformat()
        registrations_by_day[reg_date] = registrations_by_day.get(reg_date, 0) + 1

        if registration.payment_status == PaymentStatusEnum.COMPLETED:
            revenue_by_day[reg_date] = revenue_by_day.get(reg_date, Decimal('0.00')) + registration.total_amount

        # Group by user type
        user = db.query(User).filter(User.id == registration.user_id).first()
        if user:
            user_type = user.user_type.value if hasattr(user.user_type, 'value') else str(user.user_type)
            registrations_by_user_type[user_type] = registrations_by_user_type.get(user_type, 0) + 1

    # Get ticket metrics
    tickets = db.query(EventTicket).filter(EventTicket.event_id == event_id).all()
    total_tickets_available = sum(t.total_quantity for t in tickets) if tickets else 0
    tickets_sold = sum(t.sold_quantity for t in tickets) if tickets else 0
    tickets_remaining = total_tickets_available - tickets_sold

    # Calculate attendance metrics
    attendance_rate = 0.0
    no_show_rate = 0.0

    if confirmed_registrations > 0:
        attendance_rate = (attended_registrations / confirmed_registrations) * 100
        no_show_rate = ((confirmed_registrations - attended_registrations) / confirmed_registrations) * 100

    # Calculate net revenue
    net_revenue = total_revenue - refunded_amount

    # Convert Decimal values in revenue_by_day to float for JSON serialization
    revenue_by_day_serializable = {k: float(v) for k, v in revenue_by_day.items()}

    return InstituteEventAnalytics(
        event_id=event_id,
        event_title=event.title,
        event_status=event.status,

        total_registrations=total_registrations,
        confirmed_registrations=confirmed_registrations,
        pending_registrations=pending_registrations,
        cancelled_registrations=cancelled_registrations,
        attended_registrations=attended_registrations,

        total_revenue=total_revenue,
        pending_revenue=pending_revenue,
        refunded_amount=refunded_amount,
        net_revenue=net_revenue,

        total_tickets_available=total_tickets_available,
        tickets_sold=tickets_sold,
        tickets_remaining=tickets_remaining,

        attendance_rate=attendance_rate,
        no_show_rate=no_show_rate,

        registrations_by_day=registrations_by_day,
        revenue_by_day=revenue_by_day_serializable,
        registrations_by_user_type=registrations_by_user_type
    )


def cancel_institute_event(
    db: Session,
    event_id: UUID,
    institute_id: UUID,
    cancellation_request: EventCancellationRequest,
    cancelled_by: str
) -> EventCancellationResponse:
    """Cancel an event and process refunds"""

    # Verify access
    event = verify_institute_event_access(db, event_id, institute_id)

    if event.status == EventStatusEnum.CANCELLED:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Event is already cancelled"
        )

    try:
        # Get all active registrations
        registrations = db.query(EventRegistration).filter(
            EventRegistration.event_id == event_id,
            EventRegistration.status.in_([RegistrationStatusEnum.PENDING, RegistrationStatusEnum.CONFIRMED])
        ).all()

        total_registrations_affected = len(registrations)
        refunds_processed = 0
        total_refund_amount = Decimal('0.00')
        notifications_sent = 0
        notification_failures = 0

        # Process refunds for each registration
        for registration in registrations:
            if cancellation_request.refund_policy != RefundType.NONE:
                refund_amount = None
                if cancellation_request.refund_policy == RefundType.PARTIAL and cancellation_request.custom_refund_percentage:
                    refund_amount = registration.total_amount * (Decimal(cancellation_request.custom_refund_percentage) / 100)

                success, actual_refund, message = process_registration_refund(
                    db, registration,
                    cancellation_request.refund_policy,
                    refund_amount,
                    RefundReason.EVENT_CANCELLED
                )

                if success:
                    refunds_processed += 1
                    total_refund_amount += actual_refund
            else:
                # Just cancel without refund
                registration.status = RegistrationStatusEnum.CANCELLED
                registration.cancelled_at = datetime.now(timezone.utc)

            # Count notifications (would be sent in real implementation)
            if cancellation_request.notify_attendees:
                notifications_sent += 1

        # Update event status
        event.status = EventStatusEnum.CANCELLED
        event.updated_at = datetime.now(timezone.utc)

        # Handle rescheduling
        new_event_details = None
        if cancellation_request.is_rescheduled:
            if cancellation_request.new_start_datetime:
                event.start_datetime = cancellation_request.new_start_datetime
            if cancellation_request.new_end_datetime:
                event.end_datetime = cancellation_request.new_end_datetime
            if cancellation_request.new_location:
                event.location = cancellation_request.new_location

            # Change status to published if rescheduled
            event.status = EventStatusEnum.PUBLISHED

            new_event_details = {
                "start_datetime": event.start_datetime.isoformat(),
                "end_datetime": event.end_datetime.isoformat(),
                "location": event.location
            }

        db.commit()

        return EventCancellationResponse(
            event_id=event_id,
            cancellation_successful=True,
            message="Event cancelled successfully",
            total_registrations_affected=total_registrations_affected,
            refunds_processed=refunds_processed,
            total_refund_amount=total_refund_amount,
            notifications_sent=notifications_sent,
            notification_failures=notification_failures,
            cancelled_by=cancelled_by,
            rescheduled=cancellation_request.is_rescheduled,
            new_event_details=new_event_details
        )

    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Event cancellation failed: {str(e)}"
        )


def transfer_ticket(
    db: Session,
    registration_id: UUID,
    institute_id: UUID,
    transfer_request: TicketTransferRequest,
    transferred_by: str
) -> TicketTransferResponse:
    """Transfer a ticket to another user"""

    # Get registration
    registration = db.query(EventRegistration).options(
        joinedload(EventRegistration.event)
    ).filter(EventRegistration.id == registration_id).first()

    if not registration:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Registration not found"
        )

    # Verify institute access
    event = registration.event
    if event.organizer_id != institute_id and event.institute_id != institute_id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to transfer this ticket"
        )

    # Check if transfer is allowed
    now = datetime.now(timezone.utc)
    if event.start_datetime <= now + timedelta(hours=24):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Cannot transfer ticket less than 24 hours before event"
        )

    if registration.status not in [RegistrationStatusEnum.PENDING, RegistrationStatusEnum.CONFIRMED]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Can only transfer pending or confirmed registrations"
        )

    # Find new user
    new_user = db.query(User).filter(User.email == transfer_request.new_user_email).first()
    if not new_user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="New user not found"
        )

    # Get original user
    original_user = db.query(User).filter(User.id == registration.user_id).first()

    try:
        # Create new registration for new user
        new_registration = EventRegistration(
            event_id=registration.event_id,
            user_id=new_user.id,
            ticket_id=registration.ticket_id,
            quantity=registration.quantity,
            total_amount=registration.total_amount,
            currency=registration.currency,
            status=RegistrationStatusEnum.PENDING,  # New user needs to confirm
            payment_status=registration.payment_status,
            payment_reference=registration.payment_reference,
            attendee_info=registration.attendee_info,
            special_requirements=registration.special_requirements,
            emergency_contact=registration.emergency_contact,
            dietary_preferences=registration.dietary_preferences,
            accessibility_needs=registration.accessibility_needs,
            registered_at=datetime.now(timezone.utc)
        )

        # Generate new registration number
        import secrets
        new_registration.registration_number = f"REG_{secrets.token_hex(4).upper()}"

        db.add(new_registration)

        # Cancel original registration
        registration.status = RegistrationStatusEnum.CANCELLED
        registration.cancelled_at = datetime.now(timezone.utc)

        db.commit()

        return TicketTransferResponse(
            transfer_successful=True,
            message="Ticket transferred successfully",
            original_user_email=original_user.email,
            new_user_email=new_user.email,
            registration_id=registration.id,
            new_registration_id=new_registration.id,
            original_user_notified=transfer_request.notify_original_user,
            new_user_notified=transfer_request.notify_new_user,
            transferred_by=transferred_by,
            confirmation_required=transfer_request.require_new_user_confirmation
        )

    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Ticket transfer failed: {str(e)}"
        )


def get_refund_summary(
    db: Session,
    event_id: UUID,
    institute_id: UUID
) -> Dict[str, Any]:
    """Get summary of refunds for an event"""

    # Verify access
    event = verify_institute_event_access(db, event_id, institute_id)

    # Get all refunded registrations
    refunded_registrations = db.query(EventRegistration).filter(
        EventRegistration.event_id == event_id,
        EventRegistration.payment_status == PaymentStatusEnum.REFUNDED
    ).all()

    total_refunds = len(refunded_registrations)
    total_refund_amount = sum(reg.total_amount for reg in refunded_registrations)

    # Get refunds by reason
    refunds_by_reason = {}
    refunds_by_date = {}

    for registration in refunded_registrations:
        # Get payment record for refund details
        payment = db.query(EventPayment).filter(
            EventPayment.registration_id == registration.id
        ).first()

        if payment and hasattr(payment, 'refund_reason'):
            reason = payment.refund_reason or "unknown"
            refunds_by_reason[reason] = refunds_by_reason.get(reason, 0) + 1

        if registration.cancelled_at:
            date_key = registration.cancelled_at.date().isoformat()
            refunds_by_date[date_key] = refunds_by_date.get(date_key, 0) + 1

    return {
        "event_id": event_id,
        "event_title": event.title,
        "total_refunds": total_refunds,
        "total_refund_amount": float(total_refund_amount),
        "refunds_by_reason": refunds_by_reason,
        "refunds_by_date": refunds_by_date,
        "generated_at": datetime.now(timezone.utc).isoformat()
    }
