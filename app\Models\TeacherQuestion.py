from sqlalchemy import Column, ForeignKey
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from .baseModel import BaseModel

class TeacherQuestion(BaseModel):
    __tablename__ = 'teacher_questions'

    teacher_profile_id = Column(UUID(as_uuid=True), ForeignKey('teacher_profiles.id'), nullable=False)
    question_id = Column(UUID(as_uuid=True), ForeignKey('questions.id'), nullable=False)

    # Relationships (optional, for ORM navigation)
    teacher_profile = relationship('TeacherProfile', backref='teacher_questions')
    question = relationship('Question', backref='teacher_questions') 