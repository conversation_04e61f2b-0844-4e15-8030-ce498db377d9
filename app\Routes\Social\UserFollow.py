"""
User Follow Routes for EduFair Platform

This module provides comprehensive APIs for social following functionality.

CONSOLIDATED ENDPOINTS:
======================

🎯 PRIMARY ENDPOINTS (Use these):
- GET /dashboard - Complete social dashboard with stats, recent activity, and suggestions
- GET /discover - Unified discovery (suggestions + followable users)
- GET /profile/{user_id} - Complete social profile with follow status and mutual connections
- POST /follow - Follow a user
- DELETE /unfollow/{user_id} - Unfollow a user
- POST /bulk-follow - Follow multiple users at once
- POST /bulk-unfollow - Unfollow multiple users at once

📊 DETAILED ENDPOINTS (For specific needs):
- GET /followers/{user_id} - Get user's followers
- GET /following/{user_id} - Get users that user follows
- GET /mutual/{user_id} - Get mutual follows

⚠️ DEPRECATED ENDPOINTS (Still work but use primary endpoints instead):
- GET /suggestions - Use /discover?discovery_type=suggestions
- GET /followable - Use /discover?discovery_type=followable
- GET /status/{user_id} - Use /profile/{user_id}
- GET /stats/{user_id} - Use /dashboard or /profile/{user_id}

This consolidation reduces API calls and provides better user experience.
"""

from fastapi import APIRouter, Depends, HTTPException, status, Request
from sqlalchemy.orm import Session
from uuid import UUID
from typing import List

# Import dependencies
from config.session import get_db
from config.deps import oauth2_scheme, get_current_user
from config.permission import require_type

# Import CRUD operations
from Cruds.Social.UserFollow import (
    create_follow, delete_follow, check_follow_status,
    get_followers, get_following, get_mutual_follows,
    get_user_follow_stats, get_follow_suggestions,
    bulk_follow, bulk_unfollow
)
from Cruds.users import get_all_followable_users
from Cruds.Notifications import create_follow_notification
from Cruds.AdminLog import log_user_action

# Import schemas
from Schemas.Social.UserFollow import (
    UserFollowCreate, UserFollowResponse, FollowListResponse,
    UserFollowStats, FollowSuggestion, BulkFollowRequest,
    BulkFollowResponse, UserWithFollowStatus
)
from Schemas.users import UserOut
from Schemas.AdminLog import LogAction, ResourceType

# Import models
from Models.users import User

router = APIRouter()


# ==================== FOLLOW OPERATIONS ====================

@router.post("/follow", response_model=UserFollowResponse)
def follow_user(
    follow_data: UserFollowCreate,
    request: Request,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """
    Follow another user.
    
    Creates a follow relationship between the current user and the specified user.
    """
    current_user = get_current_user(token, db)
    
    try:
        # Create follow relationship
        follow_result = create_follow(db, current_user.id, follow_data)
        
        # Create notification for the followed user
        try:
            create_follow_notification(db, current_user.id, follow_data.following_id)
        except Exception as e:
            print(f"Failed to create follow notification: {e}")
        
        # Log the action
        log_user_action(
            db=db,
            action=LogAction.FOLLOW_CREATE,
            resource_type=ResourceType.FOLLOW,
            user_id=current_user.id,
            resource_id=follow_result.id,
            details={
                "follower_username": current_user.username,
                "following_id": str(follow_data.following_id)
            },
            request=request
        )
        
        return follow_result
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to follow user: {str(e)}"
        )


@router.delete("/unfollow/{user_id}")
def unfollow_user(
    user_id: UUID,
    request: Request,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """
    Unfollow a user.
    
    Removes the follow relationship between the current user and the specified user.
    """
    current_user = get_current_user(token, db)
    
    try:
        # Delete follow relationship
        success = delete_follow(db, current_user.id, user_id)
        
        # Log the action
        log_user_action(
            db=db,
            action=LogAction.FOLLOW_DELETE,
            resource_type=ResourceType.FOLLOW,
            user_id=current_user.id,
            details={
                "follower_username": current_user.username,
                "unfollowed_user_id": str(user_id)
            },
            request=request
        )
        
        return {"message": "Successfully unfollowed user", "success": success}
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to unfollow user: {str(e)}"
        )


@router.get("/profile/{user_id}")
def get_user_social_profile(
    user_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """
    Get comprehensive social profile for a user.

    Combines user info, follow status, and social statistics.
    """
    current_user = get_current_user(token, db)

    try:
        # Get follow status between current user and target user
        follow_status = check_follow_status(db, current_user.id, user_id)

        # Get target user's social stats
        user_stats = get_user_follow_stats(db, user_id)

        # Get mutual follows (limited to 5 for preview)
        mutual_follows = get_mutual_follows(db, user_id, page=1, page_size=5)

        profile = {
            "user_id": user_id,
            "follow_status": follow_status,
            "social_stats": user_stats,
            "mutual_follows_preview": mutual_follows,
            "actions": {
                "follow": f"/api/social/follow/follow",
                "unfollow": f"/api/social/follow/unfollow/{user_id}",
                "view_followers": f"/api/social/follow/followers/{user_id}",
                "view_following": f"/api/social/follow/following/{user_id}",
                "view_mutual": f"/api/social/follow/mutual/{user_id}"
            }
        }

        return profile

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get user social profile: {str(e)}"
        )


@router.get("/status/{user_id}")
def get_follow_status(
    user_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """
    Check follow status between current user and another user.

    Returns information about the follow relationship between users.
    DEPRECATED: Use /profile/{user_id} for comprehensive social profile.
    """
    current_user = get_current_user(token, db)

    try:
        status_info = check_follow_status(db, current_user.id, user_id)
        return status_info

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get follow status: {str(e)}"
        )


# ==================== FOLLOW LISTS ====================

@router.get("/followers/{user_id}", response_model=FollowListResponse)
def get_user_followers(
    user_id: UUID,
    page: int = 1,
    page_size: int = 20,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """
    Get list of users following the specified user.
    
    Returns paginated list of followers.
    """
    current_user = get_current_user(token, db)
    
    try:
        followers = get_followers(db, user_id, page, page_size)
        return followers
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get followers: {str(e)}"
        )


@router.get("/following/{user_id}", response_model=FollowListResponse)
def get_user_following(
    user_id: UUID,
    page: int = 1,
    page_size: int = 20,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """
    Get list of users that the specified user is following.
    
    Returns paginated list of users being followed.
    """
    current_user = get_current_user(token, db)
    
    try:
        following = get_following(db, user_id, page, page_size)
        return following
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get following: {str(e)}"
        )


@router.get("/mutual/{user_id}", response_model=FollowListResponse)
def get_mutual_followers(
    user_id: UUID,
    page: int = 1,
    page_size: int = 20,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """
    Get list of mutual follows between current user and specified user.
    
    Returns users who follow both the current user and the specified user.
    """
    current_user = get_current_user(token, db)
    
    try:
        mutual_follows = get_mutual_follows(db, user_id, page, page_size)
        return mutual_follows
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get mutual follows: {str(e)}"
        )


# ==================== SOCIAL DASHBOARD ====================

@router.get("/dashboard")
def get_social_dashboard(
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """
    Comprehensive social dashboard for the current user.

    Returns user's social statistics, recent activity, and discovery options.
    """
    current_user = get_current_user(token, db)

    try:
        # Get user's follow stats
        user_stats = get_user_follow_stats(db, current_user.id)

        # Get recent followers (last 5)
        recent_followers = get_followers(db, current_user.id, page=1, page_size=5)

        # Get follow suggestions (top 5)
        suggestions = get_follow_suggestions(db, current_user.id, limit=5)

        # Get follow status with a few users for quick actions
        followable_preview = get_all_followable_users(db, current_user.id, skip=0, limit=5)

        dashboard = {
            "user_id": current_user.id,
            "username": current_user.username,
            "statistics": user_stats,
            "recent_followers": recent_followers,
            "follow_suggestions": suggestions,
            "followable_preview": followable_preview,
            "quick_actions": {
                "discover_more": "/api/social/follow/discover",
                "view_all_followers": f"/api/social/follow/followers/{current_user.id}",
                "view_all_following": f"/api/social/follow/following/{current_user.id}",
                "bulk_follow": "/api/social/follow/bulk-follow"
            }
        }

        return dashboard

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get social dashboard: {str(e)}"
        )


# ==================== FOLLOW STATISTICS ====================

@router.get("/stats/{user_id}", response_model=UserFollowStats)
def get_follow_statistics(
    user_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """
    Get follow statistics for a user.

    Returns follower count, following count, and mutual follows count.
    DEPRECATED: Use /dashboard for comprehensive social stats.
    """
    current_user = get_current_user(token, db)

    try:
        stats = get_user_follow_stats(db, user_id)
        return stats

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get follow stats: {str(e)}"
        )


@router.get("/discover")
def get_social_discovery(
    discovery_type: str = "all",  # "suggestions", "followable", "all"
    limit: int = 20,
    skip: int = 0,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """
    Comprehensive social discovery endpoint.

    Combines multiple discovery features:
    - suggestions: AI-based follow suggestions from mutual connections
    - followable: All users you can follow (students/teachers not already followed)
    - all: Both suggestions and followable users
    """
    current_user = get_current_user(token, db)

    try:
        result = {
            "user_id": current_user.id,
            "discovery_type": discovery_type,
            "suggestions": [],
            "followable_users": [],
            "total_suggestions": 0,
            "total_followable": 0
        }

        if discovery_type in ["suggestions", "all"]:
            suggestions = get_follow_suggestions(db, current_user.id, limit)
            result["suggestions"] = suggestions
            result["total_suggestions"] = len(suggestions)

        if discovery_type in ["followable", "all"]:
            followable = get_all_followable_users(db, current_user.id, skip, limit)
            result["followable_users"] = followable
            result["total_followable"] = len(followable)

        return result

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get social discovery: {str(e)}"
        )


@router.get("/suggestions", response_model=List[FollowSuggestion])
def get_follow_suggestions_endpoint(
    limit: int = 10,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """
    Get follow suggestions for the current user.

    Returns suggested users to follow based on mutual connections.
    DEPRECATED: Use /discover?discovery_type=suggestions instead.
    """
    current_user = get_current_user(token, db)

    try:
        suggestions = get_follow_suggestions(db, current_user.id, limit)
        return suggestions

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get follow suggestions: {str(e)}"
        )


@router.get("/followable", response_model=List[UserOut])
def get_followable_users_endpoint(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """
    Get users that the current user can follow.

    Returns students and teachers that are not already followed.
    DEPRECATED: Use /discover?discovery_type=followable instead.
    """
    current_user = get_current_user(token, db)

    try:
        followable = get_all_followable_users(db, current_user.id, skip, limit)
        return followable

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get followable users: {str(e)}"
        )


# ==================== BULK OPERATIONS ====================

@router.post("/bulk-follow", response_model=BulkFollowResponse)
def bulk_follow_users(
    bulk_request: BulkFollowRequest,
    request: Request,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """
    Follow multiple users at once.
    
    Allows following up to 50 users in a single request.
    """
    current_user = get_current_user(token, db)
    
    try:
        result = bulk_follow(db, current_user.id, bulk_request.user_ids)
        
        # Log the bulk action
        log_user_action(
            db=db,
            action=LogAction.FOLLOW_CREATE,
            resource_type=ResourceType.FOLLOW,
            user_id=current_user.id,
            details={
                "action_type": "bulk_follow",
                "user_count": len(bulk_request.user_ids),
                "success_count": result["success_count"],
                "failure_count": result["failure_count"]
            },
            request=request
        )
        
        return BulkFollowResponse(**result)
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to bulk follow users: {str(e)}"
        )


@router.post("/bulk-unfollow")
def bulk_unfollow_users(
    bulk_request: BulkFollowRequest,
    request: Request,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """
    Unfollow multiple users at once.
    
    Allows unfollowing up to 50 users in a single request.
    """
    current_user = get_current_user(token, db)
    
    try:
        result = bulk_unfollow(db, current_user.id, bulk_request.user_ids)
        
        # Log the bulk action
        log_user_action(
            db=db,
            action=LogAction.FOLLOW_DELETE,
            resource_type=ResourceType.FOLLOW,
            user_id=current_user.id,
            details={
                "action_type": "bulk_unfollow",
                "user_count": len(bulk_request.user_ids),
                "success_count": result["success_count"],
                "failure_count": result["failure_count"]
            },
            request=request
        )
        
        return result
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to bulk unfollow users: {str(e)}"
        )
