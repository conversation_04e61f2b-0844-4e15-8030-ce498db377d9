"""
Pydantic schemas for email verification
"""

from pydantic import BaseModel, EmailStr, Field
from typing import Optional
from datetime import datetime
from uuid import UUID

class EmailVerificationRequest(BaseModel):
    """Request to send verification email"""
    email: EmailStr = Field(..., description="Email address to verify")

class EmailVerificationResponse(BaseModel):
    """Response after sending verification email"""
    message: str = Field(..., description="Success message")
    email: str = Field(..., description="Email address where verification was sent")

class VerifyEmailRequest(BaseModel):
    """Request to verify email with code"""
    verification_code: str = Field(..., min_length=6, max_length=6, description="6-digit verification code")

class VerifyEmailResponse(BaseModel):
    """Response after email verification"""
    message: str = Field(..., description="Success message")
    user_id: str = Field(..., description="User ID")
    email: str = Field(..., description="Verified email address")
    verified_at: str = Field(..., description="Verification timestamp")

class ResendVerificationRequest(BaseModel):
    """Request to resend verification email"""
    user_id: Optional[UUID] = Field(None, description="User ID (if authenticated)")
    email: Optional[EmailStr] = Field(None, description="Email address (if not authenticated)")

class PasswordResetRequest(BaseModel):
    """Request to send password reset email"""
    email: EmailStr = Field(..., description="Email address for password reset")

class PasswordResetResponse(BaseModel):
    """Response after password reset request"""
    message: str = Field(..., description="Success message")

class ResetPasswordRequest(BaseModel):
    """Request to reset password with code"""
    reset_code: str = Field(..., min_length=6, max_length=6, description="6-digit reset code")
    email: EmailStr = Field(..., description="Email address")
    new_password: str = Field(..., min_length=8, description="New password")
    confirm_password: str = Field(..., description="Confirm new password")

class ResetPasswordResponse(BaseModel):
    """Response after password reset"""
    message: str = Field(..., description="Success message")
    user_id: str = Field(..., description="User ID")

class EmailVerificationTokenOut(BaseModel):
    """Email verification token output"""
    id: UUID
    user_id: UUID
    email: str
    verification_code: str
    token_type: str
    expires_at: datetime
    is_used: bool
    used_at: Optional[datetime]
    created_at: datetime

    class Config:
        from_attributes = True

class EmailVerificationStatusOut(BaseModel):
    """Email verification status output"""
    is_email_verified: bool = Field(..., description="Whether email is verified")
    email: str = Field(..., description="Email address")
    verification_sent_at: Optional[datetime] = Field(None, description="When last verification email was sent")
    verified_at: Optional[datetime] = Field(None, description="When email was verified")

class BulkEmailVerificationRequest(BaseModel):
    """Request to send verification emails to multiple users"""
    user_ids: list[UUID] = Field(..., description="List of user IDs")
    
class BulkEmailVerificationResponse(BaseModel):
    """Response after bulk email verification"""
    total_requested: int = Field(..., description="Total emails requested")
    successful: int = Field(..., description="Successfully sent emails")
    failed: int = Field(..., description="Failed to send emails")
    details: list[dict] = Field(..., description="Detailed results")

# Admin schemas
class AdminEmailVerificationStatsOut(BaseModel):
    """Admin statistics for email verification"""
    total_users: int
    verified_users: int
    unverified_users: int
    verification_rate: float
    pending_tokens: int
    expired_tokens: int
    recent_verifications: int  # Last 24 hours

class AdminTokenManagementOut(BaseModel):
    """Admin token management output"""
    token_id: UUID
    user_id: UUID
    user_email: str
    user_username: str
    token_type: str
    created_at: datetime
    expires_at: datetime
    is_used: bool
    used_at: Optional[datetime]
    ip_address: Optional[str]
    user_agent: Optional[str]
    
    class Config:
        from_attributes = True

class AdminCleanupTokensResponse(BaseModel):
    """Response after cleaning up expired tokens"""
    message: str
    tokens_cleaned: int
    cleanup_timestamp: datetime
