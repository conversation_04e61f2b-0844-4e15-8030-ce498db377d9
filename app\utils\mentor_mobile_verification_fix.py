"""
Temporary fix to remove mobile verification requirement for mentors
This script updates existing mentors to have mobile verification set to True
"""

from sqlalchemy.orm import Session
from Models.users import User, UserTypeEnum
from config.session import engine
import logging

logger = logging.getLogger(__name__)


def auto_verify_mentor_mobiles(db: Session) -> dict:
    """
    Temporary fix: Auto-verify mobile numbers for all mentors
    
    This function sets is_mobile_verified=True for all mentor accounts
    as a temporary workaround while mobile verification is being removed
    from the mentor invitation process.
    
    Returns:
        dict: Summary of the operation
    """
    
    try:
        # Get all mentors with unverified mobile numbers
        unverified_mentors = db.query(User).filter(
            User.user_type == UserTypeEnum.mentor,
            User.is_mobile_verified == False
        ).all()
        
        updated_count = 0
        
        for mentor in unverified_mentors:
            mentor.is_mobile_verified = True
            updated_count += 1
            logger.info(f"Auto-verified mobile for mentor: {mentor.username} ({mentor.email})")
        
        db.commit()
        
        result = {
            "message": "Successfully auto-verified mobile numbers for mentors",
            "mentors_updated": updated_count,
            "note": "This is a temporary fix - mobile verification requirement has been removed for mentors"
        }
        
        logger.info(f"Auto-verified mobile numbers for {updated_count} mentors")
        return result
        
    except Exception as e:
        db.rollback()
        logger.error(f"Failed to auto-verify mentor mobiles: {str(e)}")
        raise


def check_mentor_verification_status(db: Session) -> dict:
    """
    Check the current verification status of all mentors
    
    Returns:
        dict: Statistics about mentor verification status
    """
    
    try:
        # Get mentor verification statistics
        total_mentors = db.query(User).filter(User.user_type == UserTypeEnum.mentor).count()
        
        email_verified = db.query(User).filter(
            User.user_type == UserTypeEnum.mentor,
            User.is_email_verified == True
        ).count()
        
        mobile_verified = db.query(User).filter(
            User.user_type == UserTypeEnum.mentor,
            User.is_mobile_verified == True
        ).count()
        
        both_verified = db.query(User).filter(
            User.user_type == UserTypeEnum.mentor,
            User.is_email_verified == True,
            User.is_mobile_verified == True
        ).count()
        
        # Get mentors with profile verification
        from Models.users import MentorProfile
        from sqlalchemy.orm import joinedload
        
        profile_verified = db.query(User).join(MentorProfile).filter(
            User.user_type == UserTypeEnum.mentor,
            MentorProfile.is_verified == True
        ).count()
        
        return {
            "total_mentors": total_mentors,
            "email_verified": email_verified,
            "mobile_verified": mobile_verified,
            "both_verified": both_verified,
            "profile_verified": profile_verified,
            "email_verification_rate": round((email_verified / total_mentors * 100), 2) if total_mentors > 0 else 0,
            "mobile_verification_rate": round((mobile_verified / total_mentors * 100), 2) if total_mentors > 0 else 0,
            "profile_verification_rate": round((profile_verified / total_mentors * 100), 2) if total_mentors > 0 else 0
        }
        
    except Exception as e:
        logger.error(f"Failed to check mentor verification status: {str(e)}")
        raise


def run_mentor_mobile_fix():
    """
    Main function to run the mentor mobile verification fix
    """
    
    print("🔧 Mentor Mobile Verification Fix")
    print("=" * 50)
    
    with Session(engine) as db:
        try:
            # Check current status
            print("\n📊 Current Mentor Verification Status:")
            status = check_mentor_verification_status(db)
            
            print(f"Total Mentors: {status['total_mentors']}")
            print(f"Email Verified: {status['email_verified']} ({status['email_verification_rate']}%)")
            print(f"Mobile Verified: {status['mobile_verified']} ({status['mobile_verification_rate']}%)")
            print(f"Profile Verified: {status['profile_verified']} ({status['profile_verification_rate']}%)")
            
            # Ask for confirmation
            if status['mobile_verified'] < status['total_mentors']:
                print(f"\n⚠️  Found {status['total_mentors'] - status['mobile_verified']} mentors with unverified mobile numbers")
                
                confirm = input("Do you want to auto-verify mobile numbers for all mentors? (y/N): ").strip().lower()
                
                if confirm == 'y':
                    # Apply the fix
                    print("\n🔄 Applying mobile verification fix...")
                    result = auto_verify_mentor_mobiles(db)
                    
                    print(f"✅ {result['message']}")
                    print(f"📱 Updated {result['mentors_updated']} mentor mobile verifications")
                    print(f"📝 {result['note']}")
                    
                    # Check status again
                    print("\n📊 Updated Mentor Verification Status:")
                    new_status = check_mentor_verification_status(db)
                    print(f"Mobile Verified: {new_status['mobile_verified']} ({new_status['mobile_verification_rate']}%)")
                    
                else:
                    print("❌ Operation cancelled")
            else:
                print("✅ All mentors already have verified mobile numbers")
                
        except Exception as e:
            print(f"❌ Error: {str(e)}")


if __name__ == "__main__":
    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    run_mentor_mobile_fix()
