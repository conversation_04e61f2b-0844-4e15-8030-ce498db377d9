"""
CRUD operations for Certification Management System
"""
from typing import List, Optional, Dict, Any
import uuid
import hashlib
import secrets
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, or_, func, desc, case
from fastapi import HTTPException
from datetime import datetime, timezone, timedelta

# Import Models
from Models.Certifications import (
    CertificationTemplate, CompetitionCertification, CertificationCriteria,
    CertificationAuditLog, CertificationVerification,
    CertificationTypeEnum, CertificationStatusEnum, PerformanceTierEnum
)
from Models.Events import Event, EventTypeEnum
from Models.users import User, UserTypeEnum
from Models.Exam import StudentExamAttempt
from Models.Competitions import CompetitionMentorAssignment

# Import Schemas
from Schemas.Certifications.Certifications import (
    CertificationTemplateCreate, CertificationTemplateUpdate, CertificationTemplateOut,
    CompetitionCertificationCreate, CompetitionCertificationUpdate, CompetitionCertificationOut,
    CertificationCriteriaCreate, CertificationCriteriaUpdate, CertificationCriteriaOut,
    MentorEvaluationRequest, CertificationApprovalRequest, CertificationBatchRequest,
    CertificationAnalytics, CertificationListResponse, CertificationVerificationResponse
)


# ===== Certification Template CRUD Operations =====

def create_certification_template(
    db: Session, 
    template_data: CertificationTemplateCreate,
    institute_id: uuid.UUID
) -> CertificationTemplateOut:
    """Create a new certification template"""
    try:
        # Set institute_id
        template_data.institute_id = institute_id
        
        # Create template
        db_template = CertificationTemplate(
            id=uuid.uuid4(),
            **template_data.model_dump(),
            created_at=datetime.now(timezone.utc),
            updated_at=datetime.now(timezone.utc)
        )
        
        db.add(db_template)
        db.commit()
        db.refresh(db_template)
        
        # Convert to dict to avoid relationship issues
        template_dict = {
            "id": db_template.id,
            "name": db_template.name,
            "description": db_template.description,
            "certification_type": db_template.certification_type,
            "institute_id": db_template.institute_id,
            "template_design": db_template.template_design,
            "certificate_text": db_template.certificate_text,
            "logo_url": db_template.logo_url,
            "background_image_url": db_template.background_image_url,
            "min_score_percentage": db_template.min_score_percentage,
            "max_score_percentage": db_template.max_score_percentage,
            "min_percentile": db_template.min_percentile,
            "max_percentile": db_template.max_percentile,
            "required_performance_tier": db_template.required_performance_tier,
            "min_participants_required": db_template.min_participants_required,
            "requires_mentor_approval": db_template.requires_mentor_approval,
            "auto_award": db_template.auto_award,
            "is_active": db_template.is_active,
            "is_default": db_template.is_default,
            "created_at": db_template.created_at,
            "updated_at": db_template.updated_at
        }

        return CertificationTemplateOut.model_validate(template_dict)
        
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Error creating certification template: {str(e)}")


def get_certification_templates(
    db: Session,
    institute_id: uuid.UUID,
    certification_type: Optional[CertificationTypeEnum] = None,
    active_only: bool = True
) -> List[CertificationTemplateOut]:
    """Get certification templates for an institute"""
    try:
        query = db.query(CertificationTemplate).filter(
            CertificationTemplate.institute_id == institute_id
        )
        
        if certification_type:
            query = query.filter(CertificationTemplate.certification_type == certification_type)
        
        if active_only:
            query = query.filter(CertificationTemplate.is_active == True)
        
        templates = query.all()

        # Convert templates to dict format to avoid relationship issues
        template_outs = []
        for template in templates:
            template_dict = {
                "id": template.id,
                "name": template.name,
                "description": template.description,
                "certification_type": template.certification_type,
                "institute_id": template.institute_id,
                "template_design": template.template_design,
                "certificate_text": template.certificate_text,
                "logo_url": template.logo_url,
                "background_image_url": template.background_image_url,
                "min_score_percentage": template.min_score_percentage,
                "max_score_percentage": template.max_score_percentage,
                "min_percentile": template.min_percentile,
                "max_percentile": template.max_percentile,
                "required_performance_tier": template.required_performance_tier,
                "min_participants_required": template.min_participants_required,
                "requires_mentor_approval": template.requires_mentor_approval,
                "auto_award": template.auto_award,
                "is_active": template.is_active,
                "is_default": template.is_default,
                "created_at": template.created_at,
                "updated_at": template.updated_at
            }
            template_outs.append(CertificationTemplateOut.model_validate(template_dict))

        return template_outs
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching templates: {str(e)}")


# ===== Competition Results Analysis =====

def calculate_competition_statistics(db: Session, competition_id: uuid.UUID) -> Dict[str, Any]:
    """Calculate competition statistics for certification awarding"""
    try:
        # Get competition details first
        competition = db.query(Event).filter(Event.id == competition_id).first()
        if not competition or not competition.competition_exam_id:
            return {"total_participants": 0, "scores": [], "statistics": {}}

        # Get all exam attempts for this competition
        # Use completed_at IS NOT NULL as a proxy for submitted attempts
        attempts = db.query(StudentExamAttempt).filter(
            StudentExamAttempt.exam_id == competition.competition_exam_id,
            StudentExamAttempt.completed_at.isnot(None)
        ).all()

        if not attempts:
            return {"total_participants": 0, "scores": [], "statistics": {}}

        # Calculate scores from answers (we'll need to compute them)
        scores = []
        for attempt in attempts:
            # Calculate score based on correct answers
            total_questions = db.query(func.count()).select_from(
                db.query(StudentExamAnswer).filter(StudentExamAnswer.attempt_id == attempt.id).subquery()
            ).scalar()

            correct_answers = db.query(func.count()).filter(
                StudentExamAnswer.attempt_id == attempt.id,
                StudentExamAnswer.is_correct == True
            ).scalar()

            if total_questions > 0:
                score = (correct_answers / total_questions) * 100
                scores.append(score)
            else:
                scores.append(0)
        scores.sort(reverse=True)  # Highest to lowest
        
        total_participants = len(scores)
        
        if total_participants == 0:
            return {"total_participants": 0, "scores": [], "statistics": {}}
        
        # Calculate percentiles for each score
        score_percentiles = {}
        for i, score in enumerate(scores):
            percentile = ((total_participants - i - 1) / total_participants) * 100
            score_percentiles[score] = percentile
        
        # Calculate performance tiers
        def get_performance_tier(percentile: float) -> PerformanceTierEnum:
            if percentile >= 99:
                return PerformanceTierEnum.TOP_1_PERCENT
            elif percentile >= 95:
                return PerformanceTierEnum.TOP_5_PERCENT
            elif percentile >= 90:
                return PerformanceTierEnum.TOP_10_PERCENT
            elif percentile >= 75:
                return PerformanceTierEnum.TOP_25_PERCENT
            elif percentile >= 50:
                return PerformanceTierEnum.TOP_50_PERCENT
            elif percentile >= 40:
                return PerformanceTierEnum.ABOVE_AVERAGE
            elif percentile >= 20:
                return PerformanceTierEnum.AVERAGE
            else:
                return PerformanceTierEnum.BELOW_AVERAGE
        
        # Create participant results
        participant_results = []
        for i, attempt in enumerate(attempts):
            if i < len(scores):
                score = scores[i]
                rank = scores.index(score) + 1
                percentile = score_percentiles[score]
                tier = get_performance_tier(percentile)

                participant_results.append({
                    "participant_id": attempt.student_id,
                    "exam_attempt_id": attempt.id,
                    "score": score,
                    "percentage": score,  # Score is already a percentage
                    "rank": rank,
                    "percentile": percentile,
                    "tier": tier,
                    "completion_time": attempt.duration_seconds
                })
        
        statistics = {
            "total_participants": total_participants,
            "average_score": sum(scores) / len(scores),
            "median_score": scores[len(scores) // 2],
            "highest_score": max(scores),
            "lowest_score": min(scores),
            "score_range": max(scores) - min(scores),
            "participants": participant_results
        }
        
        return statistics
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error calculating statistics: {str(e)}")


# ===== Automatic Certification Generation =====

def generate_certifications_for_competition(
    db: Session,
    competition_id: uuid.UUID,
    auto_evaluate: bool = True
) -> Dict[str, Any]:
    """Generate certifications for competition participants based on criteria"""
    try:
        # Get competition
        competition = db.query(Event).filter(Event.id == competition_id).first()
        if not competition:
            raise HTTPException(status_code=404, detail="Competition not found")
        
        # Get certification criteria for this competition
        criteria_list = db.query(CertificationCriteria).filter(
            CertificationCriteria.competition_id == competition_id,
            CertificationCriteria.is_active == True
        ).all()
        
        if not criteria_list:
            raise HTTPException(status_code=400, detail="No certification criteria defined for this competition")
        
        # Calculate competition statistics
        stats = calculate_competition_statistics(db, competition_id)
        if stats["total_participants"] == 0:
            return {"message": "No participants found", "certifications_created": 0}
        
        participants = stats["participants"]
        certifications_created = []
        
        # Process each certification criteria
        for criteria in criteria_list:
            # Get template
            template = db.query(CertificationTemplate).filter(
                CertificationTemplate.id == criteria.template_id
            ).first()
            
            if not template:
                continue
            
            # Find eligible participants
            eligible_participants = []
            
            for participant in participants:
                is_eligible = True
                
                # Check score criteria
                if criteria.min_score and participant["score"] < criteria.min_score:
                    is_eligible = False
                
                if criteria.min_percentage and participant["percentage"] < criteria.min_percentage:
                    is_eligible = False
                
                # Check rank criteria
                if criteria.max_rank and participant["rank"] > criteria.max_rank:
                    is_eligible = False
                
                # Check percentile criteria
                if criteria.min_percentile and participant["percentile"] < criteria.min_percentile:
                    is_eligible = False
                
                # Check performance tier
                if criteria.required_tier and participant["tier"] != criteria.required_tier:
                    # Allow higher tiers to qualify for lower tier certifications
                    tier_hierarchy = [
                        PerformanceTierEnum.TOP_1_PERCENT,
                        PerformanceTierEnum.TOP_5_PERCENT,
                        PerformanceTierEnum.TOP_10_PERCENT,
                        PerformanceTierEnum.TOP_25_PERCENT,
                        PerformanceTierEnum.TOP_50_PERCENT,
                        PerformanceTierEnum.ABOVE_AVERAGE,
                        PerformanceTierEnum.AVERAGE,
                        PerformanceTierEnum.BELOW_AVERAGE
                    ]
                    
                    participant_tier_index = tier_hierarchy.index(participant["tier"])
                    required_tier_index = tier_hierarchy.index(criteria.required_tier)
                    
                    if participant_tier_index > required_tier_index:
                        is_eligible = False
                
                if is_eligible:
                    eligible_participants.append(participant)
            
            # Apply award limits
            if criteria.max_awards:
                eligible_participants = eligible_participants[:criteria.max_awards]
            elif criteria.award_percentage:
                max_awards = int(stats["total_participants"] * criteria.award_percentage / 100)
                eligible_participants = eligible_participants[:max_awards]
            
            # Create certifications for eligible participants
            for participant in eligible_participants:
                # Check if certification already exists
                existing = db.query(CompetitionCertification).filter(
                    and_(
                        CompetitionCertification.competition_id == competition_id,
                        CompetitionCertification.participant_id == participant["participant_id"],
                        CompetitionCertification.certification_type == criteria.certification_type
                    )
                ).first()
                
                if existing:
                    continue  # Skip if already exists
                
                # Generate certificate title
                cert_title = f"{criteria.certification_type.value.title()} Certificate"
                if criteria.certification_type == CertificationTypeEnum.WINNER:
                    cert_title = f"Winner Certificate - Rank #{participant['rank']}"
                elif criteria.certification_type == CertificationTypeEnum.EXCELLENCE:
                    cert_title = f"Excellence Certificate - Top {participant['percentile']:.1f}%"
                
                # Create certification
                certification_data = CompetitionCertificationCreate(
                    competition_id=competition_id,
                    participant_id=participant["participant_id"],
                    template_id=template.id,
                    certification_type=criteria.certification_type,
                    certificate_title=cert_title,
                    certificate_description=f"Awarded for outstanding performance in {competition.title}",
                    final_score=participant["score"],
                    score_percentage=participant["percentage"],
                    percentile_rank=participant["percentile"],
                    performance_tier=participant["tier"],
                    rank_position=participant["rank"],
                    total_participants=stats["total_participants"],
                    achievement_details={
                        "rank": participant["rank"],
                        "percentile": participant["percentile"],
                        "tier": participant["tier"].value,
                        "completion_time": participant["completion_time"]
                    }
                )
                
                # Create certification record
                db_certification = CompetitionCertification(
                    id=uuid.uuid4(),
                    **certification_data.model_dump(),
                    status=CertificationStatusEnum.PENDING,
                    verification_code=generate_verification_code(),
                    created_at=datetime.now(timezone.utc),
                    updated_at=datetime.now(timezone.utc)
                )
                
                # Auto-approve if criteria allows and auto_evaluate is True
                if criteria.auto_award and auto_evaluate:
                    db_certification.status = CertificationStatusEnum.APPROVED
                    db_certification.approval_date = datetime.now(timezone.utc)
                
                db.add(db_certification)
                certifications_created.append({
                    "participant_id": participant["participant_id"],
                    "certification_type": criteria.certification_type.value,
                    "rank": participant["rank"],
                    "percentile": participant["percentile"]
                })
        
        db.commit()
        
        return {
            "message": "Certifications generated successfully",
            "total_participants": stats["total_participants"],
            "certifications_created": len(certifications_created),
            "details": certifications_created
        }
        
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Error generating certifications: {str(e)}")


# ===== Mentor Evaluation =====

def submit_mentor_evaluation(
    db: Session,
    evaluation_data: MentorEvaluationRequest,
    mentor_id: uuid.UUID
) -> CompetitionCertificationOut:
    """Submit mentor evaluation for a certification"""
    try:
        # Get certification
        certification = db.query(CompetitionCertification).filter(
            CompetitionCertification.id == evaluation_data.certification_id
        ).first()
        
        if not certification:
            raise HTTPException(status_code=404, detail="Certification not found")
        
        # Verify mentor is assigned to this competition
        assignment = db.query(CompetitionMentorAssignment).filter(
            and_(
                CompetitionMentorAssignment.competition_id == certification.competition_id,
                CompetitionMentorAssignment.mentor_id == mentor_id
            )
        ).first()
        
        if not assignment:
            raise HTTPException(status_code=403, detail="Mentor not assigned to this competition")
        
        # Update certification with mentor evaluation
        certification.evaluated_by = mentor_id
        certification.mentor_comments = evaluation_data.mentor_comments
        certification.mentor_rating = evaluation_data.mentor_rating
        certification.evaluation_date = datetime.now(timezone.utc)
        
        # Update status based on recommendation
        if evaluation_data.recommendation == "approve":
            certification.status = CertificationStatusEnum.APPROVED
            certification.approval_date = datetime.now(timezone.utc)
            certification.approved_by = mentor_id
        elif evaluation_data.recommendation == "reject":
            certification.status = CertificationStatusEnum.REVOKED
        
        certification.updated_at = datetime.now(timezone.utc)
        
        # Create audit log
        audit_log = CertificationAuditLog(
            id=uuid.uuid4(),
            certification_id=certification.id,
            action="evaluated",
            performed_by=mentor_id,
            details=evaluation_data.model_dump(),
            notes=f"Mentor evaluation: {evaluation_data.recommendation}"
        )
        db.add(audit_log)
        
        db.commit()
        db.refresh(certification)
        
        return CompetitionCertificationOut.model_validate(certification)
        
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Error submitting evaluation: {str(e)}")


# ===== Helper Functions =====

def generate_verification_code() -> str:
    """Generate a unique verification code"""
    return secrets.token_urlsafe(16)


def generate_certificate_hash(certification_data: Dict[str, Any]) -> str:
    """Generate a hash for certificate verification"""
    hash_string = f"{certification_data['id']}{certification_data['participant_id']}{certification_data['competition_id']}"
    return hashlib.sha256(hash_string.encode()).hexdigest()


def get_certifications_for_competition(
    db: Session,
    competition_id: uuid.UUID,
    status: Optional[CertificationStatusEnum] = None
) -> CertificationListResponse:
    """Get all certifications for a competition"""
    try:
        query = db.query(CompetitionCertification).filter(
            CompetitionCertification.competition_id == competition_id
        )
        
        if status:
            query = query.filter(CompetitionCertification.status == status)
        
        certifications = query.all()
        
        # Count by status
        total = len(certifications)
        pending_evaluation = len([c for c in certifications if c.status == CertificationStatusEnum.PENDING])
        pending_approval = len([c for c in certifications if c.status == CertificationStatusEnum.APPROVED])
        issued = len([c for c in certifications if c.status == CertificationStatusEnum.ISSUED])
        
        cert_outs = [CompetitionCertificationOut.model_validate(cert) for cert in certifications]
        
        return CertificationListResponse(
            certifications=cert_outs,
            total=total,
            pending_evaluation=pending_evaluation,
            pending_approval=pending_approval,
            issued=issued
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching certifications: {str(e)}")


def get_mentor_pending_evaluations(
    db: Session,
    mentor_id: uuid.UUID
) -> List[CompetitionCertificationOut]:
    """Get certifications pending evaluation by a specific mentor"""
    try:
        # Get competitions where this mentor is assigned
        assigned_competitions = db.query(CompetitionMentorAssignment.competition_id).filter(
            CompetitionMentorAssignment.mentor_id == mentor_id
        ).subquery()

        # Get pending certifications for those competitions
        certifications = db.query(CompetitionCertification).filter(
            and_(
                CompetitionCertification.competition_id.in_(assigned_competitions),
                CompetitionCertification.status == CertificationStatusEnum.PENDING,
                CompetitionCertification.evaluated_by.is_(None)
            )
        ).all()

        return [CompetitionCertificationOut.model_validate(cert) for cert in certifications]

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching pending evaluations: {str(e)}")


def verify_certificate(
    db: Session,
    verification_code: str
) -> CertificationVerificationResponse:
    """Verify a certificate using verification code"""
    try:
        certification = db.query(CompetitionCertification).filter(
            CompetitionCertification.verification_code == verification_code
        ).first()

        if not certification:
            return CertificationVerificationResponse(
                is_valid=False,
                verification_details={"error": "Certificate not found"},
                error_message="Invalid verification code"
            )

        # Check if certificate is issued and not revoked
        is_valid = (
            certification.status == CertificationStatusEnum.ISSUED and
            certification.is_verified and
            (certification.expiry_date is None or certification.expiry_date > datetime.now(timezone.utc))
        )

        verification_details = {
            "verification_code": verification_code,
            "status": certification.status.value,
            "issued_date": certification.issued_date.isoformat() if certification.issued_date else None,
            "expiry_date": certification.expiry_date.isoformat() if certification.expiry_date else None,
            "verification_timestamp": datetime.now(timezone.utc).isoformat()
        }

        return CertificationVerificationResponse(
            is_valid=is_valid,
            certification=CompetitionCertificationOut.model_validate(certification) if is_valid else None,
            verification_details=verification_details,
            error_message=None if is_valid else "Certificate is not valid or has expired"
        )

    except Exception as e:
        return CertificationVerificationResponse(
            is_valid=False,
            verification_details={"error": str(e)},
            error_message="Verification failed"
        )
