"""
Event Routes for EduFair Platform

This module contains all API routes for the Event Management system including:
- Event CRUD operations
- Event filtering and searching
- Event registration management
- Event analytics and statistics
"""

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session
from typing import List, Optional
from uuid import UUID

# Import dependencies
from config.session import get_db
from config.deps import oauth2_scheme, get_current_user
from config.permission import require_type

# Import CRUD operations
from Cruds.Events.Events import (
    create_event, get_event_by_id, get_all_events, update_event, delete_event,
    get_events_by_organizer, get_featured_events, get_events_by_category
)

# Import schemas
from Schemas.Events.Events import (
    EventCreate, EventUpdate, EventOut, EventDetailedOut, EventMinimalOut
)
from Schemas.Events.EventManagement import EventListFilter

router = APIRouter()


# ==================== EVENT ROUTES ====================

@router.post("/", response_model=EventOut)
def create_event_endpoint(
    event: EventCreate,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """
    Create a new event.
    
    Only institutes can create events.
    """
    current_user = get_current_user(token, db)
    return create_event(db, event, current_user.id)


@router.get("/", response_model=List[EventOut])
def get_all_events_endpoint(
    skip: int = Query(0, ge=0, description="Number of events to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Number of events to return"),
    category: Optional[str] = Query(None, description="Filter by category"),
    location: Optional[str] = Query(None, description="Filter by location"),
    is_featured: Optional[bool] = Query(None, description="Filter by featured status"),
    is_virtual: Optional[bool] = Query(None, description="Filter by virtual events"),
    db: Session = Depends(get_db),
    token: Optional[str] = Depends(oauth2_scheme)
):
    """
    Get all events with optional filtering.
    
    Public endpoint - no authentication required for viewing published events.
    """
    # Build filters
    filters = EventListFilter()
    if category:
        try:
            from Models.Events import EventCategoryEnum
            # Convert to uppercase to match enum values
            filters.category = EventCategoryEnum(category.upper())
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid category. Must be one of: workshop, conference, webinar, competition"
            )
    if location:
        filters.location = location
    if is_featured is not None:
        filters.is_featured = is_featured
    if is_virtual is not None:
        filters.is_virtual = is_virtual
    
    # Get current user if authenticated
    user_id = None
    if token:
        try:
            current_user = get_current_user(token, db)
            user_id = current_user.id
        except:
            pass  # Continue as anonymous user
    
    return get_all_events(db, skip, limit, filters, user_id)


@router.get("/featured", response_model=List[EventMinimalOut])
def get_featured_events_endpoint(
    limit: int = Query(10, ge=1, le=50, description="Number of featured events to return"),
    db: Session = Depends(get_db)
):
    """
    Get featured events for homepage slider.
    
    Public endpoint - no authentication required.
    """
    return get_featured_events(db, limit)


@router.get("/category/{category}", response_model=List[EventMinimalOut])
def get_events_by_category_endpoint(
    category: str,
    limit: int = Query(20, ge=1, le=100, description="Number of events to return"),
    db: Session = Depends(get_db)
):
    """
    Get events by category.
    
    Public endpoint - no authentication required.
    """
    try:
        from Models.Events import EventCategoryEnum
        # Convert to uppercase to match enum values
        category_enum = EventCategoryEnum(category.upper())
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid category. Must be one of: workshop, conference, webinar, competition"
        )
    
    return get_events_by_category(db, category_enum, limit)


@router.get("/my-events", response_model=List[EventOut])
def get_my_events_endpoint(
    skip: int = Query(0, ge=0, description="Number of events to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Number of events to return"),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """
    Get all events created by the current institute.

    Only institutes can access this endpoint.
    """
    current_user = get_current_user(token, db)
    return get_events_by_organizer(db, current_user.id, skip, limit)


@router.get("/my-registrations")
def redirect_to_registrations():
    """
    Redirect to the correct registrations endpoint.

    This prevents the /{event_id} route from catching /my-registrations
    """
    raise HTTPException(
        status_code=status.HTTP_301_MOVED_PERMANENTLY,
        detail="This endpoint has moved. Use /api/events/registrations/my-registrations instead",
        headers={"Location": "/api/events/registrations/my-registrations"}
    )


@router.get("/{event_id}", response_model=EventDetailedOut)
def get_event_by_id_endpoint(
    event_id: UUID,
    db: Session = Depends(get_db),
    token: Optional[str] = Depends(oauth2_scheme)
):
    """
    Get event by ID with detailed information.
    
    Public endpoint - no authentication required for viewing published events.
    Authentication optional for checking registration status.
    """
    # Get current user if authenticated
    user_id = None
    if token:
        try:
            current_user = get_current_user(token, db)
            user_id = current_user.id
        except:
            pass  # Continue as anonymous user
    
    return get_event_by_id(db, event_id, user_id)


@router.put("/{event_id}", response_model=EventOut)
def update_event_endpoint(
    event_id: UUID,
    event_update: EventUpdate,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """
    Update an existing event.
    
    Only the event organizer (institute) can update the event.
    """
    current_user = get_current_user(token, db)
    return update_event(db, event_id, event_update, current_user.id)


@router.delete("/{event_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_event_endpoint(
    event_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """
    Delete an event.
    
    Only the event organizer (institute) can delete the event.
    Cannot delete events with existing registrations.
    """
    current_user = get_current_user(token, db)
    delete_event(db, event_id, current_user.id)
    return None


# ==================== EVENT CATEGORIES ENDPOINT ====================

@router.get("/categories/list")
def get_event_categories():
    """
    Get all available event categories.
    
    Public endpoint - returns the fixed list of event categories.
    """
    from Models.Events import EventCategoryEnum
    
    categories = []
    for category in EventCategoryEnum:
        categories.append({
            "value": category.value,
            "label": category.value.title(),
            "description": _get_category_description(category.value)
        })
    
    return {
        "categories": categories,
        "total": len(categories)
    }


# ==================== HELPER FUNCTIONS ====================

def _get_category_description(category: str) -> str:
    """Get description for event category."""
    descriptions = {
        "WORKSHOP": "Interactive learning sessions and hands-on training",
        "CONFERENCE": "Professional conferences, seminars, and symposiums",
        "WEBINAR": "Online educational sessions and virtual presentations",
        "COMPETITION": "Academic competitions, contests, and challenges"
    }
    return descriptions.get(category, "Educational event")
