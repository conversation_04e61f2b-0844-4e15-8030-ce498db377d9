"""
Admin Logging Middleware for EduFair Platform

This middleware automatically logs all route activities to the admin log.
"""

from fastapi import Request, Response
from fastapi.routing import APIRoute
from starlette.middleware.base import BaseHTTPMiddleware
from sqlalchemy.orm import Session
from typing import Callable, Optional
import time
import json
from uuid import UUID

from config.session import SessionLocal
from config.deps import get_current_user_from_token
from Cruds.AdminLog import create_admin_log
from Schemas.AdminLog import LogAction, ResourceType


class AdminLoggingMiddleware(BaseHTTPMiddleware):
    """
    Middleware to automatically log all API route activities.
    
    Logs every request with user information, route details, and response status.
    """
    
    def __init__(self, app, exclude_paths: Optional[list] = None):
        super().__init__(app)
        self.exclude_paths = exclude_paths or [
            "/docs",
            "/redoc", 
            "/openapi.json",
            "/favicon.ico",
            "/health",
            "/ping"
        ]
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Process the request and log the activity"""
        
        # Skip logging for excluded paths
        if any(request.url.path.startswith(path) for path in self.exclude_paths):
            return await call_next(request)
        
        # Skip logging for static files
        if request.url.path.startswith("/static/"):
            return await call_next(request)
        
        start_time = time.time()
        
        # Get database session
        db = SessionLocal()
        
        try:
            # Extract user information
            user_id = None
            user_info = {}
            
            # Try to get user from Authorization header
            auth_header = request.headers.get("Authorization")
            if auth_header and auth_header.startswith("Bearer "):
                token = auth_header.split(" ")[1]
                try:
                    current_user = get_current_user_from_token(token, db)
                    if current_user:
                        user_id = current_user.id
                        user_info = {
                            "username": current_user.username,
                            "user_type": current_user.user_type.value,
                            "email": current_user.email
                        }
                except Exception:
                    # If token is invalid, continue without user info
                    pass
            
            # Process the request
            response = await call_next(request)
            
            # Calculate processing time
            process_time = time.time() - start_time
            
            # Determine action and resource type from the route
            action, resource_type, resource_id = self._determine_action_and_resource(request, response)
            
            # Extract request details
            request_details = await self._extract_request_details(request, response, process_time, user_info)
            
            # Log the activity
            try:
                create_admin_log(
                    db=db,
                    action=action,
                    resource_type=resource_type,
                    user_id=user_id,
                    resource_id=resource_id,
                    details=request_details,
                    ip_address=self._get_client_ip(request),
                    user_agent=request.headers.get("user-agent")
                )
            except Exception as e:
                # Don't fail the request if logging fails
                print(f"Failed to log admin activity: {e}")
            
            return response
            
        except Exception as e:
            # If there's an error in the middleware, don't break the request
            print(f"Error in admin logging middleware: {e}")
            return await call_next(request)
        
        finally:
            db.close()
    
    def _determine_action_and_resource(self, request: Request, response: Response) -> tuple:
        """Determine the action and resource type from the request"""
        
        method = request.method.upper()
        path = request.url.path.lower()
        status_code = response.status_code
        
        # Default values
        action = LogAction.ADMIN_ACCESS
        resource_type = ResourceType.SYSTEM
        resource_id = None
        
        # Extract resource ID from path if present
        path_parts = path.strip('/').split('/')
        for part in path_parts:
            try:
                resource_id = UUID(part)
                break
            except (ValueError, TypeError):
                continue
        
        # Determine action based on method and path
        if method == "POST":
            if "login" in path:
                action = LogAction.USER_LOGIN
                resource_type = ResourceType.USER
            elif "register" in path or "signup" in path:
                action = LogAction.USER_CREATE
                resource_type = ResourceType.USER
            elif "follow" in path:
                action = LogAction.FOLLOW_CREATE
                resource_type = ResourceType.FOLLOW
            elif "message" in path or "chat" in path:
                action = LogAction.MESSAGE_SEND
                resource_type = ResourceType.MESSAGE
            elif "event" in path:
                action = LogAction.EVENT_CREATE
                resource_type = ResourceType.EVENT
            elif "registration" in path:
                action = LogAction.REGISTRATION_CREATE
                resource_type = ResourceType.REGISTRATION
            else:
                action = LogAction.USER_CREATE  # Generic create action
        
        elif method == "PUT" or method == "PATCH":
            if "user" in path:
                action = LogAction.USER_UPDATE
                resource_type = ResourceType.USER
            elif "event" in path:
                action = LogAction.EVENT_UPDATE
                resource_type = ResourceType.EVENT
            else:
                action = LogAction.USER_UPDATE  # Generic update action
        
        elif method == "DELETE":
            if "user" in path:
                action = LogAction.USER_DELETE
                resource_type = ResourceType.USER
            elif "follow" in path or "unfollow" in path:
                action = LogAction.FOLLOW_DELETE
                resource_type = ResourceType.FOLLOW
            elif "message" in path:
                action = LogAction.MESSAGE_DELETE
                resource_type = ResourceType.MESSAGE
            elif "event" in path:
                action = LogAction.EVENT_DELETE
                resource_type = ResourceType.EVENT
            else:
                action = LogAction.USER_DELETE  # Generic delete action
        
        elif method == "GET":
            action = LogAction.ADMIN_ACCESS
            
            # Determine resource type for GET requests
            if "user" in path:
                resource_type = ResourceType.USER
            elif "event" in path:
                resource_type = ResourceType.EVENT
            elif "message" in path or "chat" in path:
                resource_type = ResourceType.MESSAGE
            elif "notification" in path:
                resource_type = ResourceType.NOTIFICATION
            elif "follow" in path:
                resource_type = ResourceType.FOLLOW
            elif "admin" in path or "log" in path:
                resource_type = ResourceType.SYSTEM
        
        # Handle logout
        if "logout" in path:
            action = LogAction.USER_LOGOUT
            resource_type = ResourceType.USER
        
        # Handle export operations
        if "export" in path:
            action = LogAction.ADMIN_EXPORT
            resource_type = ResourceType.SYSTEM
        
        return action, resource_type, resource_id
    
    async def _extract_request_details(self, request: Request, response: Response, process_time: float, user_info: dict) -> dict:
        """Extract detailed information about the request"""
        
        details = {
            "method": request.method,
            "path": str(request.url.path),
            "query_params": dict(request.query_params),
            "status_code": response.status_code,
            "process_time_seconds": round(process_time, 4),
            "user_info": user_info,
            "request_size": request.headers.get("content-length"),
            "response_size": response.headers.get("content-length")
        }
        
        # Add request body for non-GET requests (but limit size and exclude sensitive data)
        if request.method.upper() != "GET":
            try:
                # Note: In real middleware, request body might already be consumed
                # This is a simplified approach
                content_type = request.headers.get("content-type", "")
                if "application/json" in content_type:
                    details["content_type"] = "application/json"
                elif "multipart/form-data" in content_type:
                    details["content_type"] = "multipart/form-data"
                elif "application/x-www-form-urlencoded" in content_type:
                    details["content_type"] = "application/x-www-form-urlencoded"
            except Exception:
                pass
        
        # Add response headers (selected ones)
        response_headers = {}
        for header_name in ["content-type", "location", "set-cookie"]:
            if header_name in response.headers:
                if header_name == "set-cookie":
                    response_headers[header_name] = "[REDACTED]"  # Don't log actual cookies
                else:
                    response_headers[header_name] = response.headers[header_name]
        
        if response_headers:
            details["response_headers"] = response_headers
        
        # Add error information for failed requests
        if response.status_code >= 400:
            details["error"] = True
            details["error_category"] = self._categorize_error(response.status_code)
        
        return details
    
    def _get_client_ip(self, request: Request) -> Optional[str]:
        """Extract client IP address from request"""
        
        # Check for forwarded headers first (for reverse proxies)
        forwarded_for = request.headers.get("x-forwarded-for")
        if forwarded_for:
            # Take the first IP in the chain
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("x-real-ip")
        if real_ip:
            return real_ip
        
        # Fall back to direct client IP
        if request.client:
            return request.client.host
        
        return None
    
    def _categorize_error(self, status_code: int) -> str:
        """Categorize error based on status code"""
        
        if 400 <= status_code < 500:
            if status_code == 401:
                return "authentication_error"
            elif status_code == 403:
                return "authorization_error"
            elif status_code == 404:
                return "not_found_error"
            elif status_code == 422:
                return "validation_error"
            else:
                return "client_error"
        elif 500 <= status_code < 600:
            return "server_error"
        else:
            return "unknown_error"


# Helper function to add the middleware to FastAPI app
def add_admin_logging_middleware(app, exclude_paths: Optional[list] = None):
    """
    Add admin logging middleware to FastAPI application.
    
    Args:
        app: FastAPI application instance
        exclude_paths: List of paths to exclude from logging
    """
    app.add_middleware(AdminLoggingMiddleware, exclude_paths=exclude_paths)
