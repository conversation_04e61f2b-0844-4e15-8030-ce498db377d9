import os
import json
import uuid
from typing import Dict, Any, <PERSON>, Optional, <PERSON><PERSON>
from sqlalchemy.orm import Session
import httpx

from Models.Exam import Exam, StudentExamAttempt, StudentExamAnswer, StudentExamAIResult, StudentExamTeacherResult
from Models.Questions import Question, McqsQuestionsOptions
from Models.users import User
from config.config import settings


def _validate_exam_attempt(db: Session, exam_id: uuid.UUID, student_id: uuid.UUID) -> Tuple[bool, str, Optional[StudentExamAttempt]]:
    attempt = db.query(StudentExamAttempt).filter(
        StudentExamAttempt.exam_id == exam_id,
        StudentExamAttempt.student_id == student_id
    ).first()

    if not attempt:
        return False, "Student has not attempted this exam yet", None
    return True, "", attempt


def _check_disqualification_status(attempt: StudentExamAttempt) -> Tuple[bool, Dict[str, Any]]:
    if attempt.status == "disqualified":
        return True, {
            "success": False,
            "message": "Cannot check exam - student was disqualified from this exam",
            "is_disqualified": True,
            "disqualification_reason": "Student was disqualified during the exam session"
        }
    return False, {}


def _check_existing_ai_results(db: Session, attempt_id: uuid.UUID) -> Tuple[bool, str]:
    existing_results = db.query(StudentExamAIResult).filter(
        StudentExamAIResult.attempt_id == attempt_id
    ).first()

    if existing_results:
        return True, "Exam already checked by AI. Use get-results endpoint."
    return False, ""


def _validate_exam_exists(db: Session, exam_id: uuid.UUID) -> Tuple[bool, str, Optional[Exam]]:
    exam = db.query(Exam).filter(Exam.id == exam_id).first()
    if not exam:
        return False, "Exam not found", None
    return True, "", exam


def _validate_student_exists(db: Session, student_id: uuid.UUID) -> Tuple[bool, str, Optional[User]]:
    student = db.query(User).filter(User.id == student_id).first()
    if not student:
        return False, "Student not found", None
    return True, "", student


def _get_student_answers(db: Session, attempt_id: uuid.UUID) -> Tuple[bool, str, List[StudentExamAnswer]]:
    answers = db.query(StudentExamAnswer).filter(StudentExamAnswer.attempt_id == attempt_id).all()
    if not answers:
        return False, "No answers found", []
    return True, "", answers


def _get_correct_mcq_answer(db: Session, question_id: uuid.UUID) -> Optional[str]:
    correct_option = db.query(McqsQuestionsOptions).filter(
        McqsQuestionsOptions.question_id == question_id,
        McqsQuestionsOptions.is_correct == True
    ).first()
    return correct_option.option_text if correct_option else None


def _prepare_ai_data(db: Session, answers: List[StudentExamAnswer]) -> Tuple[List[Dict], int]:
    student_answers = []
    total_max_score = 0

    for answer in answers:
        question = db.query(Question).filter(Question.id == answer.question_id).first()
        if question:
            correct_answer = None
            # Fix: Use question.Type instead of question.question_type
            question_type = question.Type.value if hasattr(question.Type, 'value') else str(question.Type)
            if question_type.lower() == 'mcqs':  # Check for MCQS type
                correct_answer = _get_correct_mcq_answer(db, question.id)

            max_score = question.marks or 1
            total_max_score += max_score

            student_answers.append({
                'question_id': str(question.id),
                'question_text': question.text,  # Fix: Use question.text instead of question.question_text
                'question_type': question_type,
                'student_answer': answer.answer or '',
                'correct_answer': correct_answer,
                'max_score': max_score
            })

    return student_answers, total_max_score


async def check_exam_with_ai(db: Session, exam_id: uuid.UUID, student_id: uuid.UUID) -> Dict[str, Any]:
    try:
        is_valid, error_msg, attempt = _validate_exam_attempt(db, exam_id, student_id)
        if not is_valid:
            return {"success": False, "message": error_msg}

        is_disqualified, disqualification_response = _check_disqualification_status(attempt)
        if is_disqualified:
            return disqualification_response

        has_results, results_msg = _check_existing_ai_results(db, attempt.id)
        if has_results:
            return {"success": False, "message": results_msg}

        exam_exists, exam_error, exam = _validate_exam_exists(db, exam_id)
        if not exam_exists:
            return {"success": False, "message": exam_error}

        student_exists, student_error, student = _validate_student_exists(db, student_id)
        if not student_exists:
            return {"success": False, "message": student_error}

        answers_exist, answers_error, answers = _get_student_answers(db, attempt.id)
        if not answers_exist:
            return {"success": False, "message": answers_error}

        student_answers, total_max_score = _prepare_ai_data(db, answers)

        api_key_valid, api_error = _validate_api_key()
        if not api_key_valid:
            return {"success": False, "message": api_error}

        prompt = _build_ai_prompt(exam, student, student_answers, total_max_score)

        ai_response = await _call_gemini_api(prompt)
        if not ai_response["success"]:
            return ai_response

        save_result = _save_ai_results(db, attempt.id, student_answers, ai_response["data"])
        if not save_result["success"]:
            return save_result

        return {
            "success": True,
            "message": "Exam checked successfully with AI",
            "ai_results": ai_response["data"]
        }

    except Exception as e:
        return {"success": False, "message": f"Error checking exam with AI: {str(e)}"}


def _validate_api_key() -> Tuple[bool, str]:
    api_key = getattr(settings, "GEMINI_API_KEY", None) or os.getenv("GEMINI_API_KEY")
    if not api_key:
        return False, "GEMINI_API_KEY not configured"
    return True, ""


def _build_ai_prompt(exam: Exam, student: User, student_answers: List[Dict], total_max_score: int) -> str:
    prompt = f"""You are an expert exam evaluator. Check this exam and provide structured feedback.

EXAM: {exam.title}
STUDENT: {student.username}
TOTAL QUESTIONS: {len(student_answers)}

QUESTIONS AND ANSWERS:
"""

    for i, qa in enumerate(student_answers, 1):
        prompt += f"""
Question {i}: {qa['question_text']}
Type: {qa['question_type']}
Max Score: {qa['max_score']}
Correct Answer: {qa['correct_answer'] or 'N/A'}
Student Answer: {qa['student_answer']}

"""

    prompt += f"""
Provide response in this EXACT JSON format:
{{
    "total_score": <number>,
    "max_total_score": {total_max_score},
    "percentage": <number>,
    "overall_feedback": "<string>",
    "strengths": ["<string>", ...],
    "improvements": ["<string>", ...],
    "question_feedbacks": [
        {{
            "question_number": 1,
            "ai_score": <number>,
            "max_score": <number>,
            "feedback": "<string>",
            "suggestions": "<string>"
        }}
    ]
}}

SCORING:
- MCQ: Full marks if correct, 0 if wrong
- Descriptive: Partial marks based on quality
- Be fair and constructive

RESPOND ONLY WITH JSON, NO OTHER TEXT.
"""
    return prompt


async def _call_gemini_api(prompt: str) -> Dict[str, Any]:
    try:
        api_key = getattr(settings, "GEMINI_API_KEY", None) or os.getenv("GEMINI_API_KEY")
        url = f"https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent?key={api_key}"

        payload = {
            "contents": [{"parts": [{"text": prompt}]}],
            "generationConfig": {
                "temperature": 0.3,
                "topK": 40,
                "topP": 0.95,
                "maxOutputTokens": 16384,
            }
        }

        async with httpx.AsyncClient(timeout=60.0) as client:
            response = await client.post(url, json=payload)
            response.raise_for_status()

            result = response.json()
            if "candidates" in result and len(result["candidates"]) > 0:
                content = result["candidates"][0]["content"]["parts"][0]["text"]

                start_idx = content.find('{')
                end_idx = content.rfind('}') + 1
                if start_idx != -1 and end_idx != -1:
                    json_str = content[start_idx:end_idx]
                    ai_result = json.loads(json_str)
                    return {"success": True, "data": ai_result}

        return {"success": False, "message": "Invalid AI response format"}

    except Exception as e:
        return {"success": False, "message": f"AI API call failed: {str(e)}"}


def _save_ai_results(db: Session, attempt_id: uuid.UUID, student_answers: List[Dict], ai_result: Dict) -> Dict[str, Any]:
    try:
        for i, feedback in enumerate(ai_result.get('question_feedbacks', [])):
            if i < len(student_answers):
                question_id = uuid.UUID(student_answers[i]['question_id'])

                ai_result_record = db.query(StudentExamAIResult).filter(
                    StudentExamAIResult.attempt_id == attempt_id,
                    StudentExamAIResult.question_id == question_id
                ).first()

                if not ai_result_record:
                    ai_result_record = StudentExamAIResult(
                        attempt_id=attempt_id,
                        question_id=question_id
                    )
                    db.add(ai_result_record)

                ai_result_record.ai_score = feedback.get('ai_score', 0)
                ai_result_record.ai_feedback = feedback.get('feedback', '')

        db.commit()
        return {"success": True}

    except Exception as e:
        db.rollback()
        return {"success": False, "message": f"Error saving AI results: {str(e)}"}

def _build_disqualified_ai_response(exam_id: uuid.UUID, student_id: uuid.UUID, attempt: StudentExamAttempt) -> Dict[str, Any]:
    return {
        "success": True,
        "message": "Student was disqualified from this exam",
        "is_disqualified": True,
        "disqualification_reason": "Student was disqualified during the exam session",
        "result": {
            "exam_id": str(exam_id),
            "student_id": str(student_id),
            "attempt_id": str(attempt.id),
            "is_disqualified": True,
            "disqualification_reason": "Student was disqualified during the exam session",
            "attempt_status": "disqualified",
            "total_score": 0,
            "max_total_score": 0,
            "percentage": 0.0,
            "question_feedbacks": [],
            "checked_at": attempt.updated_at.isoformat() if attempt.updated_at else None
        }
    }


def _get_ai_results_from_db(db: Session, attempt_id: uuid.UUID) -> List[StudentExamAIResult]:
    return db.query(StudentExamAIResult).filter(
        StudentExamAIResult.attempt_id == attempt_id
    ).all()


def _build_ai_results_response(db: Session, exam_id: uuid.UUID, student_id: uuid.UUID, attempt: StudentExamAttempt,
                              exam: Exam, student: User, ai_results: List[StudentExamAIResult]) -> Dict[str, Any]:
    if not ai_results:
        return {"success": False, "message": "No AI results found. Please run AI check first."}

    total_score = sum(result.ai_score or 0 for result in ai_results)
    max_total_score = sum(
        db.query(Question).filter(Question.id == result.question_id).first().marks or 0
        for result in ai_results
    )
    percentage = (total_score / max_total_score * 100) if max_total_score > 0 else 0

    question_feedbacks = []
    for i, result in enumerate(ai_results, 1):
        question = db.query(Question).filter(Question.id == result.question_id).first()

        # Get student's attempted answer for this question
        student_answer_record = db.query(StudentExamAnswer).filter(
            StudentExamAnswer.attempt_id == attempt.id,
            StudentExamAnswer.question_id == result.question_id
        ).first()

        student_attempted_answer = student_answer_record.answer if student_answer_record else ""

        question_feedbacks.append({
            "question_number": i,
            "question_id": str(result.question_id),
            "question_text": question.text if question else "",  # Fix: Use question.text instead of question.question_text
            "student_attempted_answer": student_attempted_answer,  # ✅ ADDED: Student's actual answer
            "ai_score": result.ai_score or 0,
            "max_score": question.marks if question else 0,
            "feedback": result.ai_feedback or "",
            "suggestions": ""
        })

    return {
        "success": True,
        "is_disqualified": False,
        "result": {
            "exam_id": str(exam_id),
            "student_id": str(student_id),
            "attempt_id": str(attempt.id),
            "exam_title": exam.title,
            "student_name": student.username,
            "is_disqualified": False,
            "attempt_status": attempt.status or "submitted",
            "total_score": total_score,
            "max_total_score": max_total_score,
            "percentage": percentage,
            "question_feedbacks": question_feedbacks,
            "checked_at": attempt.updated_at.isoformat() if attempt.updated_at else None
        }
    }


def get_ai_results(db: Session, exam_id: uuid.UUID, student_id: uuid.UUID) -> Dict[str, Any]:
    try:
        is_valid, error_msg, attempt = _validate_exam_attempt(db, exam_id, student_id)
        if not is_valid:
            return {"success": False, "message": error_msg}

        if _check_student_disqualification_status(attempt):
            return _build_disqualified_ai_response(exam_id, student_id, attempt)

        exam_exists, exam_error, exam = _validate_exam_exists(db, exam_id)
        if not exam_exists:
            return {"success": False, "message": exam_error}

        student_exists, student_error, student = _validate_student_exists(db, student_id)
        if not student_exists:
            return {"success": False, "message": student_error}

        ai_results = _get_ai_results_from_db(db, attempt.id)
        return _build_ai_results_response(db, exam_id, student_id, attempt, exam, student, ai_results)

    except Exception as e:
        return {"success": False, "message": f"Error: {str(e)}"}


def _fetch_student_exam_attempt(db: Session, exam_id: uuid.UUID, student_id: uuid.UUID) -> tuple:
    try:
        attempt_query = db.query(
            StudentExamAttempt,
            Exam.title.label('exam_title'),
            User.username.label('student_name')
        ).join(
            Exam, StudentExamAttempt.exam_id == Exam.id
        ).join(
            User, StudentExamAttempt.student_id == User.id
        ).filter(
            StudentExamAttempt.exam_id == exam_id,
            StudentExamAttempt.student_id == student_id
        ).first()
        return attempt_query
    except Exception as e:
        raise Exception(f"Database error fetching exam attempt: {str(e)}")


def _validate_exam_attempt_exists(attempt_query) -> bool:
    return attempt_query is not None


def _check_student_disqualification_status(attempt) -> bool:
    return attempt.status == "disqualified"


def _build_disqualified_student_response(exam_id: uuid.UUID, student_id: uuid.UUID,
                                       attempt, exam_title: str, student_name: str) -> Dict[str, Any]:
    return {
        "success": True,
        "message": "Student was disqualified from this exam and has failed",
        "is_disqualified": True,
        "disqualification_reason": "Student was disqualified during the exam session",
        "result": {
            "exam_id": str(exam_id),
            "student_id": str(student_id),
            "attempt_id": str(attempt.id),
            "exam_title": exam_title,
            "student_name": student_name,
            "is_disqualified": True,
            "disqualification_reason": "Student was disqualified during the exam session",
            "attempt_status": "disqualified",
            "total_score": 0,
            "max_total_score": 0,
            "percentage": 0.0,
            "has_teacher_results": False,
            "has_ai_results": False,
            "results_source": "none",
            "submitted_at": attempt.completed_at.isoformat() if attempt.completed_at else None
        }
    }


def _check_results_availability(db: Session, attempt_id: uuid.UUID) -> tuple:
    try:
        teacher_results_count = db.query(StudentExamTeacherResult).filter(
            StudentExamTeacherResult.attempt_id == attempt_id
        ).count()

        ai_results_count = db.query(StudentExamAIResult).filter(
            StudentExamAIResult.attempt_id == attempt_id
        ).count()

        return (teacher_results_count > 0, ai_results_count > 0)
    except Exception as e:
        raise Exception(f"Database error checking results availability: {str(e)}")


def _calculate_teacher_scores(db: Session, attempt_id: uuid.UUID) -> tuple:
    try:
        teacher_scores = db.query(
            StudentExamTeacherResult.teacher_score,
            Question.marks
        ).join(
            Question, StudentExamTeacherResult.question_id == Question.id
        ).filter(
            StudentExamTeacherResult.attempt_id == attempt_id
        ).all()

        total_score = sum(teacher_score or 0 for teacher_score, _ in teacher_scores)
        max_total_score = sum(max_marks or 0 for _, max_marks in teacher_scores)
        return (total_score, max_total_score)
    except Exception as e:
        raise Exception(f"Database error calculating teacher scores: {str(e)}")


def _calculate_ai_scores(db: Session, attempt_id: uuid.UUID) -> tuple:
    try:
        ai_scores = db.query(
            StudentExamAIResult.ai_score,
            Question.marks
        ).join(
            Question, StudentExamAIResult.question_id == Question.id
        ).filter(
            StudentExamAIResult.attempt_id == attempt_id
        ).all()

        total_score = sum(ai_score or 0 for ai_score, _ in ai_scores)
        max_total_score = sum(max_marks or 0 for _, max_marks in ai_scores)
        return (total_score, max_total_score)
    except Exception as e:
        raise Exception(f"Database error calculating AI scores: {str(e)}")


def _determine_results_source_and_scores(db: Session, attempt_id: uuid.UUID,
                                        has_teacher_results: bool, has_ai_results: bool) -> tuple:
    try:
        if has_teacher_results:
            total_score, max_total_score = _calculate_teacher_scores(db, attempt_id)
            return (total_score, max_total_score, "teacher")
        elif has_ai_results:
            total_score, max_total_score = _calculate_ai_scores(db, attempt_id)
            return (total_score, max_total_score, "ai")
        else:
            return (0, 0, "none")
    except Exception as e:
        raise Exception(f"Error determining results source and scores: {str(e)}")


def _calculate_percentage_score(total_score: float, max_total_score: float) -> float:
    if max_total_score <= 0:
        return 0.0
    return (total_score / max_total_score) * 100


def _format_submitted_timestamp(completed_at) -> str:
    if completed_at:
        return completed_at.isoformat()
    return None


def _build_successful_exam_result(exam_id: uuid.UUID, student_id: uuid.UUID, attempt,
                                exam_title: str, student_name: str, total_score: float,
                                max_total_score: float, percentage: float,
                                has_teacher_results: bool, has_ai_results: bool,
                                results_source: str) -> Dict[str, Any]:
    return {
        "success": True,
        "is_disqualified": False,
        "result": {
            "exam_id": str(exam_id),
            "student_id": str(student_id),
            "attempt_id": str(attempt.id),
            "exam_title": exam_title,
            "student_name": student_name,
            "is_disqualified": False,
            "attempt_status": attempt.status or "submitted",
            "total_score": total_score,
            "max_total_score": max_total_score,
            "percentage": percentage,
            "has_teacher_results": has_teacher_results,
            "has_ai_results": has_ai_results,
            "results_source": results_source,
            "submitted_at": _format_submitted_timestamp(attempt.completed_at)
        }
    }


def get_basic_exam_results(db: Session, exam_id: uuid.UUID, student_id: uuid.UUID) -> Dict[str, Any]:
    try:
        attempt_query = _fetch_student_exam_attempt(db, exam_id, student_id)

        if not _validate_exam_attempt_exists(attempt_query):
            return {"success": False, "message": "Student has not attempted this exam"}

        attempt, exam_title, student_name = attempt_query

        if _check_student_disqualification_status(attempt):
            return _build_disqualified_student_response(
                exam_id, student_id, attempt, exam_title, student_name
            )

        has_teacher_results, has_ai_results = _check_results_availability(db, attempt.id)

        total_score, max_total_score, results_source = _determine_results_source_and_scores(
            db, attempt.id, has_teacher_results, has_ai_results
        )

        percentage = _calculate_percentage_score(total_score, max_total_score)

        return _build_successful_exam_result(
            exam_id, student_id, attempt, exam_title, student_name,
            total_score, max_total_score, percentage,
            has_teacher_results, has_ai_results, results_source
        )
    except Exception as e:
        return {
            "success": False,
            "message": f"Error getting basic exam results: {str(e)}",
            "error_type": "system_error"
        }
