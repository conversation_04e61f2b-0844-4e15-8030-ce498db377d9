"""
User Follow Schemas for EduFair Platform

This module contains Pydantic schemas for user following functionality.
"""

from pydantic import BaseModel, Field
from uuid import UUID
from datetime import datetime
from typing import Optional, List


# ==================== BASE SCHEMAS ====================

class UserFollowBase(BaseModel):
    """Base schema for user follow"""
    pass


class UserFollowCreate(UserFollowBase):
    """Schema for creating a follow relationship"""
    following_id: UUID = Field(..., description="ID of user to follow")


class UserFollowResponse(BaseModel):
    """Schema for follow relationship response"""
    id: UUID
    follower_id: UUID
    following_id: UUID
    followed_at: datetime
    
    class Config:
        from_attributes = True


# ==================== USER SCHEMAS WITH FOLLOW INFO ====================

class UserBasicInfo(BaseModel):
    """Basic user information for follow lists"""
    id: UUID
    username: str
    email: str
    user_type: str
    profile_picture: Optional[str] = None
    country: Optional[str] = None
    
    class Config:
        from_attributes = True


class UserWithFollowStatus(UserBasicInfo):
    """User info with follow status"""
    is_following: bool = False
    is_followed_by: bool = False
    followers_count: int = 0
    following_count: int = 0


class FollowListResponse(BaseModel):
    """Response for followers/following lists"""
    users: List[UserBasicInfo]
    total_count: int
    page: int
    page_size: int
    has_next: bool
    has_previous: bool


# ==================== FOLLOW STATS ====================

class UserFollowStats(BaseModel):
    """User follow statistics"""
    user_id: UUID
    followers_count: int = 0
    following_count: int = 0
    mutual_follows_count: int = 0


class FollowSuggestion(BaseModel):
    """Follow suggestion schema"""
    user: UserBasicInfo
    reason: str  # "mutual_followers", "same_interests", etc.
    mutual_followers_count: int = 0


class FollowSuggestionsResponse(BaseModel):
    """Response for follow suggestions"""
    suggestions: List[FollowSuggestion]
    total_count: int


# ==================== BULK OPERATIONS ====================

class BulkFollowRequest(BaseModel):
    """Schema for bulk follow operations"""
    user_ids: List[UUID] = Field(..., max_items=50, description="List of user IDs to follow")


class BulkFollowResponse(BaseModel):
    """Response for bulk follow operations"""
    successful_follows: List[UUID]
    failed_follows: List[dict]  # [{"user_id": UUID, "error": str}]
    total_processed: int
    success_count: int
    failure_count: int


# ==================== FOLLOW ACTIVITY ====================

class FollowActivity(BaseModel):
    """Follow activity schema"""
    id: UUID
    follower: UserBasicInfo
    following: UserBasicInfo
    followed_at: datetime
    activity_type: str = "follow"


class FollowActivityResponse(BaseModel):
    """Response for follow activity feed"""
    activities: List[FollowActivity]
    total_count: int
    page: int
    page_size: int
    has_next: bool
    has_previous: bool
