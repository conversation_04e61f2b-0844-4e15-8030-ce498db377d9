"""
Health Check and Monitoring Endpoints for EduFair
Provides comprehensive system health monitoring
"""

import asyncio
import time
import psutil
import uuid
from datetime import datetime, timezone
from typing import Dict, Any, Optional

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from sqlalchemy import text

from config.session import get_db
# from config.mongodb import get_mongodb  # Temporarily commented out
from config.logging import get_logger, log_security_event

router = APIRouter()
logger = get_logger(__name__)


class HealthStatus:
    """Health status constants"""
    HEALTHY = "healthy"
    UNHEALTHY = "unhealthy"
    DEGRADED = "degraded"
    UNKNOWN = "unknown"


async def check_database_health(db: Session) -> Dict[str, Any]:
    """Check PostgreSQL database health"""
    try:
        start_time = time.time()
        
        # Test basic connectivity
        result = db.execute(text("SELECT 1 as health_check"))
        result.fetchone()
        
        # Test a more complex query to check performance
        result = db.execute(text("SELECT COUNT(*) FROM users"))
        user_count = result.scalar()
        
        response_time = (time.time() - start_time) * 1000  # Convert to milliseconds
        
        # Determine health status based on response time
        if response_time < 100:
            status = HealthStatus.HEALTHY
        elif response_time < 500:
            status = HealthStatus.DEGRADED
        else:
            status = HealthStatus.UNHEALTHY
        
        return {
            "status": status,
            "response_time_ms": round(response_time, 2),
            "user_count": user_count,
            "details": "Database connection successful"
        }
        
    except Exception as e:
        logger.error(f"Database health check failed: {str(e)}")
        return {
            "status": HealthStatus.UNHEALTHY,
            "error": str(e),
            "details": "Database connection failed"
        }


async def check_cache_health() -> Dict[str, Any]:
    """Check in-memory cache health"""
    try:
        start_time = time.time()

        # Test cache operations
        from utils.cache import CacheManager
        cache_manager = CacheManager()

        test_key = f"health_check_{uuid.uuid4()}"
        await cache_manager.set(test_key, "test_value", 10)
        value = await cache_manager.get(test_key)
        await cache_manager.delete(test_key)

        response_time = (time.time() - start_time) * 1000

        # Get cache stats
        with cache_manager.lock:
            total_entries = len(cache_manager.storage)

        # Determine health status
        if response_time < 10:
            status = HealthStatus.HEALTHY
        elif response_time < 50:
            status = HealthStatus.DEGRADED
        else:
            status = HealthStatus.UNHEALTHY

        return {
            "status": status,
            "response_time_ms": round(response_time, 2),
            "total_entries": total_entries,
            "cache_type": "in-memory",
            "test_operation": "success" if value == "test_value" else "failed",
            "details": "Cache operations successful"
        }

    except Exception as e:
        logger.error(f"Cache health check failed: {str(e)}")
        return {
            "status": HealthStatus.UNHEALTHY,
            "error": str(e),
            "details": "Cache operations failed"
        }


async def check_mongodb_health() -> Dict[str, Any]:
    """Check MongoDB health"""
    try:
        # Temporarily return healthy status without actual check
        return {
            "status": HealthStatus.HEALTHY,
            "response_time_ms": 0.0,
            "collections_count": 0,
            "details": "MongoDB check temporarily disabled"
        }

    except Exception as e:
        logger.error(f"MongoDB health check failed: {str(e)}")
        return {
            "status": HealthStatus.UNHEALTHY,
            "error": str(e),
            "details": "MongoDB connection failed"
        }


def get_system_metrics() -> Dict[str, Any]:
    """Get system performance metrics"""
    try:
        # CPU usage
        cpu_percent = psutil.cpu_percent(interval=1)
        
        # Memory usage
        memory = psutil.virtual_memory()
        
        # Disk usage
        disk = psutil.disk_usage('/')
        
        # Network stats (if available)
        try:
            network = psutil.net_io_counters()
            network_stats = {
                "bytes_sent": network.bytes_sent,
                "bytes_recv": network.bytes_recv,
                "packets_sent": network.packets_sent,
                "packets_recv": network.packets_recv
            }
        except:
            network_stats = {"error": "Network stats unavailable"}
        
        return {
            "cpu": {
                "usage_percent": cpu_percent,
                "count": psutil.cpu_count()
            },
            "memory": {
                "total_gb": round(memory.total / (1024**3), 2),
                "available_gb": round(memory.available / (1024**3), 2),
                "used_percent": memory.percent
            },
            "disk": {
                "total_gb": round(disk.total / (1024**3), 2),
                "free_gb": round(disk.free / (1024**3), 2),
                "used_percent": round((disk.used / disk.total) * 100, 2)
            },
            "network": network_stats
        }
        
    except Exception as e:
        logger.error(f"System metrics collection failed: {str(e)}")
        return {"error": str(e)}


@router.get("/health")
async def basic_health_check():
    """
    Basic health check endpoint
    Returns simple status for load balancers
    """
    return {
        "status": "healthy",
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "service": "EduFair API"
    }


@router.get("/health/detailed")
async def detailed_health_check(db: Session = Depends(get_db)):
    """
    Detailed health check with all service dependencies
    """
    check_id = str(uuid.uuid4())
    start_time = time.time()
    
    logger.info(f"Starting detailed health check", extra={"check_id": check_id})
    
    # Run all health checks concurrently
    database_task = asyncio.create_task(check_database_health(db))
    cache_task = asyncio.create_task(check_cache_health())
    mongodb_task = asyncio.create_task(check_mongodb_health())

    # Wait for all checks to complete
    database_health = await database_task
    cache_health = await cache_task
    mongodb_health = await mongodb_task
    
    # Get system metrics
    system_metrics = get_system_metrics()
    
    total_time = (time.time() - start_time) * 1000
    
    # Determine overall health status
    service_statuses = [
        database_health["status"],
        cache_health["status"],
        mongodb_health["status"]
    ]
    
    if all(status == HealthStatus.HEALTHY for status in service_statuses):
        overall_status = HealthStatus.HEALTHY
    elif any(status == HealthStatus.UNHEALTHY for status in service_statuses):
        overall_status = HealthStatus.UNHEALTHY
    else:
        overall_status = HealthStatus.DEGRADED
    
    health_report = {
        "status": overall_status,
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "check_id": check_id,
        "total_check_time_ms": round(total_time, 2),
        "services": {
            "database": database_health,
            "cache": cache_health,
            "mongodb": mongodb_health
        },
        "system": system_metrics,
        "version": "1.0.0",  # You can make this dynamic
        "environment": "production"  # You can make this configurable
    }
    
    # Log health check result
    logger.info(
        f"Health check completed: {overall_status}",
        extra={
            "check_id": check_id,
            "overall_status": overall_status,
            "total_time_ms": round(total_time, 2),
            "health_check": True
        }
    )
    
    # Log security event if system is unhealthy
    if overall_status == HealthStatus.UNHEALTHY:
        log_security_event(
            event_type="SYSTEM_HEALTH_CRITICAL",
            message="System health check failed - critical services unavailable",
            additional_data={
                "check_id": check_id,
                "failed_services": [
                    service for service, health in health_report["services"].items()
                    if health["status"] == HealthStatus.UNHEALTHY
                ]
            }
        )
    
    return health_report


@router.get("/health/readiness")
async def readiness_check(db: Session = Depends(get_db)):
    """
    Kubernetes readiness probe endpoint
    Checks if the application is ready to serve traffic
    """
    try:
        # Quick database check
        db.execute(text("SELECT 1"))

        # Quick cache check
        from utils.cache import CacheManager
        cache_manager = CacheManager()
        test_key = f"readiness_check_{uuid.uuid4()}"
        await cache_manager.set(test_key, "test", 5)
        await cache_manager.delete(test_key)

        return {
            "status": "ready",
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
        
    except Exception as e:
        logger.error(f"Readiness check failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Service not ready"
        )


@router.get("/health/liveness")
async def liveness_check():
    """
    Kubernetes liveness probe endpoint
    Checks if the application is alive and should not be restarted
    """
    return {
        "status": "alive",
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "uptime_seconds": time.time() - psutil.Process().create_time()
    }


@router.get("/metrics")
async def get_metrics():
    """
    Prometheus-style metrics endpoint
    Returns application metrics in a format suitable for monitoring
    """
    try:
        system_metrics = get_system_metrics()
        
        # Format metrics for Prometheus (simplified)
        metrics = []
        
        if "cpu" in system_metrics:
            metrics.append(f"edufair_cpu_usage_percent {system_metrics['cpu']['usage_percent']}")
            metrics.append(f"edufair_cpu_count {system_metrics['cpu']['count']}")
        
        if "memory" in system_metrics:
            metrics.append(f"edufair_memory_usage_percent {system_metrics['memory']['used_percent']}")
            metrics.append(f"edufair_memory_total_bytes {system_metrics['memory']['total_gb'] * 1024**3}")
        
        if "disk" in system_metrics:
            metrics.append(f"edufair_disk_usage_percent {system_metrics['disk']['used_percent']}")
            metrics.append(f"edufair_disk_total_bytes {system_metrics['disk']['total_gb'] * 1024**3}")
        
        # Add application-specific metrics
        metrics.append(f"edufair_health_check_timestamp {time.time()}")
        
        return "\n".join(metrics)
        
    except Exception as e:
        logger.error(f"Metrics collection failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Metrics collection failed"
        )
