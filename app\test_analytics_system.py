"""
Test script for the Student Analytics System

This script tests all the analytics endpoints to ensure they work correctly.
"""

import asyncio
import httpx
from datetime import datetime, timedelta, timezone
import json


BASE_URL = "http://localhost:8000"
TEST_STUDENT_TOKEN = None  # Will be set after login


async def login_as_student():
    """Login as a test student and get authentication token"""
    global TEST_STUDENT_TOKEN
    
    async with httpx.AsyncClient() as client:
        # Try to login with test credentials
        login_data = {
            "username": "<EMAIL>",
            "password": "testpassword123"
        }
        
        response = await client.post(f"{BASE_URL}/api/users/login", data=login_data)
        
        if response.status_code == 200:
            data = response.json()
            TEST_STUDENT_TOKEN = data.get("access_token")
            print("✅ Successfully logged in as test student")
            return True
        else:
            print(f"❌ Failed to login: {response.status_code} - {response.text}")
            return False


async def test_subject_analytics():
    """Test subject-wise analytics endpoint"""
    print("\n🔍 Testing Subject Analytics...")
    
    if not TEST_STUDENT_TOKEN:
        print("❌ No authentication token available")
        return False
    
    headers = {"Authorization": f"Bearer {TEST_STUDENT_TOKEN}"}
    
    # Calculate date range (last 3 months)
    end_date = datetime.now(timezone.utc)
    start_date = end_date - timedelta(days=90)
    
    params = {
        "start_date": start_date.isoformat(),
        "end_date": end_date.isoformat(),
        "period_type": "monthly",
        "include_trends": True,
        "include_comparisons": True,
        "include_recommendations": True,
        "include_chapter_breakdown": True
    }
    
    async with httpx.AsyncClient() as client:
        response = await client.get(
            f"{BASE_URL}/api/student/analytics/subject",
            headers=headers,
            params=params,
            timeout=30.0
        )
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Subject analytics endpoint working")
            print(f"   - Found {len(data.get('subjects', []))} subjects")
            print(f"   - Overall GPA: {data.get('overall_gpa', 0):.2f}")
            print(f"   - Strongest subject: {data.get('strongest_subject', 'N/A')}")
            return True
        else:
            print(f"❌ Subject analytics failed: {response.status_code}")
            print(f"   Response: {response.text}")
            return False


async def test_class_grade_analytics():
    """Test class/grade-wise analytics endpoint"""
    print("\n🔍 Testing Class/Grade Analytics...")
    
    if not TEST_STUDENT_TOKEN:
        print("❌ No authentication token available")
        return False
    
    headers = {"Authorization": f"Bearer {TEST_STUDENT_TOKEN}"}
    
    # Calculate date range (last 3 months)
    end_date = datetime.now(timezone.utc)
    start_date = end_date - timedelta(days=90)
    
    params = {
        "start_date": start_date.isoformat(),
        "end_date": end_date.isoformat(),
        "period_type": "monthly",
        "include_trends": True,
        "include_comparisons": True,
        "include_recommendations": True,
        "include_peer_comparison": True,
        "include_rank_history": True
    }
    
    async with httpx.AsyncClient() as client:
        response = await client.get(
            f"{BASE_URL}/api/student/analytics/class-grade",
            headers=headers,
            params=params,
            timeout=30.0
        )
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Class/Grade analytics endpoint working")
            print(f"   - Found {len(data.get('class_performance', []))} classes")
            print(f"   - Grade level: {data.get('grade_analytics', {}).get('grade_level', 'N/A')}")
            print(f"   - Academic standing: {data.get('overall_academic_standing', 'N/A')}")
            return True
        else:
            print(f"❌ Class/Grade analytics failed: {response.status_code}")
            print(f"   Response: {response.text}")
            return False


async def test_classroom_analytics():
    """Test classroom-wise analytics endpoint"""
    print("\n🔍 Testing Classroom Analytics...")
    
    if not TEST_STUDENT_TOKEN:
        print("❌ No authentication token available")
        return False
    
    headers = {"Authorization": f"Bearer {TEST_STUDENT_TOKEN}"}
    
    # Calculate date range (last 3 months)
    end_date = datetime.now(timezone.utc)
    start_date = end_date - timedelta(days=90)
    
    params = {
        "start_date": start_date.isoformat(),
        "end_date": end_date.isoformat(),
        "period_type": "monthly",
        "include_trends": True,
        "include_comparisons": True,
        "include_recommendations": True,
        "include_engagement_details": True
    }
    
    async with httpx.AsyncClient() as client:
        response = await client.get(
            f"{BASE_URL}/api/student/analytics/classroom",
            headers=headers,
            params=params,
            timeout=30.0
        )
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Classroom analytics endpoint working")
            print(f"   - Found {len(data.get('classrooms', []))} classrooms")
            print(f"   - Overall engagement score: {data.get('overall_engagement_score', 0):.2f}")
            print(f"   - Most engaged classroom: {data.get('most_engaged_classroom', 'N/A')}")
            return True
        else:
            print(f"❌ Classroom analytics failed: {response.status_code}")
            print(f"   Response: {response.text}")
            return False


async def test_competition_analytics():
    """Test competition-wise analytics endpoint"""
    print("\n🔍 Testing Competition Analytics...")
    
    if not TEST_STUDENT_TOKEN:
        print("❌ No authentication token available")
        return False
    
    headers = {"Authorization": f"Bearer {TEST_STUDENT_TOKEN}"}
    
    # Calculate date range (last 6 months for competitions)
    end_date = datetime.now(timezone.utc)
    start_date = end_date - timedelta(days=180)
    
    params = {
        "start_date": start_date.isoformat(),
        "end_date": end_date.isoformat(),
        "period_type": "monthly",
        "include_trends": True,
        "include_comparisons": True,
        "include_recommendations": True,
        "include_upcoming": True
    }
    
    async with httpx.AsyncClient() as client:
        response = await client.get(
            f"{BASE_URL}/api/student/analytics/competition",
            headers=headers,
            params=params,
            timeout=30.0
        )
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Competition analytics endpoint working")
            print(f"   - Found {len(data.get('competitions', []))} competitions")
            print(f"   - Total participated: {data.get('summary', {}).get('total_competitions_participated', 0)}")
            print(f"   - Average score: {data.get('summary', {}).get('average_score', 0):.2f}")
            return True
        else:
            print(f"❌ Competition analytics failed: {response.status_code}")
            print(f"   Response: {response.text}")
            return False


async def test_comprehensive_analytics():
    """Test comprehensive analytics endpoint"""
    print("\n🔍 Testing Comprehensive Analytics...")
    
    if not TEST_STUDENT_TOKEN:
        print("❌ No authentication token available")
        return False
    
    headers = {"Authorization": f"Bearer {TEST_STUDENT_TOKEN}"}
    
    # Calculate date range (last 3 months)
    end_date = datetime.now(timezone.utc)
    start_date = end_date - timedelta(days=90)
    
    params = {
        "start_date": start_date.isoformat(),
        "end_date": end_date.isoformat(),
        "period_type": "monthly",
        "include_trends": True,
        "include_comparisons": True,
        "include_recommendations": True
    }
    
    async with httpx.AsyncClient() as client:
        response = await client.get(
            f"{BASE_URL}/api/student/analytics/comprehensive",
            headers=headers,
            params=params,
            timeout=60.0  # Longer timeout for comprehensive analytics
        )
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Comprehensive analytics endpoint working")
            print(f"   - Student: {data.get('student_name', 'N/A')}")
            print(f"   - Overall performance score: {data.get('overall_performance_score', 0):.2f}")
            print(f"   - Strength areas: {len(data.get('academic_strength_areas', []))}")
            print(f"   - Recommendations: {len(data.get('personalized_recommendations', []))}")
            return True
        else:
            print(f"❌ Comprehensive analytics failed: {response.status_code}")
            print(f"   Response: {response.text}")
            return False


async def test_analytics_health():
    """Test analytics health endpoint"""
    print("\n🔍 Testing Analytics Health...")
    
    async with httpx.AsyncClient() as client:
        response = await client.get(f"{BASE_URL}/api/student/analytics/health")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Analytics health endpoint working")
            print(f"   - Status: {data.get('status', 'unknown')}")
            print(f"   - Service: {data.get('service', 'unknown')}")
            return True
        else:
            print(f"❌ Analytics health failed: {response.status_code}")
            return False


async def main():
    """Run all analytics tests"""
    print("🚀 Starting Student Analytics System Tests")
    print("=" * 50)
    
    # Test health endpoint first (no auth required)
    health_ok = await test_analytics_health()
    
    # Try to login
    login_ok = await login_as_student()
    
    if not login_ok:
        print("\n❌ Cannot proceed with tests - login failed")
        print("Make sure you have a test student account or update the credentials")
        return
    
    # Run all analytics tests
    tests = [
        test_subject_analytics,
        test_class_grade_analytics,
        test_classroom_analytics,
        test_competition_analytics,
        test_comprehensive_analytics
    ]
    
    results = []
    for test in tests:
        try:
            result = await test()
            results.append(result)
        except Exception as e:
            print(f"❌ Test failed with exception: {str(e)}")
            results.append(False)
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Results Summary:")
    print(f"   - Health check: {'✅' if health_ok else '❌'}")
    print(f"   - Authentication: {'✅' if login_ok else '❌'}")
    print(f"   - Subject analytics: {'✅' if results[0] else '❌'}")
    print(f"   - Class/Grade analytics: {'✅' if results[1] else '❌'}")
    print(f"   - Classroom analytics: {'✅' if results[2] else '❌'}")
    print(f"   - Competition analytics: {'✅' if results[3] else '❌'}")
    print(f"   - Comprehensive analytics: {'✅' if results[4] else '❌'}")
    
    total_tests = len(results) + 2  # +2 for health and auth
    passed_tests = sum(results) + (1 if health_ok else 0) + (1 if login_ok else 0)
    
    print(f"\n🎯 Overall: {passed_tests}/{total_tests} tests passed")
    
    if passed_tests == total_tests:
        print("🎉 All tests passed! Analytics system is working correctly.")
    else:
        print("⚠️  Some tests failed. Check the output above for details.")


if __name__ == "__main__":
    asyncio.run(main())
