"""
Classroom-wise Analytics CRUD Operations for EduFair Platform

This module contains CRUD operations for classroom-specific student analytics.
"""

import uuid
from datetime import datetime, timedelta, timezone
from typing import List, Dict, Any, Optional
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import func, desc, asc, and_, or_, case
from fastapi import HTTPException, status

# Import Models
from Models.users import User, UserTypeEnum, Subject
from Models.Classroom import Classroom, StudentClassroom
from Models.Tasks import Task, TaskStudents, TaskStatus, TaskClassroom
from Models.Exam import Exam, StudentExamAssignment, StudentExamAttempt, StudentExamAIResult, StudentExamTeacherResult
from Models.Announcements import Announcement

# Import Schemas
from Schemas.StudentAnalytics import (
    ClassroomAnalyticsRequest, ClassroomAnalyticsResponse, ClassroomEngagementMetrics,
    AnalyticsTimeRange
)

# Import caching utilities (temporarily disabled)
# from utils.AnalyticsCache import cache_analytics, get_cache_ttl


# @cache_analytics("classroom_analytics", ttl_minutes=get_cache_ttl("classroom_analytics"))
def get_classroom_analytics(
    db: Session,
    student_id: uuid.UUID,
    request: ClassroomAnalyticsRequest
) -> ClassroomAnalyticsResponse:
    """
    Get comprehensive classroom-wise analytics for a student
    """
    # Verify student exists
    student = db.query(User).filter(
        User.id == student_id,
        User.user_type == UserTypeEnum.student
    ).first()
    
    if not student:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Student not found"
        )
    
    # Get classrooms to analyze
    if request.classroom_ids:
        classrooms = db.query(Classroom).filter(
            Classroom.id.in_(request.classroom_ids)
        ).all()
    else:
        # Get all classrooms the student is enrolled in
        classrooms = _get_student_classrooms(db, student_id, request.time_range)
    
    # Analyze each classroom
    classroom_metrics = []
    engagement_scores = []
    
    for classroom in classrooms:
        metrics = _analyze_classroom_engagement(
            db, student_id, classroom, request.time_range, request.include_engagement_details
        )
        classroom_metrics.append(metrics)
        engagement_scores.append(metrics.overall_engagement_score)
    
    # Calculate overall metrics
    overall_engagement_score = sum(engagement_scores) / len(engagement_scores) if engagement_scores else 0.0
    
    # Determine most and least engaged classrooms
    most_engaged = max(classroom_metrics, key=lambda x: x.overall_engagement_score).classroom_name if classroom_metrics else None
    least_engaged = min(classroom_metrics, key=lambda x: x.overall_engagement_score).classroom_name if classroom_metrics else None
    
    # Determine engagement trend
    engagement_trend = _calculate_engagement_trend(db, student_id, request.time_range)
    
    # Generate recommendations
    recommendations = _generate_classroom_recommendations(classroom_metrics, overall_engagement_score)
    
    return ClassroomAnalyticsResponse(
        student_id=student_id,
        time_range=request.time_range,
        classrooms=classroom_metrics,
        most_engaged_classroom=most_engaged,
        least_engaged_classroom=least_engaged,
        overall_engagement_score=overall_engagement_score,
        engagement_trend=engagement_trend,
        recommendations=recommendations,
        last_updated=datetime.now(timezone.utc)
    )


def _get_student_classrooms(
    db: Session, 
    student_id: uuid.UUID, 
    time_range: AnalyticsTimeRange
) -> List[Classroom]:
    """Get all classrooms the student is enrolled in during the time range"""
    
    classrooms = db.query(Classroom).join(
        StudentClassroom, Classroom.id == StudentClassroom.classroom_id
    ).filter(
        StudentClassroom.student_id == student_id,
        # Note: StudentClassroom doesn't have joined_at field, using created_at instead
        or_(
            StudentClassroom.created_at >= time_range.start_date,
            StudentClassroom.created_at <= time_range.end_date
        )
    ).all()
    
    return classrooms


def _analyze_classroom_engagement(
    db: Session,
    student_id: uuid.UUID,
    classroom: Classroom,
    time_range: AnalyticsTimeRange,
    include_engagement_details: bool
) -> ClassroomEngagementMetrics:
    """Analyze student's engagement and performance in a specific classroom"""
    
    # Get teacher information
    teacher = db.query(User).filter(User.id == classroom.teacher_id).first()
    teacher_name = teacher.username if teacher else "Unknown Teacher"
    
    # Get classroom subject (simplified - assuming one subject per classroom)
    subject = _get_classroom_subject(db, classroom.id)
    
    # Calculate attendance rate
    attendance_rate = _calculate_attendance_rate(db, student_id, classroom.id, time_range)
    
    # Calculate participation score
    participation_score = _calculate_participation_score(db, student_id, classroom.id, time_range)
    
    # Calculate assignment metrics
    assignment_metrics = _calculate_assignment_metrics(db, student_id, classroom.id, time_range)
    
    # Calculate performance metrics
    performance_metrics = _calculate_classroom_performance_metrics(db, student_id, classroom.id, time_range)
    
    # Calculate engagement indicators
    engagement_indicators = {}
    if include_engagement_details:
        engagement_indicators = _calculate_engagement_indicators(db, student_id, classroom.id, time_range)
    
    # Calculate time-based metrics
    time_metrics = _calculate_time_metrics(db, student_id, classroom.id, time_range)
    
    # Get teacher feedback
    teacher_feedback = _get_teacher_feedback(db, student_id, classroom.id, time_range)
    
    # Calculate overall engagement score
    overall_engagement_score = _calculate_overall_engagement_score(
        attendance_rate, participation_score, assignment_metrics, performance_metrics
    )
    
    return ClassroomEngagementMetrics(
        classroom_id=classroom.id,
        classroom_name=classroom.name,
        teacher_name=teacher_name,
        subject=subject,
        attendance_rate=attendance_rate,
        participation_score=participation_score,
        assignment_submission_rate=assignment_metrics['submission_rate'],
        on_time_submission_rate=assignment_metrics['on_time_rate'],
        classroom_average=performance_metrics['classroom_average'],
        student_average=performance_metrics['student_average'],
        performance_vs_classroom=performance_metrics['performance_vs_classroom'],
        classroom_rank=performance_metrics['classroom_rank'],
        total_classroom_students=performance_metrics['total_students'],
        questions_asked=engagement_indicators.get('questions_asked', 0),
        discussions_participated=engagement_indicators.get('discussions_participated', 0),
        peer_interactions=engagement_indicators.get('peer_interactions', 0),
        help_requests=engagement_indicators.get('help_requests', 0),
        assignments_completed=assignment_metrics['completed'],
        assignments_total=assignment_metrics['total'],
        average_assignment_score=assignment_metrics['average_score'],
        best_assignment_score=assignment_metrics['best_score'],
        time_spent_in_classroom=time_metrics['time_spent'],
        active_learning_time=time_metrics['active_time'],
        positive_feedback_count=teacher_feedback['positive_count'],
        improvement_suggestions=teacher_feedback['suggestions'],
        overall_engagement_score=overall_engagement_score
    )


def _get_classroom_subject(db: Session, classroom_id: uuid.UUID) -> str:
    """Get the primary subject for a classroom"""
    
    # Get the most common subject from tasks in this classroom
    subject_query = db.query(Subject.name, func.count(Task.id).label('count')).join(
        Task, Subject.id == Task.subject_id
    ).join(
        TaskClassroom, Task.id == TaskClassroom.task_id
    ).filter(
        TaskClassroom.classroom_id == classroom_id
    ).group_by(Subject.name).order_by(desc('count')).first()
    
    return subject_query[0] if subject_query else "General"


def _calculate_attendance_rate(
    db: Session,
    student_id: uuid.UUID,
    classroom_id: uuid.UUID,
    time_range: AnalyticsTimeRange
) -> float:
    """Calculate student's attendance rate in the classroom"""
    
    # This would involve tracking actual attendance data
    # For now, return a calculated estimate based on activity
    
    # Count days with activity (assignments, exams, etc.)
    active_days = db.query(func.count(func.distinct(func.date(TaskStudents.submission_date)))).join(
        Task, TaskStudents.task_id == Task.id
    ).join(
        TaskClassroom, Task.id == TaskClassroom.task_id
    ).filter(
        TaskStudents.student_id == student_id,
        TaskClassroom.classroom_id == classroom_id,
        TaskStudents.submission_date >= time_range.start_date,
        TaskStudents.submission_date <= time_range.end_date
    ).scalar() or 0
    
    # Estimate total classroom days (simplified)
    total_days = (time_range.end_date - time_range.start_date).days
    classroom_days = max(1, total_days // 7 * 5)  # Assuming 5 days per week
    
    return min(100.0, (active_days / classroom_days * 100)) if classroom_days > 0 else 0.0


def _calculate_participation_score(
    db: Session,
    student_id: uuid.UUID,
    classroom_id: uuid.UUID,
    time_range: AnalyticsTimeRange
) -> float:
    """Calculate student's participation score in the classroom"""
    
    # Count various participation activities
    assignment_submissions = db.query(TaskStudents).join(
        Task, TaskStudents.task_id == Task.id
    ).filter(
        TaskStudents.student_id == student_id,
        Task.classroom_id == classroom_id,
        TaskStudents.submission_date >= time_range.start_date,
        TaskStudents.submission_date <= time_range.end_date
    ).count()
    
    # Count exam attempts
    exam_attempts = db.query(StudentExamAttempt).join(
        Exam, StudentExamAttempt.exam_id == Exam.id
    ).filter(
        StudentExamAttempt.student_id == student_id,
        StudentExamAttempt.started_at >= time_range.start_date,
        StudentExamAttempt.started_at <= time_range.end_date
    ).count()
    
    # Calculate participation score (simplified formula)
    participation_score = min(100.0, (assignment_submissions * 10 + exam_attempts * 15))
    
    return participation_score


def _calculate_assignment_metrics(
    db: Session,
    student_id: uuid.UUID,
    classroom_id: uuid.UUID,
    time_range: AnalyticsTimeRange
) -> Dict[str, Any]:
    """Calculate assignment-related metrics for the classroom"""
    
    # Get all assignments in the classroom (using TaskClassroom relationship)
    all_assignments = db.query(Task).join(
        TaskClassroom, Task.id == TaskClassroom.task_id
    ).filter(
        TaskClassroom.classroom_id == classroom_id,
        Task.created_at >= time_range.start_date,
        Task.created_at <= time_range.end_date
    ).all()

    # Get student's assignment submissions
    student_assignments = db.query(TaskStudents).join(
        Task, TaskStudents.task_id == Task.id
    ).join(
        TaskClassroom, Task.id == TaskClassroom.task_id
    ).filter(
        TaskStudents.student_id == student_id,
        TaskClassroom.classroom_id == classroom_id,
        Task.created_at >= time_range.start_date,
        Task.created_at <= time_range.end_date
    ).all()
    
    total_assignments = len(all_assignments)
    completed_assignments = len([a for a in student_assignments if a.submission_date])
    graded_assignments = [a for a in student_assignments if a.grade is not None]
    
    # Calculate rates
    submission_rate = (completed_assignments / total_assignments * 100) if total_assignments > 0 else 0.0
    
    # Calculate on-time submissions
    on_time_count = 0
    for assignment in student_assignments:
        if assignment.submission_date and assignment.task.deadline:
            if assignment.submission_date <= assignment.task.deadline:
                on_time_count += 1
    
    on_time_rate = (on_time_count / completed_assignments * 100) if completed_assignments > 0 else 0.0
    
    # Calculate score metrics
    scores = [a.grade for a in graded_assignments if a.grade is not None]
    average_score = sum(scores) / len(scores) if scores else 0.0
    best_score = max(scores) if scores else 0.0
    
    return {
        'total': total_assignments,
        'completed': completed_assignments,
        'submission_rate': submission_rate,
        'on_time_rate': on_time_rate,
        'average_score': average_score,
        'best_score': best_score
    }


def _calculate_classroom_performance_metrics(
    db: Session,
    student_id: uuid.UUID,
    classroom_id: uuid.UUID,
    time_range: AnalyticsTimeRange
) -> Dict[str, Any]:
    """Calculate performance metrics for the student in the classroom"""

    # Get student's scores in this classroom
    student_scores = []

    # Get assignment scores
    assignment_scores = db.query(TaskStudents.grade).join(
        Task, TaskStudents.task_id == Task.id
    ).join(
        TaskClassroom, Task.id == TaskClassroom.task_id
    ).filter(
        TaskStudents.student_id == student_id,
        TaskClassroom.classroom_id == classroom_id,
        TaskStudents.grade.isnot(None),
        Task.created_at >= time_range.start_date,
        Task.created_at <= time_range.end_date
    ).all()

    student_scores.extend([score[0] for score in assignment_scores])

    # Get exam scores (if exams are classroom-specific)
    exam_scores = []  # Placeholder - would need complex exam-classroom mapping

    student_average = sum(student_scores) / len(student_scores) if student_scores else 0.0

    # Calculate classroom average
    all_students = db.query(StudentClassroom.student_id).filter(
        StudentClassroom.classroom_id == classroom_id
    ).all()

    classroom_scores = []
    for student_tuple in all_students:
        sid = student_tuple[0]
        scores = db.query(TaskStudents.grade).join(
            Task, TaskStudents.task_id == Task.id
        ).join(
            TaskClassroom, Task.id == TaskClassroom.task_id
        ).filter(
            TaskStudents.student_id == sid,
            TaskClassroom.classroom_id == classroom_id,
            TaskStudents.grade.isnot(None),
            Task.created_at >= time_range.start_date,
            Task.created_at <= time_range.end_date
        ).all()

        if scores:
            avg_score = sum(score[0] for score in scores) / len(scores)
            classroom_scores.append(avg_score)

    classroom_average = sum(classroom_scores) / len(classroom_scores) if classroom_scores else 0.0

    # Calculate rank
    classroom_scores.sort(reverse=True)
    classroom_rank = 1
    for i, score in enumerate(classroom_scores, 1):
        if score <= student_average:
            classroom_rank = i
            break

    return {
        'student_average': student_average,
        'classroom_average': classroom_average,
        'performance_vs_classroom': student_average - classroom_average,
        'classroom_rank': classroom_rank,
        'total_students': len(all_students)
    }


def _calculate_engagement_indicators(
    db: Session,
    student_id: uuid.UUID,
    classroom_id: uuid.UUID,
    time_range: AnalyticsTimeRange
) -> Dict[str, int]:
    """Calculate detailed engagement indicators"""

    # These would involve tracking specific engagement activities
    # For now, return estimated values based on available data

    # Count assignment submissions as participation
    assignment_count = db.query(TaskStudents).join(
        Task, TaskStudents.task_id == Task.id
    ).join(
        TaskClassroom, Task.id == TaskClassroom.task_id
    ).filter(
        TaskStudents.student_id == student_id,
        TaskClassroom.classroom_id == classroom_id,
        TaskStudents.submission_date >= time_range.start_date,
        TaskStudents.submission_date <= time_range.end_date
    ).count()

    # Since we don't have specific engagement tracking tables,
    # return zeros instead of estimates
    return {
        'questions_asked': 0,
        'discussions_participated': 0,
        'peer_interactions': 0,
        'help_requests': 0
    }


def _calculate_time_metrics(
    db: Session,
    student_id: uuid.UUID,
    classroom_id: uuid.UUID,
    time_range: AnalyticsTimeRange
) -> Dict[str, float]:
    """Calculate time-based metrics for the classroom"""

    # Get assignment submission times to estimate time spent
    assignments = db.query(TaskStudents).join(
        Task, TaskStudents.task_id == Task.id
    ).join(
        TaskClassroom, Task.id == TaskClassroom.task_id
    ).filter(
        TaskStudents.student_id == student_id,
        TaskClassroom.classroom_id == classroom_id,
        TaskStudents.submission_date.isnot(None),
        Task.created_at >= time_range.start_date,
        Task.created_at <= time_range.end_date
    ).all()

    total_time_spent = 0.0
    active_learning_time = 0.0

    for assignment in assignments:
        if assignment.submission_date and assignment.task.created_at:
            # Estimate time spent on assignment
            time_diff = assignment.submission_date - assignment.task.created_at
            hours_spent = min(time_diff.total_seconds() / 3600, 24)  # Cap at 24 hours per assignment
            total_time_spent += hours_spent

            # Estimate active learning time (80% of total time)
            active_learning_time += hours_spent * 0.8

    return {
        'time_spent': total_time_spent,
        'active_time': active_learning_time
    }


def _get_teacher_feedback(
    db: Session,
    student_id: uuid.UUID,
    classroom_id: uuid.UUID,
    time_range: AnalyticsTimeRange
) -> Dict[str, Any]:
    """Get teacher feedback for the student in the classroom"""

    # Count positive feedback (assignments with high grades)
    high_grade_assignments = db.query(TaskStudents).join(
        Task, TaskStudents.task_id == Task.id
    ).filter(
        TaskStudents.student_id == student_id,
        Task.classroom_id == classroom_id,
        TaskStudents.grade >= 80,  # Consider 80+ as positive feedback
        Task.created_at >= time_range.start_date,
        Task.created_at <= time_range.end_date
    ).count()

    # Generate improvement suggestions based on performance
    suggestions = []

    # Get average grade
    avg_grade = db.query(func.avg(TaskStudents.grade)).join(
        Task, TaskStudents.task_id == Task.id
    ).join(
        TaskClassroom, Task.id == TaskClassroom.task_id
    ).filter(
        TaskStudents.student_id == student_id,
        TaskClassroom.classroom_id == classroom_id,
        TaskStudents.grade.isnot(None),
        Task.created_at >= time_range.start_date,
        Task.created_at <= time_range.end_date
    ).scalar() or 0

    if avg_grade < 70:
        suggestions.append("Focus on understanding core concepts")
        suggestions.append("Seek additional help during office hours")
    elif avg_grade < 85:
        suggestions.append("Work on improving assignment quality")
        suggestions.append("Participate more actively in class discussions")

    return {
        'positive_count': high_grade_assignments,
        'suggestions': suggestions
    }


def _calculate_overall_engagement_score(
    attendance_rate: float,
    participation_score: float,
    assignment_metrics: Dict[str, Any],
    performance_metrics: Dict[str, Any]
) -> float:
    """Calculate overall engagement score for the classroom"""

    # Weighted calculation of engagement score
    weights = {
        'attendance': 0.25,
        'participation': 0.25,
        'assignment_completion': 0.25,
        'performance': 0.25
    }

    engagement_score = (
        attendance_rate * weights['attendance'] +
        participation_score * weights['participation'] +
        assignment_metrics['submission_rate'] * weights['assignment_completion'] +
        min(100, performance_metrics['student_average']) * weights['performance']
    )

    return round(engagement_score, 2)


def _calculate_engagement_trend(
    db: Session,
    student_id: uuid.UUID,
    time_range: AnalyticsTimeRange
) -> str:
    """Calculate overall engagement trend across all classrooms"""

    # This would involve complex time-series analysis
    # For now, return a simple trend based on recent activity

    # Count recent activities
    recent_activities = db.query(TaskStudents).join(
        Task, TaskStudents.task_id == Task.id
    ).filter(
        TaskStudents.student_id == student_id,
        TaskStudents.submission_date >= time_range.end_date - timedelta(days=30),
        TaskStudents.submission_date <= time_range.end_date
    ).count()

    # Count older activities
    older_activities = db.query(TaskStudents).join(
        Task, TaskStudents.task_id == Task.id
    ).filter(
        TaskStudents.student_id == student_id,
        TaskStudents.submission_date >= time_range.start_date,
        TaskStudents.submission_date < time_range.end_date - timedelta(days=30)
    ).count()

    if recent_activities > older_activities * 1.1:
        return "improving"
    elif recent_activities < older_activities * 0.9:
        return "declining"
    else:
        return "stable"


def _generate_classroom_recommendations(
    classroom_metrics: List[ClassroomEngagementMetrics],
    overall_engagement_score: float
) -> List[str]:
    """Generate recommendations based on classroom analytics"""

    recommendations = []

    # Overall engagement recommendations
    if overall_engagement_score < 60:
        recommendations.append("Increase overall classroom participation and engagement")

    # Attendance recommendations
    low_attendance_classrooms = [
        cm for cm in classroom_metrics if cm.attendance_rate < 80
    ]
    if low_attendance_classrooms:
        recommendations.append(f"Improve attendance in: {', '.join([cm.classroom_name for cm in low_attendance_classrooms])}")

    # Assignment completion recommendations
    low_completion_classrooms = [
        cm for cm in classroom_metrics if cm.assignment_submission_rate < 70
    ]
    if low_completion_classrooms:
        recommendations.append("Focus on completing assignments on time")

    # Performance recommendations
    low_performance_classrooms = [
        cm for cm in classroom_metrics if cm.performance_vs_classroom < -10
    ]
    if low_performance_classrooms:
        recommendations.append("Seek additional support in underperforming subjects")

    # Time management recommendations
    late_submission_classrooms = [
        cm for cm in classroom_metrics if cm.on_time_submission_rate < 70
    ]
    if late_submission_classrooms:
        recommendations.append("Improve time management and assignment planning")

    return recommendations
