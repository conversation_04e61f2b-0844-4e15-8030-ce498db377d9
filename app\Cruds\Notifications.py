"""
Notifications CRUD Operations for EduFair Platform

This module contains CRUD operations for user notifications.
"""

from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func, desc
from fastapi import HTTPException, status
from uuid import UUID
from typing import List, Optional, Dict, Any
from datetime import datetime, timezone

from Models.users import User, UserNotification
from Schemas.Notifications import (
    NotificationCreate, NotificationResponse, NotificationListResponse,
    NotificationDashboard, NotificationStats, BulkNotificationCreate,
    NotificationType
)


# ==================== NOTIFICATION OPERATIONS ====================

def create_notification(db: Session, notification_data: NotificationCreate) -> NotificationResponse:
    """Create a new notification"""
    
    # Check if user exists
    user = db.query(User).filter(User.id == notification_data.user_id).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    
    # Create notification
    db_notification = UserNotification(
        user_id=notification_data.user_id,
        title=notification_data.title,
        message=notification_data.message,
        notification_type=notification_data.notification_type.value,
        related_id=notification_data.related_id,
        created_at=datetime.now(timezone.utc)
    )
    
    db.add(db_notification)
    db.commit()
    db.refresh(db_notification)
    
    return NotificationResponse.model_validate(db_notification)


def get_notification(db: Session, notification_id: UUID, user_id: UUID) -> NotificationResponse:
    """Get a specific notification"""
    
    notification = db.query(UserNotification).filter(
        and_(
            UserNotification.id == notification_id,
            UserNotification.user_id == user_id
        )
    ).first()
    
    if not notification:
        raise HTTPException(status_code=404, detail="Notification not found")
    
    return NotificationResponse.model_validate(notification)


def get_user_notifications(
    db: Session,
    user_id: UUID,
    page: int = 1,
    page_size: int = 20,
    unread_only: bool = False,
    notification_type: Optional[str] = None
) -> NotificationListResponse:
    """Get notifications for a user"""
    
    offset = (page - 1) * page_size
    
    # Build query
    query = db.query(UserNotification).filter(UserNotification.user_id == user_id)
    
    if unread_only:
        query = query.filter(UserNotification.is_read == False)
    
    if notification_type:
        query = query.filter(UserNotification.notification_type == notification_type)
    
    # Get total count
    total_count = query.count()
    
    # Get unread count
    unread_count = db.query(UserNotification).filter(
        and_(
            UserNotification.user_id == user_id,
            UserNotification.is_read == False
        )
    ).count()
    
    # Get notifications
    notifications = query.order_by(desc(UserNotification.created_at)).offset(offset).limit(page_size).all()
    
    return NotificationListResponse(
        notifications=[NotificationResponse.model_validate(n) for n in notifications],
        total_count=total_count,
        unread_count=unread_count,
        page=page,
        page_size=page_size,
        has_next=offset + page_size < total_count,
        has_previous=page > 1
    )


def mark_notification_as_read(db: Session, notification_id: UUID, user_id: UUID) -> bool:
    """Mark a notification as read"""
    
    notification = db.query(UserNotification).filter(
        and_(
            UserNotification.id == notification_id,
            UserNotification.user_id == user_id,
            UserNotification.is_read == False
        )
    ).first()
    
    if not notification:
        return False
    
    notification.is_read = True
    notification.read_at = datetime.now(timezone.utc)
    db.commit()
    
    return True


def mark_all_notifications_as_read(db: Session, user_id: UUID) -> int:
    """Mark all notifications as read for a user"""
    
    unread_notifications = db.query(UserNotification).filter(
        and_(
            UserNotification.user_id == user_id,
            UserNotification.is_read == False
        )
    ).all()
    
    read_time = datetime.now(timezone.utc)
    count = 0
    
    for notification in unread_notifications:
        notification.is_read = True
        notification.read_at = read_time
        count += 1
    
    db.commit()
    return count


def delete_notification(db: Session, notification_id: UUID, user_id: UUID) -> bool:
    """Delete a notification"""
    
    notification = db.query(UserNotification).filter(
        and_(
            UserNotification.id == notification_id,
            UserNotification.user_id == user_id
        )
    ).first()
    
    if not notification:
        raise HTTPException(status_code=404, detail="Notification not found")
    
    db.delete(notification)
    db.commit()
    
    return True


# ==================== BULK OPERATIONS ====================

def create_bulk_notifications(db: Session, bulk_data: BulkNotificationCreate) -> Dict[str, Any]:
    """Create notifications for multiple users"""
    
    successful_notifications = []
    failed_notifications = []
    
    for user_id in bulk_data.user_ids:
        try:
            # Check if user exists
            user = db.query(User).filter(User.id == user_id).first()
            if not user:
                failed_notifications.append({"user_id": user_id, "error": "User not found"})
                continue
            
            # Create notification
            db_notification = UserNotification(
                user_id=user_id,
                title=bulk_data.title,
                message=bulk_data.message,
                notification_type=bulk_data.notification_type.value,
                related_id=bulk_data.related_id,
                created_at=datetime.now(timezone.utc)
            )
            
            db.add(db_notification)
            successful_notifications.append(user_id)
            
        except Exception as e:
            failed_notifications.append({"user_id": user_id, "error": str(e)})
    
    db.commit()
    
    return {
        "successful_notifications": successful_notifications,
        "failed_notifications": failed_notifications,
        "total_processed": len(bulk_data.user_ids),
        "success_count": len(successful_notifications),
        "failure_count": len(failed_notifications)
    }


def delete_bulk_notifications(db: Session, user_id: UUID, notification_ids: List[UUID]) -> Dict[str, Any]:
    """Delete multiple notifications"""
    
    successful_deletions = []
    failed_deletions = []
    
    for notification_id in notification_ids:
        try:
            notification = db.query(UserNotification).filter(
                and_(
                    UserNotification.id == notification_id,
                    UserNotification.user_id == user_id
                )
            ).first()
            
            if notification:
                db.delete(notification)
                successful_deletions.append(notification_id)
            else:
                failed_deletions.append({"notification_id": notification_id, "error": "Not found"})
                
        except Exception as e:
            failed_deletions.append({"notification_id": notification_id, "error": str(e)})
    
    db.commit()
    
    return {
        "successful_deletions": successful_deletions,
        "failed_deletions": failed_deletions,
        "total_processed": len(notification_ids),
        "success_count": len(successful_deletions),
        "failure_count": len(failed_deletions)
    }


# ==================== NOTIFICATION STATISTICS ====================

def get_notification_stats(db: Session, user_id: UUID) -> NotificationStats:
    """Get notification statistics for a user"""
    
    # Total notifications
    total_notifications = db.query(UserNotification).filter(
        UserNotification.user_id == user_id
    ).count()
    
    # Unread notifications
    unread_notifications = db.query(UserNotification).filter(
        and_(
            UserNotification.user_id == user_id,
            UserNotification.is_read == False
        )
    ).count()
    
    # Notifications today
    today = datetime.now(timezone.utc).date()
    notifications_today = db.query(UserNotification).filter(
        and_(
            UserNotification.user_id == user_id,
            func.date(UserNotification.created_at) == today
        )
    ).count()
    
    # Most common notification type
    most_common_type = db.query(
        UserNotification.notification_type,
        func.count(UserNotification.notification_type).label('count')
    ).filter(
        UserNotification.user_id == user_id
    ).group_by(
        UserNotification.notification_type
    ).order_by(desc('count')).first()
    
    # Last notification date
    last_notification = db.query(UserNotification).filter(
        UserNotification.user_id == user_id
    ).order_by(desc(UserNotification.created_at)).first()
    
    return NotificationStats(
        user_id=user_id,
        total_notifications=total_notifications,
        unread_notifications=unread_notifications,
        notifications_today=notifications_today,
        most_common_type=most_common_type.notification_type if most_common_type else None,
        last_notification_date=last_notification.created_at if last_notification else None
    )


def get_notification_dashboard(db: Session, user_id: UUID) -> NotificationDashboard:
    """Get notification dashboard data for a user"""
    
    # Get basic stats
    stats = get_notification_stats(db, user_id)
    
    # Get notifications by type
    notifications_by_type = db.query(
        UserNotification.notification_type,
        func.count(UserNotification.notification_type).label('count')
    ).filter(
        UserNotification.user_id == user_id
    ).group_by(
        UserNotification.notification_type
    ).all()
    
    # Get recent notifications
    recent_notifications = db.query(UserNotification).filter(
        UserNotification.user_id == user_id
    ).order_by(desc(UserNotification.created_at)).limit(5).all()
    
    return NotificationDashboard(
        total_notifications=stats.total_notifications,
        unread_notifications=stats.unread_notifications,
        notifications_by_type=[
            {"notification_type": nt.notification_type, "count": nt.count}
            for nt in notifications_by_type
        ],
        recent_notifications=[
            NotificationResponse.model_validate(n) for n in recent_notifications
        ]
    )


# ==================== NOTIFICATION HELPERS ====================

def create_follow_notification(db: Session, follower_id: UUID, following_id: UUID) -> Optional[NotificationResponse]:
    """Create a notification when someone follows a user"""
    
    follower = db.query(User).filter(User.id == follower_id).first()
    if not follower:
        return None
    
    notification_data = NotificationCreate(
        user_id=following_id,
        title="New Follower",
        message=f"{follower.username} started following you",
        notification_type=NotificationType.FOLLOW,
        related_id=follower_id
    )
    
    return create_notification(db, notification_data)


def create_message_notification(db: Session, sender_id: UUID, receiver_id: UUID, message_preview: str) -> Optional[NotificationResponse]:
    """Create a notification when someone sends a message"""
    
    sender = db.query(User).filter(User.id == sender_id).first()
    if not sender:
        return None
    
    # Truncate message preview
    preview = message_preview[:50] + "..." if len(message_preview) > 50 else message_preview
    
    notification_data = NotificationCreate(
        user_id=receiver_id,
        title="New Message",
        message=f"{sender.username}: {preview}",
        notification_type=NotificationType.MESSAGE,
        related_id=sender_id
    )
    
    return create_notification(db, notification_data)
