from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List
from uuid import UUID
from Schemas.Token import Token
from config.deps import get_current_user
from config.permission import require_type
from config.security import oauth2_scheme
from Schemas.TeacherModule.tasks import (
    ChapterCreate, ChapterOut, ChapterUpdate, ChapterListOut, ChapterWithTopicsOut
)
from Cruds.TeacherModule import tasks as crud
from config.session import get_db

router = APIRouter()

# ===== CHAPTER ROUTES =====
@router.post("/", response_model=ChapterOut)
def create_chapter(chapter: ChapterCreate, db: Session = Depends(get_db), token: Token = Depends(oauth2_scheme),
    _ = Depends(require_type("admin"))):
    return crud.create_chapter(db, chapter)

@router.get("/", response_model=ChapterListOut)
def get_all_chapters(skip: int = 0, limit: int = 100, db: Session = Depends(get_db), token: Token = Depends(oauth2_scheme)):
    return crud.get_all_chapters(db, skip, limit)

@router.get("/{chapter_id}", response_model=ChapterOut)
def get_chapter_by_id(chapter_id: UUID, db: Session = Depends(get_db), token: Token = Depends(oauth2_scheme)):
    return crud.get_chapter_by_id(db, chapter_id)

@router.get("/subjects/{subject_id}/chapters", response_model=ChapterListOut)
def get_chapters_by_subject(subject_id: UUID, skip: int = 0, limit: int = 100, db: Session = Depends(get_db), token: Token = Depends(oauth2_scheme)):
    return crud.get_chapters_by_subject(db, subject_id, skip, limit)

@router.put("/{chapter_id}", response_model=ChapterOut)
def update_chapter(chapter_id: UUID, chapter: ChapterUpdate, db: Session = Depends(get_db), token: Token = Depends(oauth2_scheme),
    _ = Depends(require_type("admin"))):
    return crud.update_chapter(db, chapter_id, chapter)

@router.delete("/{chapter_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_chapter(chapter_id: UUID, db: Session = Depends(get_db), token: Token = Depends(oauth2_scheme),
    _ = Depends(require_type("admin"))):
    crud.delete_chapter(db, chapter_id)
    return None

@router.get("/{chapter_id}/topics", response_model=ChapterWithTopicsOut)
def get_chapter_with_topics(chapter_id: UUID, db: Session = Depends(get_db), token: Token = Depends(oauth2_scheme)):
    return crud.get_chapter_with_topics(db, chapter_id) 