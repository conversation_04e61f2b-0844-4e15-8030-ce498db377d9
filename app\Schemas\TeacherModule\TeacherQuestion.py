from pydantic import BaseModel, Field
from uuid import UUID
from Schemas.Exams.Questions import QuestionCreate

class TeacherQuestionBase(BaseModel):
    teacher_profile_id: UUID = Field(...)

class TeacherQuestionCreate(TeacherQuestionBase):
    question: QuestionCreate  # This already includes marks via QuestionCreate

class TeacherQuestionOut(TeacherQuestionBase):
    id: UUID
    question_id: UUID
    class Config:
        from_attributes = True 