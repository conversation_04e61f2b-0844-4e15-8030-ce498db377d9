"""
EduFair API Main Application

This module initializes and configures the FastAPI application for the EduFair platform.
It sets up middleware, routes, database connections, and background services.
"""

from fastapi import FastAPI
from fastapi.staticfiles import StaticFiles

from config.app_config import create_app
from config.database_setup import setup_database
from config.middleware_setup import setup_middleware
from config.static_files import setup_static_files
from Routes.router_setup import setup_routers
from services.background_services import BackgroundServiceManager
from services.startup_service import StartupService
from services.shutdown_service import ShutdownService

# Initialize application components
startup_service = StartupService()

# Create FastAPI app
app = create_app()

# Setup application components
setup_database()
setup_middleware(app)
setup_static_files(app)
setup_routers(app)

# Application event handlers
@app.on_event("startup")
async def startup_event():
    """Application startup event handler"""
    await startup_service.initialize_application()


@app.on_event("shutdown")
async def shutdown_event():
    """Application shutdown event handler"""
    background_manager = startup_service.get_background_manager()
    shutdown_service = ShutdownService(background_manager)
    await shutdown_service.cleanup_application()


