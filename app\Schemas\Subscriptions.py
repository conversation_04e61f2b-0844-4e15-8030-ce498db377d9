from pydantic import BaseModel, <PERSON>, validator
from uuid import UUID
from typing import List, Optional, Dict, Any
from datetime import datetime
from decimal import Decimal
from enum import Enum

from Models.users import UserTypeEnum


class PlanTypeEnum(str, Enum):
    BASIC = "basic"
    PREMIUM = "premium"
    PRO = "pro"
    HOME_TUTOR = "home_tutor"


class SubscriptionStatusEnum(str, Enum):
    ACTIVE = "active"
    EXPIRED = "expired"
    CANCELLED = "cancelled"
    TRIAL = "trial"
    SUSPENDED = "suspended"


class BillingCycleEnum(str, Enum):
    MONTHLY = "monthly"
    YEARLY = "yearly"
    LIFETIME = "lifetime"


# Subscription Plan Schemas
class SubscriptionPlanBase(BaseModel):
    name: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = None
    price: int = Field(..., ge=0, description="Price in cents")
    duration_days: int = Field(..., gt=0)
    features: Optional[Dict[str, Any]] = None
    is_active: bool = True
    plan_type: PlanTypeEnum
    target_user_type: UserTypeEnum
    
    # Plan limits and features
    max_classrooms: Optional[int] = Field(None, ge=0)
    max_students_per_classroom: Optional[int] = Field(None, ge=0)
    max_exams_per_month: Optional[int] = Field(None, ge=0)
    max_questions_per_exam: Optional[int] = Field(None, ge=0)
    allows_home_tutoring: bool = False
    allows_ai_question_generation: bool = False
    allows_advanced_analytics: bool = False
    priority_support: bool = False


class SubscriptionPlanCreate(SubscriptionPlanBase):
    pass


class SubscriptionPlanUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = None
    price: Optional[int] = Field(None, ge=0)
    duration_days: Optional[int] = Field(None, gt=0)
    features: Optional[Dict[str, Any]] = None
    is_active: Optional[bool] = None
    
    # Plan limits and features
    max_classrooms: Optional[int] = Field(None, ge=0)
    max_students_per_classroom: Optional[int] = Field(None, ge=0)
    max_exams_per_month: Optional[int] = Field(None, ge=0)
    max_questions_per_exam: Optional[int] = Field(None, ge=0)
    allows_home_tutoring: Optional[bool] = None
    allows_ai_question_generation: Optional[bool] = None
    allows_advanced_analytics: Optional[bool] = None
    priority_support: Optional[bool] = None


class SubscriptionPlanOut(SubscriptionPlanBase):
    id: UUID
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


# User Subscription Schemas
class UserSubscriptionBase(BaseModel):
    plan_id: Optional[UUID] = None
    status: SubscriptionStatusEnum = SubscriptionStatusEnum.ACTIVE
    auto_renew: bool = True
    billing_cycle: BillingCycleEnum = BillingCycleEnum.MONTHLY
    is_trial: bool = False


class UserSubscriptionCreate(UserSubscriptionBase):
    user_id: UUID


class UserSubscriptionUpdate(BaseModel):
    plan_id: Optional[UUID] = None
    status: Optional[SubscriptionStatusEnum] = None
    auto_renew: Optional[bool] = None
    billing_cycle: Optional[BillingCycleEnum] = None
    end_date: Optional[datetime] = None


class UserSubscriptionOut(UserSubscriptionBase):
    id: UUID
    user_id: UUID
    start_date: datetime
    end_date: Optional[datetime]
    trial_end_date: Optional[datetime]
    next_billing_date: Optional[datetime]
    payment_reference: Optional[str]
    current_usage: Optional[Dict[str, Any]]
    created_at: datetime
    updated_at: datetime
    
    # Include plan details
    plan: Optional[SubscriptionPlanOut] = None

    class Config:
        from_attributes = True


# Teacher Profile Schemas (Enhanced)
class TeacherSubjectAssignment(BaseModel):
    subject_id: UUID
    subject_name: str


class TeacherProfileUpdate(BaseModel):
    bio: Optional[str] = None
    experience_years: Optional[int] = Field(None, ge=0, le=50)
    specialization: Optional[str] = None
    website: Optional[str] = None
    office_hours: Optional[str] = None
    
    # Home tutoring features
    offers_home_tutoring: Optional[bool] = None
    home_address: Optional[str] = None
    latitude: Optional[Decimal] = Field(None, ge=-90, le=90)
    longitude: Optional[Decimal] = Field(None, ge=-180, le=180)
    tutoring_radius_km: Optional[int] = Field(None, ge=1, le=100)
    hourly_rate_home: Optional[Decimal] = Field(None, ge=0)
    hourly_rate_online: Optional[Decimal] = Field(None, ge=0)
    
    # Contact preferences
    preferred_contact_method: Optional[str] = None
    whatsapp_number: Optional[str] = None
    
    # Availability
    available_days: Optional[List[str]] = None
    available_hours: Optional[Dict[str, List[str]]] = None
    
    # Subject assignments
    subject_ids: Optional[List[UUID]] = None


class TeacherProfileOut(BaseModel):
    id: UUID
    teacher_id: UUID
    bio: Optional[str]
    experience_years: Optional[int]
    specialization: Optional[str]
    website: Optional[str]
    office_hours: Optional[str]
    rating: Optional[Decimal]
    
    # Home tutoring features
    offers_home_tutoring: bool
    home_address: Optional[str]
    latitude: Optional[Decimal]
    longitude: Optional[Decimal]
    tutoring_radius_km: Optional[int]
    hourly_rate_home: Optional[Decimal]
    hourly_rate_online: Optional[Decimal]
    
    # Contact preferences
    preferred_contact_method: Optional[str]
    whatsapp_number: Optional[str]
    
    # Availability
    available_days: Optional[List[str]]
    available_hours: Optional[Dict[str, List[str]]]
    
    # Subjects
    subjects: List[TeacherSubjectAssignment] = []
    
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


# Home Tutoring Search Schemas
class HomeTutoringSearchFilter(BaseModel):
    subject_ids: Optional[List[UUID]] = None
    latitude: Optional[Decimal] = Field(None, ge=-90, le=90)
    longitude: Optional[Decimal] = Field(None, ge=-180, le=180)
    radius_km: Optional[int] = Field(None, ge=1, le=50)
    max_hourly_rate: Optional[Decimal] = Field(None, ge=0)
    min_rating: Optional[Decimal] = Field(None, ge=0, le=5)
    available_days: Optional[List[str]] = None


class HomeTutorOut(BaseModel):
    teacher_id: UUID
    username: str
    full_name: str
    bio: Optional[str]
    experience_years: Optional[int]
    rating: Optional[Decimal]
    hourly_rate_home: Optional[Decimal]
    hourly_rate_online: Optional[Decimal]
    subjects: List[TeacherSubjectAssignment]
    distance_km: Optional[float]
    available_days: Optional[List[str]]
    preferred_contact_method: Optional[str]
    whatsapp_number: Optional[str]


class HomeTutoringSearchResponse(BaseModel):
    tutors: List[HomeTutorOut]
    total: int
    search_radius_km: int
    center_latitude: Decimal
    center_longitude: Decimal


# Subscription Analytics
class SubscriptionStatsOut(BaseModel):
    total_subscriptions: int
    active_subscriptions: int
    trial_subscriptions: int
    expired_subscriptions: int
    revenue_this_month: Decimal
    revenue_total: Decimal
    subscriptions_by_plan: Dict[str, int]
    subscriptions_by_user_type: Dict[str, int]


# Usage tracking schemas
class UsageMetrics(BaseModel):
    classrooms_created: int = 0
    students_enrolled: int = 0
    exams_created: int = 0
    questions_generated: int = 0
    ai_questions_used: int = 0
    analytics_views: int = 0


class SubscriptionUsageOut(BaseModel):
    user_id: UUID
    plan_name: str
    current_usage: UsageMetrics
    plan_limits: Dict[str, Optional[int]]
    usage_percentage: Dict[str, Optional[float]]
    is_over_limit: bool
    warnings: List[str] = []
