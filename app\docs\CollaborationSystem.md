# Mentor-Institute Collaboration System

## Overview

This system provides a comprehensive solution for managing collaborations between mentors and institutes, supporting bidirectional invitations and full CRUD operations for collaboration management.

## Key Features

### 1. Bidirectional Invitations
- **Mentor → Institute**: Mentors can invite institutes to collaborate
- **Institute → Mentor**: Institutes can invite mentors to join them

### 2. Collaboration Management
- Full CRUD operations for collaborations
- Status tracking (active, ended, pending)
- Contract terms and rate management
- Start/end date tracking

### 3. Well-Structured Routes
- Separate route files for mentor and institute perspectives
- Proper authentication and authorization
- Comprehensive API endpoints

## Database Schema

### Updated MentorInstituteAssociation Model
```python
class MentorInstituteAssociation(BaseModel):
    mentor_id: UUID
    institute_id: UUID
    status: str = "active"
    hourly_rate: Optional[float]
    hours_per_week: Optional[int]
    contract_terms: Optional[str]
    start_date: datetime
    end_date: Optional[datetime]
    created_from_invite_id: Optional[UUID]
    created_from_application_id: Optional[UUID]
```

### CollaborationDetails Schema
```python
class CollaborationDetails(BaseModel):
    id: UUID
    mentor: Mentor 
    institute: Institute
    status: str
    hourly_rate: Optional[float]
    hours_per_week: Optional[int]
    contract_terms: Optional[str]
    start_date: datetime
    end_date: Optional[datetime]
    created_at: datetime
    updated_at: datetime
```

## API Endpoints

### Mentor Routes (`/mentors/collaborations/`)

#### Collaboration Management
- `GET /collaborations` - List mentor's collaborations
- `GET /collaborations/{id}` - Get specific collaboration
- `PUT /collaborations/{id}` - Update collaboration
- `DELETE /collaborations/{id}` - Delete collaboration
- `POST /collaborations` - Create new collaboration (admin)

#### Invitation Management
- `POST /invitations/send-to-institute` - Send invitation to institute
- `GET /invitations/sent` - List sent invitations
- `GET /invitations/received` - List received invitations
- `POST /invitations/{id}/respond` - Respond to invitation

### Institute Routes (`/institutes/collaborations/`)

#### Collaboration Management
- `GET /collaborations` - List institute's collaborations
- `GET /collaborations/{id}` - Get specific collaboration
- `PUT /collaborations/{id}` - Update collaboration
- `POST /collaborations/{id}/end` - End collaboration

#### Invitation Management
- `POST /invitations/send-to-mentor` - Send invitation to mentor
- `GET /invitations/sent` - List sent invitations
- `GET /invitations/received` - List received invitations
- `POST /invitations/{id}/respond` - Respond to invitation

#### Mentor Discovery
- `GET /mentors/search` - Search available mentors
- `GET /mentors/{id}` - Get mentor details

## CRUD Functions

### CollaborationCrud.py
- `create_collaboration()` - Create new collaboration
- `get_collaboration_by_id()` - Retrieve collaboration
- `update_collaboration()` - Update collaboration details
- `delete_collaboration()` - Delete collaboration
- `list_collaborations()` - List with filtering and pagination

### Enhanced Mentor.py CRUD
- `create_invite_to_institute()` - Mentor invites institute
- `create_invite_to_mentor()` - Institute invites mentor
- `list_sent_invites_to_institutes()` - List mentor's sent invites
- `list_sent_invites_to_mentors()` - List institute's sent invites
- `respond_to_received_invite()` - Mentor responds to invite
- `respond_to_received_invite_as_institute()` - Institute responds to invite

## Usage Examples

### 1. Mentor Inviting Institute
```python
# POST /mentors/collaborations/invitations/send-to-institute
{
    "receiver_id": "institute-uuid",
    "hourly_rate": 50.0,
    "hours_per_week": 20
}
```

### 2. Institute Inviting Mentor
```python
# POST /institutes/collaborations/invitations/send-to-mentor
{
    "receiver_id": "mentor-uuid", 
    "hourly_rate": 45.0,
    "hours_per_week": 15
}
```

### 3. Responding to Invitation
```python
# POST /mentors/collaborations/invitations/{id}/respond
{
    "accept": true,
    "hourly_rate": 55.0,  # Counter-offer
    "hours_per_week": 18
}
```

### 4. Creating Direct Collaboration
```python
# POST /mentors/collaborations/collaborations
{
    "mentor_id": "mentor-uuid",
    "institute_id": "institute-uuid",
    "hourly_rate": 50.0,
    "hours_per_week": 20,
    "contract_terms": "6-month contract with renewal option"
}
```

### 5. Updating Collaboration
```python
# PUT /mentors/collaborations/collaborations/{id}
{
    "status": "active",
    "hourly_rate": 60.0,
    "contract_terms": "Updated terms"
}
```

## Key Features Implemented

### ✅ Bidirectional Invitations
- Mentors can invite institutes
- Institutes can invite mentors
- Proper tracking of who receives the invitation

### ✅ Complete CRUD Operations
- Create, Read, Update, Delete for collaborations
- Comprehensive filtering and pagination
- Status management

### ✅ Well-Structured Routes
- Separate files for mentor and institute perspectives
- Proper authentication and authorization
- RESTful API design

### ✅ Enhanced Database Model
- Updated MentorInstituteAssociation with all required fields
- Proper relationships and foreign keys
- Status tracking and audit fields

### ✅ Comprehensive Schemas
- CollaborationDetails with nested Mentor/Institute objects
- CRUD schemas for create/update operations
- Proper validation and type hints

## Security & Authorization

- All routes require authentication via JWT tokens
- Role-based access control (mentor/institute specific routes)
- Users can only access their own collaborations and invitations
- Proper validation of ownership before allowing operations

## Next Steps

1. **Testing**: Write comprehensive unit tests for all CRUD operations
2. **Integration**: Integrate routes into main FastAPI application
3. **Documentation**: Add OpenAPI documentation for all endpoints
4. **Validation**: Add more robust input validation and error handling
5. **Notifications**: Implement email/SMS notifications for invitations
6. **Analytics**: Add collaboration analytics and reporting features

## File Structure

```
EduFair/app/
├── Models/users.py (updated MentorInstituteAssociation)
├── Schemas/Mentors/MentorInstitutes.py (updated with new schemas)
├── Cruds/
│   ├── Mentors/CollaborationCrud.py (new)
│   └── Institute/Mentor.py (enhanced)
└── Routes/
    ├── Mentors/Collaborations.py (new)
    └── Institute/Collaborations.py (new)
```
