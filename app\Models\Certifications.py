"""
Certification Models for Competition System
"""
from sqlalchemy import Column, String, Text, Integer, DateTime, ForeignKey, Boolean, JSON, Enum, Numeric
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from .baseModel import BaseModel
from datetime import datetime, timezone
import uuid
import enum


class CertificationTypeEnum(enum.Enum):
    WINNER = "winner"
    RUNNER_UP = "runner_up"
    PARTICIPATION = "participation"
    EXCELLENCE = "excellence"
    MERIT = "merit"
    DISTINCTION = "distinction"
    ACHIEVEMENT = "achievement"


class CertificationStatusEnum(enum.Enum):
    PENDING = "pending"
    APPROVED = "approved"
    ISSUED = "issued"
    REVOKED = "revoked"


class PerformanceTierEnum(enum.Enum):
    TOP_1_PERCENT = "top_1_percent"
    TOP_5_PERCENT = "top_5_percent"
    TOP_10_PERCENT = "top_10_percent"
    TOP_25_PERCENT = "top_25_percent"
    TOP_50_PERCENT = "top_50_percent"
    ABOVE_AVERAGE = "above_average"
    AVERAGE = "average"
    BELOW_AVERAGE = "below_average"


class CertificationTemplate(BaseModel):
    __tablename__ = 'certification_templates'
    
    name = Column(String(200), nullable=False)
    description = Column(Text, nullable=True)
    certification_type = Column(Enum(CertificationTypeEnum), nullable=False)
    institute_id = Column(UUID(as_uuid=True), ForeignKey('users.id'), nullable=False)
    
    # Template design and content
    template_design = Column(JSON, nullable=True)  # Design configuration
    certificate_text = Column(Text, nullable=True)  # Certificate content template
    logo_url = Column(String(500), nullable=True)
    background_image_url = Column(String(500), nullable=True)
    
    # Criteria for awarding this certification
    min_score_percentage = Column(Numeric(5, 2), nullable=True)
    max_score_percentage = Column(Numeric(5, 2), nullable=True)
    min_percentile = Column(Numeric(5, 2), nullable=True)
    max_percentile = Column(Numeric(5, 2), nullable=True)
    required_performance_tier = Column(Enum(PerformanceTierEnum), nullable=True)
    
    # Additional criteria
    min_participants_required = Column(Integer, default=1)  # Minimum participants for this cert to be valid
    requires_mentor_approval = Column(Boolean, default=True)
    auto_award = Column(Boolean, default=False)  # Automatically award when criteria met
    
    # Template settings
    is_active = Column(Boolean, default=True)
    is_default = Column(Boolean, default=False)  # Default template for this type
    
    # Relationships
    institute = relationship('User', backref='certification_templates')
    certifications = relationship('CompetitionCertification', back_populates='template', cascade='all, delete-orphan')


class CompetitionCertification(BaseModel):
    __tablename__ = 'competition_certifications'
    
    # Core certification info
    competition_id = Column(UUID(as_uuid=True), ForeignKey('events.id'), nullable=False)
    participant_id = Column(UUID(as_uuid=True), ForeignKey('users.id'), nullable=False)
    template_id = Column(UUID(as_uuid=True), ForeignKey('certification_templates.id'), nullable=False)
    
    # Certification details
    certification_type = Column(Enum(CertificationTypeEnum), nullable=False)
    status = Column(Enum(CertificationStatusEnum), default=CertificationStatusEnum.PENDING)
    
    # Performance metrics
    final_score = Column(Numeric(5, 2), nullable=False)
    score_percentage = Column(Numeric(5, 2), nullable=False)
    percentile_rank = Column(Numeric(5, 2), nullable=False)
    performance_tier = Column(Enum(PerformanceTierEnum), nullable=False)
    rank_position = Column(Integer, nullable=False)
    total_participants = Column(Integer, nullable=False)
    
    # Certification content
    certificate_title = Column(String(300), nullable=False)
    certificate_description = Column(Text, nullable=True)
    achievement_details = Column(JSON, nullable=True)  # Specific achievements
    
    # Mentor evaluation
    evaluated_by = Column(UUID(as_uuid=True), ForeignKey('users.id'), nullable=True)  # Mentor who evaluated
    mentor_comments = Column(Text, nullable=True)
    mentor_rating = Column(Numeric(3, 2), nullable=True)  # 1-5 rating from mentor
    evaluation_date = Column(DateTime(timezone=True), nullable=True)
    
    # Approval workflow
    approved_by = Column(UUID(as_uuid=True), ForeignKey('users.id'), nullable=True)  # Institute admin
    approval_date = Column(DateTime(timezone=True), nullable=True)
    approval_notes = Column(Text, nullable=True)
    
    # Certificate generation
    certificate_url = Column(String(500), nullable=True)  # Generated certificate file URL
    certificate_hash = Column(String(128), nullable=True)  # For verification
    issued_date = Column(DateTime(timezone=True), nullable=True)
    expiry_date = Column(DateTime(timezone=True), nullable=True)
    
    # Verification
    verification_code = Column(String(50), unique=True, nullable=True)
    is_verified = Column(Boolean, default=False)
    verification_url = Column(String(500), nullable=True)
    
    # Relationships
    competition = relationship('Event', backref='certifications')
    participant = relationship('User', foreign_keys=[participant_id], backref='received_certifications')
    template = relationship('CertificationTemplate', back_populates='certifications')
    evaluator = relationship('User', foreign_keys=[evaluated_by], overlaps="received_certifications")
    approver = relationship('User', foreign_keys=[approved_by], overlaps="received_certifications,evaluator")
    
    __table_args__ = (
        {'schema': None},  # Use default schema
    )


class CertificationCriteria(BaseModel):
    __tablename__ = 'certification_criteria'
    
    competition_id = Column(UUID(as_uuid=True), ForeignKey('events.id'), nullable=False)
    certification_type = Column(Enum(CertificationTypeEnum), nullable=False)
    
    # Score-based criteria
    min_score = Column(Numeric(5, 2), nullable=True)
    min_percentage = Column(Numeric(5, 2), nullable=True)
    
    # Rank-based criteria
    max_rank = Column(Integer, nullable=True)  # Top N positions
    min_percentile = Column(Numeric(5, 2), nullable=True)  # Top X percentile
    
    # Performance tier criteria
    required_tier = Column(Enum(PerformanceTierEnum), nullable=True)
    
    # Additional requirements
    min_completion_time = Column(Integer, nullable=True)  # Minimum time spent (minutes)
    max_completion_time = Column(Integer, nullable=True)  # Maximum time allowed (minutes)
    requires_all_questions_attempted = Column(Boolean, default=False)
    min_correct_answers = Column(Integer, nullable=True)
    
    # Mentor evaluation requirements
    requires_mentor_approval = Column(Boolean, default=True)
    min_mentor_rating = Column(Numeric(3, 2), nullable=True)
    
    # Award limits
    max_awards = Column(Integer, nullable=True)  # Maximum number of this certification type
    award_percentage = Column(Numeric(5, 2), nullable=True)  # Percentage of participants to award
    
    # Template to use
    template_id = Column(UUID(as_uuid=True), ForeignKey('certification_templates.id'), nullable=False)
    
    # Settings
    is_active = Column(Boolean, default=True)
    auto_award = Column(Boolean, default=False)
    
    # Relationships
    competition = relationship('Event', backref='certification_criteria')
    template = relationship('CertificationTemplate', backref='criteria')


class CertificationAuditLog(BaseModel):
    __tablename__ = 'certification_audit_logs'
    
    certification_id = Column(UUID(as_uuid=True), ForeignKey('competition_certifications.id'), nullable=False)
    action = Column(String(50), nullable=False)  # created, evaluated, approved, issued, revoked
    performed_by = Column(UUID(as_uuid=True), ForeignKey('users.id'), nullable=False)
    action_date = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc))
    details = Column(JSON, nullable=True)
    notes = Column(Text, nullable=True)
    
    # Relationships
    certification = relationship('CompetitionCertification', backref='audit_logs')
    performer = relationship('User', backref='certification_actions')


class CertificationVerification(BaseModel):
    __tablename__ = 'certification_verifications'
    
    certification_id = Column(UUID(as_uuid=True), ForeignKey('competition_certifications.id'), nullable=False)
    verification_code = Column(String(50), unique=True, nullable=False)
    verified_by = Column(UUID(as_uuid=True), ForeignKey('users.id'), nullable=True)  # Who verified it
    verification_date = Column(DateTime(timezone=True), nullable=True)
    verification_method = Column(String(50), nullable=True)  # email, qr_code, manual, etc.
    verification_ip = Column(String(45), nullable=True)
    verification_location = Column(String(200), nullable=True)
    
    # Verification status
    is_valid = Column(Boolean, default=True)
    verification_notes = Column(Text, nullable=True)
    
    # Relationships
    certification = relationship('CompetitionCertification', backref='verifications')
    verifier = relationship('User', backref='verified_certifications')
