from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from uuid import UUID

# Import CRUD functions
from Cruds.Institute.Dashboard import (
    get_dashboard_summary, get_quick_stats, get_recent_activities,
    get_notifications, get_analytics
)

# Import Schemas
from Schemas.Institute.Dashboard import (
    DashboardSummaryOut, QuickStatsOut, RecentActivitiesResponse,
    NotificationsResponse, AnalyticsOut, GrowthMetricsOut,
    EventSuccessMetric, CustomReportRequest, CustomReportOut
)

# Import dependencies
from config.session import get_db
from config.security import oauth2_scheme
from config.deps import get_current_user
from config.permission import require_type

router = APIRouter()


@router.get("/summary", response_model=DashboardSummaryOut)
def get_dashboard_summary_route(
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """Get institute dashboard summary statistics"""
    current_user = get_current_user(token, db)
    return get_dashboard_summary(db, current_user.id)


@router.get("/analytics", response_model=AnalyticsOut)
def get_dashboard_analytics_route(
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """Get comprehensive analytics for institute dashboard"""
    current_user = get_current_user(token, db)
    return get_analytics(db, current_user.id)


@router.get("/recent-activities", response_model=RecentActivitiesResponse)
def get_recent_activities_route(
    limit: int = Query(10, ge=1, le=50, description="Number of activities to return"),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """Get recent activities for institute"""
    current_user = get_current_user(token, db)
    return get_recent_activities(db, current_user.id, limit)


@router.get("/notifications", response_model=NotificationsResponse)
def get_notifications_route(
    limit: int = Query(10, ge=1, le=50, description="Number of notifications to return"),
    unread_only: bool = Query(False, description="Return only unread notifications"),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """Get notifications for institute"""
    current_user = get_current_user(token, db)
    return get_notifications(db, current_user.id, limit, unread_only)


@router.get("/quick-stats", response_model=QuickStatsOut)
def get_quick_stats_route(
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """Get quick statistics for institute dashboard"""
    current_user = get_current_user(token, db)
    return get_quick_stats(db, current_user.id)


@router.get("/mentor-performance", response_model=List[dict])
def get_mentor_performance_analytics(
    months: int = Query(6, ge=1, le=24, description="Number of months to analyze"),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """Get mentor performance analytics over time"""
    current_user = get_current_user(token, db)
    analytics = get_analytics(db, current_user.id)
    return analytics.mentor_performance[:months]


@router.get("/event-engagement", response_model=List[dict])
def get_event_engagement_analytics(
    limit: int = Query(10, ge=1, le=50, description="Number of events to analyze"),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """Get event engagement analytics"""
    current_user = get_current_user(token, db)
    analytics = get_analytics(db, current_user.id)
    return analytics.event_stats[:limit]


@router.get("/mentor-effectiveness", response_model=List[dict])
def get_mentor_effectiveness_analytics(
    limit: int = Query(10, ge=1, le=50, description="Number of mentors to analyze"),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """Get mentor effectiveness analytics"""
    current_user = get_current_user(token, db)
    analytics = get_analytics(db, current_user.id)
    return analytics.mentor_effectiveness[:limit]


@router.get("/event-success-metrics", response_model=EventSuccessMetric)
def get_event_success_metrics_route(
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """Get event success metrics for institute"""
    current_user = get_current_user(token, db)
    
    # Import here to avoid circular imports
    from Models.Events import Event, EventRegistration, EventStatusEnum
    from sqlalchemy import func
    from decimal import Decimal
    
    # Get event statistics
    total_events = db.query(Event).filter(Event.institute_id == current_user.id).count()
    
    completed_events = db.query(Event).filter(
        Event.institute_id == current_user.id,
        Event.status == EventStatusEnum.completed
    ).all()
    
    # Calculate success metrics (placeholder logic)
    successful_events = len([e for e in completed_events if True])  # Placeholder: all completed events are "successful"
    average_attendance_rate = 85.0  # Placeholder
    average_satisfaction_score = 4.2  # Placeholder
    total_revenue = Decimal("0.00")  # Placeholder
    roi_percentage = 15.5  # Placeholder
    
    return EventSuccessMetric(
        total_events=total_events,
        successful_events=successful_events,
        average_attendance_rate=average_attendance_rate,
        average_satisfaction_score=average_satisfaction_score,
        total_revenue=total_revenue,
        roi_percentage=roi_percentage
    )


@router.get("/growth-metrics", response_model=List[GrowthMetricsOut])
def get_growth_metrics_route(
    period: str = Query("month", description="Time period (month, quarter, year)"),
    periods: int = Query(6, ge=1, le=24, description="Number of periods to analyze"),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """Get growth metrics for institute"""
    current_user = get_current_user(token, db)
    
    # Placeholder implementation
    growth_metrics = []
    for i in range(periods):
        growth_metrics.append(GrowthMetricsOut(
            period=f"2024-{12-i:02d}" if period == "month" else f"2024-Q{4-i//3}",
            mentor_growth=15.2 + (i * 2.1),
            event_growth=25.0 + (i * 1.5),
            attendee_growth=18.5 + (i * 3.2),
            revenue_growth=12.8 + (i * 2.8)
        ))
    
    return growth_metrics


@router.post("/custom-report", response_model=CustomReportOut)
def create_custom_report(
    report_request: CustomReportRequest,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """Create a custom report for institute"""
    current_user = get_current_user(token, db)
    
    # Placeholder implementation - in real system would queue report generation
    import uuid
    from datetime import datetime, timezone
    
    report_id = uuid.uuid4()
    
    return CustomReportOut(
        report_id=report_id,
        report_name=report_request.report_name,
        report_type=report_request.report_type,
        status="generating",
        created_at=datetime.now(timezone.utc)
    )


@router.get("/export/{report_id}")
def export_report(
    report_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """Export a generated report"""
    current_user = get_current_user(token, db)
    
    # Placeholder implementation
    return {
        "report_id": str(report_id),
        "download_url": f"/api/institute/reports/download/{report_id}",
        "status": "ready",
        "expires_at": "2024-12-31T23:59:59Z"
    }
