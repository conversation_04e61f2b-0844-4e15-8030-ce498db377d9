from typing import List, Dict, Any, Optional
from datetime import datetime, timezone, timedelta
from calendar import monthrange
import uuid
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, or_

# Import Models
from Models.Events import Event, EventRegistration, EventStatusEnum, RegistrationStatusEnum
from Models.Exam import Exam, StudentExamAssignment
from Models.Tasks import Task, TaskStudents
from Models.users import User

# Import Schemas
from Schemas.Events.Events import CalendarEventOut, CalendarResponse, EventCategoryOut, EventLocationOut


class CalendarService:
    def __init__(self):
        pass

    def get_comprehensive_calendar(
        self,
        db: Session,
        year: int,
        month: int,
        user_id: uuid.UUID,
        include_events: bool = True,
        include_exams: bool = True,
        include_tasks: bool = True,
        category_filter: Optional[List[uuid.UUID]] = None
    ) -> Dict[str, Any]:
        """Get comprehensive calendar view including events, exams, and tasks"""
        
        # Get first and last day of the month
        first_day = datetime(year, month, 1, tzinfo=timezone.utc)
        last_day_num = monthrange(year, month)[1]
        last_day = datetime(year, month, last_day_num, 23, 59, 59, tzinfo=timezone.utc)
        
        calendar_items = []
        
        # Get events
        if include_events:
            events = self._get_calendar_events(db, first_day, last_day, user_id, category_filter)
            calendar_items.extend(events)
        
        # Get exams
        if include_exams:
            exams = self._get_calendar_exams(db, first_day, last_day, user_id)
            calendar_items.extend(exams)
        
        # Get tasks
        if include_tasks:
            tasks = self._get_calendar_tasks(db, first_day, last_day, user_id)
            calendar_items.extend(tasks)
        
        # Sort by start time
        calendar_items.sort(key=lambda x: x.get('start_datetime', x.get('start_time')))
        
        return {
            "year": year,
            "month": month,
            "items": calendar_items,
            "total_items": len(calendar_items),
            "summary": self._get_calendar_summary(calendar_items)
        }

    def _get_calendar_events(
        self,
        db: Session,
        start_date: datetime,
        end_date: datetime,
        user_id: uuid.UUID,
        category_filter: Optional[List[uuid.UUID]] = None
    ) -> List[Dict[str, Any]]:
        """Get events for calendar view"""
        
        query = db.query(Event).options(
            joinedload(Event.category),
            joinedload(Event.location)
        ).filter(
            Event.start_datetime >= start_date,
            Event.start_datetime <= end_date,
            Event.status == EventStatusEnum.PUBLISHED,
            Event.is_public == True
        )
        
        if category_filter:
            query = query.filter(Event.category_id.in_(category_filter))
        
        events = query.all()
        
        calendar_events = []
        for event in events:
            # Check if user is registered
            registration = db.query(EventRegistration).filter(
                EventRegistration.event_id == event.id,
                EventRegistration.user_id == user_id
            ).first()
            
            is_registered = registration is not None
            registration_status = registration.status if registration else None
            
            calendar_events.append({
                "id": str(event.id),
                "title": event.title,
                "type": "event",
                "category": "event",
                "start_datetime": event.start_datetime,
                "end_datetime": event.end_datetime,
                "description": event.short_description,
                "location": event.location.name if event.location else None,
                "is_virtual": event.location.is_virtual if event.location else False,
                "event_category": event.category.name,
                "event_category_color": event.category.color,
                "is_registered": is_registered,
                "registration_status": registration_status.value if registration_status else None,
                "is_competition": event.is_competition,
                "organizer_id": str(event.organizer_id),
                "banner_image_url": event.banner_image_url,
                "is_featured": event.is_featured
            })
        
        return calendar_events

    def _get_calendar_exams(
        self,
        db: Session,
        start_date: datetime,
        end_date: datetime,
        user_id: uuid.UUID
    ) -> List[Dict[str, Any]]:
        """Get exams for calendar view"""
        
        exams = db.query(Exam).join(StudentExamAssignment).filter(
            StudentExamAssignment.student_id == user_id,
            Exam.start_time >= start_date,
            Exam.start_time <= end_date
        ).all()
        
        calendar_exams = []
        for exam in exams:
            end_time = exam.start_time + timedelta(minutes=exam.total_duration) if exam.start_time else None
            
            calendar_exams.append({
                "id": str(exam.id),
                "title": exam.title,
                "type": "exam",
                "category": "academic",
                "start_datetime": exam.start_time,
                "end_datetime": end_time,
                "description": exam.description,
                "duration_minutes": exam.total_duration,
                "total_marks": exam.total_marks,
                "location": None,  # Exams are typically online
                "is_virtual": True
            })
        
        return calendar_exams

    def _get_calendar_tasks(
        self,
        db: Session,
        start_date: datetime,
        end_date: datetime,
        user_id: uuid.UUID
    ) -> List[Dict[str, Any]]:
        """Get tasks for calendar view"""
        
        tasks = db.query(Task).join(TaskStudents).filter(
            TaskStudents.student_id == user_id,
            Task.deadline >= start_date,
            Task.deadline <= end_date
        ).all()
        
        calendar_tasks = []
        for task in tasks:
            # Get submission status
            task_student = db.query(TaskStudents).filter(
                TaskStudents.task_id == task.id,
                TaskStudents.student_id == user_id
            ).first()
            
            is_submitted = task_student.submission_date is not None if task_student else False
            is_overdue = task.deadline < datetime.now(timezone.utc) and not is_submitted
            
            calendar_tasks.append({
                "id": str(task.id),
                "title": task.name,
                "type": "task",
                "category": "academic",
                "start_datetime": task.deadline,
                "end_datetime": task.deadline,
                "description": task.description,
                "is_submitted": is_submitted,
                "is_overdue": is_overdue,
                "submission_date": task_student.submission_date if task_student else None,
                "grade": task_student.grade if task_student else None,
                "location": None,
                "is_virtual": True
            })
        
        return calendar_tasks

    def _get_calendar_summary(self, calendar_items: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Generate summary statistics for calendar items"""
        
        total_items = len(calendar_items)
        events_count = len([item for item in calendar_items if item["type"] == "event"])
        exams_count = len([item for item in calendar_items if item["type"] == "exam"])
        tasks_count = len([item for item in calendar_items if item["type"] == "task"])
        
        # Count registered events
        registered_events = len([
            item for item in calendar_items 
            if item["type"] == "event" and item.get("is_registered", False)
        ])
        
        # Count overdue tasks
        overdue_tasks = len([
            item for item in calendar_items 
            if item["type"] == "task" and item.get("is_overdue", False)
        ])
        
        # Count submitted tasks
        submitted_tasks = len([
            item for item in calendar_items 
            if item["type"] == "task" and item.get("is_submitted", False)
        ])
        
        return {
            "total_items": total_items,
            "events_count": events_count,
            "exams_count": exams_count,
            "tasks_count": tasks_count,
            "registered_events": registered_events,
            "overdue_tasks": overdue_tasks,
            "submitted_tasks": submitted_tasks,
            "completion_rate": (submitted_tasks / tasks_count * 100) if tasks_count > 0 else 0
        }

    def get_upcoming_items(
        self,
        db: Session,
        user_id: uuid.UUID,
        days_ahead: int = 7,
        limit: int = 10
    ) -> List[Dict[str, Any]]:
        """Get upcoming calendar items for the next N days"""
        
        now = datetime.now(timezone.utc)
        end_date = now + timedelta(days=days_ahead)
        
        # Get all upcoming items
        calendar_items = []
        
        # Events
        events = self._get_calendar_events(db, now, end_date, user_id)
        calendar_items.extend(events)
        
        # Exams
        exams = self._get_calendar_exams(db, now, end_date, user_id)
        calendar_items.extend(exams)
        
        # Tasks
        tasks = self._get_calendar_tasks(db, now, end_date, user_id)
        calendar_items.extend(tasks)
        
        # Sort by start time and limit
        calendar_items.sort(key=lambda x: x.get('start_datetime', x.get('start_time')))
        
        return calendar_items[:limit]

    def get_daily_schedule(
        self,
        db: Session,
        user_id: uuid.UUID,
        date: datetime
    ) -> Dict[str, Any]:
        """Get detailed schedule for a specific day"""
        
        # Get start and end of the day
        start_of_day = date.replace(hour=0, minute=0, second=0, microsecond=0)
        end_of_day = date.replace(hour=23, minute=59, second=59, microsecond=999999)
        
        # Get all items for the day
        calendar_items = []
        
        # Events
        events = self._get_calendar_events(db, start_of_day, end_of_day, user_id)
        calendar_items.extend(events)
        
        # Exams
        exams = self._get_calendar_exams(db, start_of_day, end_of_day, user_id)
        calendar_items.extend(exams)
        
        # Tasks
        tasks = self._get_calendar_tasks(db, start_of_day, end_of_day, user_id)
        calendar_items.extend(tasks)
        
        # Sort by start time
        calendar_items.sort(key=lambda x: x.get('start_datetime', x.get('start_time')))
        
        return {
            "date": date.date(),
            "items": calendar_items,
            "total_items": len(calendar_items),
            "has_conflicts": self._check_time_conflicts(calendar_items)
        }

    def _check_time_conflicts(self, calendar_items: List[Dict[str, Any]]) -> bool:
        """Check if there are any time conflicts in the schedule"""
        
        # Sort items by start time
        sorted_items = sorted(
            calendar_items,
            key=lambda x: x.get('start_datetime', x.get('start_time'))
        )
        
        for i in range(len(sorted_items) - 1):
            current_item = sorted_items[i]
            next_item = sorted_items[i + 1]
            
            current_end = current_item.get('end_datetime', current_item.get('start_datetime'))
            next_start = next_item.get('start_datetime', next_item.get('start_time'))
            
            if current_end and next_start and current_end > next_start:
                return True
        
        return False


# Global calendar service instance
calendar_service = CalendarService()
