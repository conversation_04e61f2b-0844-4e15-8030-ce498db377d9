import uuid
from sqlalchemy.orm import Session
from fastapi import HTT<PERSON><PERSON>x<PERSON>
from Models.Questions import Question, McqsQuestionsOptions
from Models.TeacherQuestion import TeacherQuestion
from Models.users import TeacherProfile, Subject
from Models.Class import ClassNumber
from Models.Chapter import Chapter, Topic, SubTopic
from Schemas.Exams.Questions import QuestionCreate, QuestionUpdate, McqOptionCreate, TeacherProfileOut
from config.deps import get_current_user
from motor.motor_asyncio import AsyncIOMotorClient
from bson import ObjectId
from config.mongodb import get_mongodb
import httpx
from config.config import settings
import os
from typing import Optional
import json
import re

def resolve_curriculum_ids(db: Session, class_name: str, subject_name: str, chapter_name: str, topic_name: Optional[str] = None, subtopic_name: Optional[str] = None):
    """
    Resolve string names to UUIDs for curriculum entities.
    Returns a dict with class_id, subject_id, chapter_id, topic_id, subtopic_id
    """
    # Get class_id by ClassNo
    class_obj = db.query(ClassNumber).filter(ClassNumber.ClassNo == class_name).first()
    if not class_obj:
        raise HTTPException(status_code=404, detail=f"Class '{class_name}' not found")

    # Get subject_id by name
    subject_obj = db.query(Subject).filter(Subject.name == subject_name).first()
    if not subject_obj:
        raise HTTPException(status_code=404, detail=f"Subject '{subject_name}' not found")

    # Get chapter_id by name and subject_id
    chapter_obj = db.query(Chapter).filter(
        Chapter.name == chapter_name,
        Chapter.subject_id == subject_obj.id
    ).first()
    if not chapter_obj:
        raise HTTPException(status_code=404, detail=f"Chapter '{chapter_name}' not found for subject '{subject_name}'")

    # Get topic_id if provided
    topic_id = None
    if topic_name:
        topic_obj = db.query(Topic).filter(
            Topic.name == topic_name,
            Topic.chapter_id == chapter_obj.id
        ).first()
        if topic_obj:
            topic_id = topic_obj.id

    # Get subtopic_id if provided
    subtopic_id = None
    if subtopic_name and topic_id:
        subtopic_obj = db.query(SubTopic).filter(
            SubTopic.name == subtopic_name,
            SubTopic.topic_id == topic_id
        ).first()
        if subtopic_obj:
            subtopic_id = subtopic_obj.id

    return {
        "class_id": class_obj.id,
        "subject_id": subject_obj.id,
        "chapter_id": chapter_obj.id,
        "topic_id": topic_id,
        "subtopic_id": subtopic_id
    }

def create_question(db: Session, question_in: QuestionCreate, user) -> Question:
    new_question = Question(
        text=question_in.text,
        answer=question_in.answer,
        Type=question_in.Type.value if hasattr(question_in.Type, 'value') else question_in.Type,
        Level=question_in.Level.value if hasattr(question_in.Level, 'value') else question_in.Level,
        imageUrl=question_in.imageUrl,
        class_id=question_in.class_id,
        subject_id=question_in.subject_id,
        chapter_id=question_in.chapter_id,
        topic_id=question_in.topic_id,
        subtopic_id=question_in.subtopic_id,
        teacher_id=user.id,
        marks=question_in.marks if hasattr(question_in, 'marks') else 1
    )
    db.add(new_question)
    db.flush()
    # Add MCQ options if provided
    if (question_in.Type.value if hasattr(question_in.Type, 'value') else question_in.Type) == 'MCQS' and question_in.options:
        for opt in question_in.options:
            option = McqsQuestionsOptions(
                question_id=new_question.id,
                option_text=opt.option_text,
                is_correct=opt.is_correct
            )
            db.add(option)
    # Add TeacherQuestion relation if user is a teacher
    if user.user_type.value == 'teacher' and user.teacher_profile:
        tq = TeacherQuestion(teacher_profile_id=user.teacher_profile.id, question_id=new_question.id)
        db.add(tq)
    db.commit()
    db.refresh(new_question)
    return new_question

def get_all_questions(db: Session):
    return db.query(Question).all()

def get_question_by_id(db: Session, question_id: uuid.UUID):
    question = db.query(Question).filter(Question.id == question_id).first()
    if not question:
        raise HTTPException(status_code=404, detail="Question not found.")
    # Attach teacher_profile_id and teacher_profile if exists
    tq = db.query(TeacherQuestion).filter(TeacherQuestion.question_id == question_id).first()
    if tq:
        question.teacher_profile_id = tq.teacher_profile_id
        question.teacher_profile = tq.teacher_profile
    return question

def update_question(db: Session, question_id: uuid.UUID, question_update: QuestionUpdate):
    question = db.query(Question).filter(Question.id == question_id).first()
    if not question:
        raise HTTPException(status_code=404, detail="Question not found.")
    if question_update.text is not None:
        question.text = question_update.text
    if question_update.answer is not None:
        question.answer = question_update.answer
    if question_update.Type is not None:
        question.Type = question_update.Type.value if hasattr(question_update.Type, 'value') else question_update.Type
    if question_update.Level is not None:
        question.Level = question_update.Level.value if hasattr(question_update.Level, 'value') else question_update.Level
    if question_update.imageUrl is not None:
        question.imageUrl = question_update.imageUrl
    if question_update.class_id is not None:
        question.class_id = question_update.class_id
    if question_update.subject_id is not None:
        question.subject_id = question_update.subject_id
    if question_update.chapter_id is not None:
        question.chapter_id = question_update.chapter_id
    if question_update.topic_id is not None:
        question.topic_id = question_update.topic_id
    if question_update.subtopic_id is not None:
        question.subtopic_id = question_update.subtopic_id
    if question_update.marks is not None:
        question.marks = question_update.marks
    # Update MCQ options if provided
    if (question_update.Type.value if hasattr(question_update.Type, 'value') else question_update.Type) == 'MCQS' and question_update.options is not None:
        # Remove old options
        db.query(McqsQuestionsOptions).filter(McqsQuestionsOptions.question_id == question_id).delete()
        # Add new options
        for opt in question_update.options:
            option = McqsQuestionsOptions(
                question_id=question_id,
                option_text=opt.option_text,
                is_correct=opt.is_correct
            )
            db.add(option)
    db.commit()
    db.refresh(question)
    return question

def delete_question(db: Session, question_id: uuid.UUID):
    question = db.query(Question).filter(Question.id == question_id).first()
    if not question:
        raise HTTPException(status_code=404, detail="Question not found.")
    # Delete related TeacherQuestion entries
    db.query(TeacherQuestion).filter(TeacherQuestion.question_id == question_id).delete()
    db.delete(question)
    db.commit()
    return None

def update_mcq_option_text(db: Session, option_id: uuid.UUID, option_update) -> McqsQuestionsOptions:
    option = db.query(McqsQuestionsOptions).filter(McqsQuestionsOptions.id == option_id).first()
    if not option:
        raise HTTPException(status_code=404, detail="Option not found.")
    option.option_text = option_update.option_text
    db.commit()
    db.refresh(option)
    return option

async def get_data_for_AI(class_: str, subject: str, chapter: str):
    db = await get_mongodb()  # Get the MongoDB client from the app config
    collection = db["Edufair_AI_Data"]
    query = {
        "class": class_,
        "subject": subject,
        "chapter": chapter
    }
    result = await collection.find_one(query)
    
    if result and "_id" in result:
        result["_id"] = str(result["_id"])
    return result

def normalize_question_fields(question):
    """
    Normalize and validate question fields to match expected schema.
    Maps common AI variations to expected field names.
    """
    # Field mapping for common AI variations
    field_mappings = {
        'solution_steps': 'answer',
        'solution': 'answer',
        'correct_answer': 'answer',
        'explanation': 'answer',
        'response': 'answer',
        'type': 'Type',
        'question_type': 'Type',
        'difficulty': 'Level',
        'difficulty_level': 'Level',
        'level': 'Level',
        'question_text': 'text',
        'question': 'text',
        'image_url': 'imageUrl',
        'image': 'imageUrl',
        'score': 'marks',
        'points': 'marks'
    }

    # Create normalized question
    normalized = {}

    # Apply field mappings
    for key, value in question.items():
        mapped_key = field_mappings.get(key, key)
        normalized[mapped_key] = value

    # Ensure required fields exist with defaults
    required_fields = {
        'text': '',
        'answer': '',
        'Type': 'MCQS',
        'Level': 'MEDIUM',
        'imageUrl': None,
        'marks': 1
    }

    for field, default_value in required_fields.items():
        if field not in normalized or normalized[field] is None:
            if field == 'text' and not normalized.get('text'):
                raise ValueError(f"Question text is required but missing or empty")
            if field == 'answer' and not normalized.get('answer'):
                raise ValueError(f"Question answer is required but missing or empty")
            normalized[field] = default_value

    # Validate and normalize enum values
    valid_types = ['MCQS', 'SHORT', 'LONG']
    valid_levels = ['EASY', 'MEDIUM', 'HARD']

    if normalized['Type'] not in valid_types:
        # Try to map common variations
        type_mappings = {
            'MCQ': 'MCQS',
            'MULTIPLE_CHOICE': 'MCQS',
            'MULTIPLE CHOICE': 'MCQS',
            'SHORT_ANSWER': 'SHORT',
            'SHORT ANSWER': 'SHORT',
            'LONG_ANSWER': 'LONG',
            'LONG ANSWER': 'LONG',
            'ESSAY': 'LONG'
        }
        normalized['Type'] = type_mappings.get(normalized['Type'].upper(), 'MCQS')

    if normalized['Level'] not in valid_levels:
        # Try to map common variations
        level_mappings = {
            'LOW': 'EASY',
            'BASIC': 'EASY',
            'SIMPLE': 'EASY',
            'INTERMEDIATE': 'MEDIUM',
            'MODERATE': 'MEDIUM',
            'AVERAGE': 'MEDIUM',
            'HIGH': 'HARD',
            'DIFFICULT': 'HARD',
            'COMPLEX': 'HARD',
            'ADVANCED': 'HARD'
        }
        normalized['Level'] = level_mappings.get(normalized['Level'].upper(), 'MEDIUM')

    # Ensure marks is a positive integer
    try:
        normalized['marks'] = max(1, int(normalized['marks']))
    except (ValueError, TypeError):
        normalized['marks'] = 1

    # Handle options for MCQS
    if normalized['Type'] == 'MCQS':
        if 'options' not in normalized or not normalized['options']:
            raise ValueError("MCQS questions must have options array")

        # Auto-fix common AI mistakes with options format
        if isinstance(normalized['options'], list) and len(normalized['options']) > 0:
            # Check if it's an array of strings (common AI mistake)
            if all(isinstance(opt, str) for opt in normalized['options']):
                print(f"DEBUG: Converting string array options to proper format: {normalized['options']}")
                # Convert string array to proper format
                string_options = normalized['options']
                normalized['options'] = []
                for i, opt_text in enumerate(string_options):
                    normalized['options'].append({
                        'option_text': opt_text.strip(),
                        'is_correct': i == 0  # Mark first as correct by default
                    })
                print(f"DEBUG: Converted to: {normalized['options']}")

            # Check if it's an array of mixed types
            elif any(not isinstance(opt, (dict, str)) for opt in normalized['options']):
                print(f"DEBUG: Mixed option types detected: {[type(opt) for opt in normalized['options']]}")
                # Try to convert each option to proper format
                fixed_options = []
                for i, opt in enumerate(normalized['options']):
                    if isinstance(opt, dict):
                        fixed_options.append(opt)
                    else:
                        fixed_options.append({
                            'option_text': str(opt).strip(),
                            'is_correct': i == 0
                        })
                normalized['options'] = fixed_options

        # Validate and normalize options structure
        normalized_options = []
        for i, option in enumerate(normalized['options']):
            # Handle different option formats
            if isinstance(option, str):
                # Option is just a string - convert to proper format
                normalized_option = {
                    'option_text': option.strip(),
                    'is_correct': False  # Will be set later if needed
                }
            elif isinstance(option, dict):
                # Normalize option fields - handle common AI variations
                normalized_option = {}

                # Map option text field variations
                option_text_fields = ['option_text', 'text', 'option', 'choice', 'answer_option', 'content']
                option_text = None
                for field in option_text_fields:
                    if field in option and option[field]:
                        option_text = str(option[field]).strip()
                        break

                if not option_text:
                    # Log the actual option structure for debugging
                    print(f"DEBUG: Option {i} structure: {option}")
                    available_fields = list(option.keys()) if isinstance(option, dict) else "Not a dict"
                    raise ValueError(f"Option {i} missing 'option_text' field. Available fields: {available_fields}")

                normalized_option['option_text'] = option_text

                # Map is_correct field variations
                is_correct_fields = ['is_correct', 'correct', 'is_answer', 'is_right', 'answer']
                is_correct = False
                for field in is_correct_fields:
                    if field in option:
                        # Handle various boolean representations
                        value = option[field]
                        if isinstance(value, bool):
                            is_correct = value
                        elif isinstance(value, str):
                            is_correct = value.lower() in ['true', 'yes', '1', 'correct']
                        elif isinstance(value, (int, float)):
                            is_correct = bool(value)
                        break

                normalized_option['is_correct'] = is_correct
            else:
                # Option is neither string nor dict - try to convert
                print(f"DEBUG: Option {i} unexpected type: {type(option)}, value: {option}")
                try:
                    option_text = str(option).strip()
                    if option_text:
                        normalized_option = {
                            'option_text': option_text,
                            'is_correct': False
                        }
                    else:
                        raise ValueError(f"Option {i} cannot be converted to text")
                except Exception as e:
                    raise ValueError(f"Option {i} must be a dictionary or string, got {type(option)}: {option}")

            normalized_options.append(normalized_option)

        normalized['options'] = normalized_options

        # Ensure we have exactly 4 options
        if len(normalized_options) < 4:
            # Pad with generic options if needed
            while len(normalized_options) < 4:
                normalized_options.append({
                    'option_text': f"Option {len(normalized_options) + 1}",
                    'is_correct': False
                })
        elif len(normalized_options) > 4:
            # Trim to 4 options
            normalized_options = normalized_options[:4]

        # Ensure at least one correct answer
        has_correct = any(opt['is_correct'] for opt in normalized_options)
        if not has_correct:
            # Mark the first option as correct if none are marked
            normalized_options[0]['is_correct'] = True

        # Ensure only one correct answer
        correct_count = sum(1 for opt in normalized_options if opt['is_correct'])
        if correct_count > 1:
            # Keep only the first correct answer
            found_correct = False
            for opt in normalized_options:
                if opt['is_correct'] and found_correct:
                    opt['is_correct'] = False
                elif opt['is_correct']:
                    found_correct = True

    else:
        # Remove options for non-MCQS questions
        normalized.pop('options', None)

    return normalized

def extract_questions_from_gemini_response(gemini_result):
    """
    Extracts the questions array from the Gemini LLM response.
    Handles code block markers, parses JSON, and normalizes fields.
    """
    try:
        # Step 1: Navigate to the text field
        text = (
            gemini_result["candidates"][0]["content"]["parts"][0]["text"]
        )

        # Step 2: Clean the text and extract JSON
        # Remove code block markers (```json ... ```) if present
        json_match = re.search(r"```(?:json)?\s*(.*?)\s*```", text, re.DOTALL)
        if json_match:
            questions_json = json_match.group(1)
        else:
            # Remove any backticks and extra whitespace
            questions_json = text.strip("`").strip()

        # Additional cleaning: remove any leading/trailing non-JSON text
        # Look for the first [ and last ] to extract just the JSON array
        start_idx = questions_json.find('[')
        end_idx = questions_json.rfind(']')

        if start_idx != -1 and end_idx != -1 and end_idx > start_idx:
            questions_json = questions_json[start_idx:end_idx + 1]

        # Step 3: Parse the JSON
        parsed_data = json.loads(questions_json)

        # Step 4: Handle different response formats
        questions = None
        if isinstance(parsed_data, list):
            # Direct list of questions
            questions = parsed_data
        elif isinstance(parsed_data, dict):
            # Check if it's wrapped in an object with a questions key
            if "questions" in parsed_data:
                questions = parsed_data["questions"]
            elif "data" in parsed_data:
                questions = parsed_data["data"]
            else:
                # If it's a single question object, wrap it in a list
                questions = [parsed_data]
        else:
            raise ValueError(f"Unexpected parsed data type: {type(parsed_data)}")

        # Step 5: Validate that questions is a list and each item is a dict
        if not isinstance(questions, list):
            raise ValueError(f"Questions is not a list: {type(questions)}")

        # Step 6: Normalize each question to match expected schema
        normalized_questions = []
        for i, question in enumerate(questions):
            if not isinstance(question, dict):
                raise ValueError(f"Question at index {i} is not a dictionary: {type(question)}")

            try:
                normalized_question = normalize_question_fields(question)
                normalized_questions.append(normalized_question)
            except ValueError as e:
                # Log the actual question structure for debugging
                print(f"DEBUG: Question {i+1} structure: {question}")
                raise ValueError(f"Question {i+1} validation failed: {str(e)}")

        return normalized_questions
    except json.JSONDecodeError as e:
        raise ValueError(f"Failed to parse JSON: {e}")
    except Exception as e:
        # Log the actual response for debugging
        print(f"DEBUG: Gemini response text: {text[:500]}...")
        raise ValueError(f"Failed to extract questions: {e}")

async def call_gemini_with_ai_data(
    class_: str,
    subject: str,
    chapter: str,
    no_of_questions: int = 3,
    topic: Optional[str] = None,
    subtopic: Optional[str] = None,
    no_of_easy: Optional[int] = None,
    no_of_medium: Optional[int] = None,
    no_of_hard: Optional[int] = None
):
    # Cap the number of questions at 20
    no_of_questions = min(max(no_of_questions, 1), 20)

    # Validate difficulty distribution if provided
    if any([no_of_easy, no_of_medium, no_of_hard]):
        total_difficulty_questions = (no_of_easy or 0) + (no_of_medium or 0) + (no_of_hard or 0)
        if total_difficulty_questions != no_of_questions:
            raise HTTPException(
                status_code=400,
                detail=f"Sum of difficulty questions ({total_difficulty_questions}) must equal total questions ({no_of_questions})"
            )

    # Step 1: Get data from MongoDB
    ai_data = await get_data_for_AI(class_, subject, chapter)
    context_available = ai_data is not None

    # Determine text data source - automatically generate with or without context
    if ai_data is not None:
        text_data = ai_data["data"] if ai_data and "data" in ai_data else ""
    else:
        # No context available, but generate anyway
        text_data = ""

    # Step 2: Prepare Gemini API call
    api_key = getattr(settings, "GEMINI_API_KEY", None) or os.getenv("GEMINI_API_KEY")
    if not api_key:
        raise RuntimeError("GEMINI_API_KEY not set in environment or config.")

    url = f"https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent?key={api_key}"

    # Build the advanced prompt based on available context
    prompt_parts = [
        f"You are an expert question generator for grade {class_} {subject}.",
        f"Given the following chapter: '{chapter}'.",
    ]

    # Add topic and subtopic if provided
    if topic:
        prompt_parts.append(f"Focus on the topic: '{topic}'.")
    if subtopic:
        prompt_parts.append(f"If possible, relate to the subtopic: '{subtopic}'.")

    if text_data:
        prompt_parts.append(f"Here is the reference text data (do NOT copy questions from it, only use it for inspiration):\n{text_data}")
    else:
        prompt_parts.append("No specific curriculum context available. Generate questions based on standard curriculum expectations for this grade and subject.")

    # Add difficulty distribution requirements
    if any([no_of_easy, no_of_medium, no_of_hard]):
        difficulty_requirements = []
        if no_of_easy:
            difficulty_requirements.append(f"{no_of_easy} EASY questions")
        if no_of_medium:
            difficulty_requirements.append(f"{no_of_medium} MEDIUM questions")
        if no_of_hard:
            difficulty_requirements.append(f"{no_of_hard} HARD questions")

        prompt_parts.extend([
            f"Generate exactly {no_of_questions} questions with the following difficulty distribution:",
            f"- {', '.join(difficulty_requirements)}",
            "Each question must match EXACTLY the following JSON structure, using only these fields:",
        ])
    else:
        prompt_parts.extend([
            f"Generate {no_of_questions} unique, original, and challenging questions with mixed difficulty levels in JSON format.",
            "CRITICAL: You must use EXACTLY these field names - no variations allowed:",
            "- 'text' (not 'question', 'question_text', etc.)",
            "- 'answer' (not 'solution', 'solution_steps', 'correct_answer', etc.)",
            "- 'Type' (not 'type', 'question_type', etc.)",
            "- 'Level' (not 'level', 'difficulty', etc.)",
            "- 'imageUrl' (not 'image_url', 'image', etc.)",
            "- 'marks' (not 'score', 'points', etc.)",
            "- 'options' (only for MCQS type)",
            "",
            "Each question must match EXACTLY this JSON structure:",
            '''
[
  {
    "text": "What is the atomic number of Hydrogen?",
    "answer": "1",
    "Type": "MCQS",
    "Level": "EASY",
    "imageUrl": null,
    "marks": 1,
    "options": [
      {"option_text": "1", "is_correct": true},
      {"option_text": "2", "is_correct": false},
      {"option_text": "8", "is_correct": false},
      {"option_text": "0", "is_correct": false}
    ]
  },
  {
    "text": "Explain the process of nuclear fission.",
    "answer": "Nuclear fission is the process in which a heavy nucleus splits into two lighter nuclei, releasing energy.",
    "Type": "LONG",
    "Level": "MEDIUM",
    "imageUrl": null,
    "marks": 5
  }
]
            ''',
            "FIELD REQUIREMENTS:",
            "- 'text': The question text (required, non-empty string)",
            "- 'answer': The correct answer or solution (required, non-empty string)",
            "- 'Type': Must be exactly 'MCQS', 'SHORT', or 'LONG' (required)",
            "- 'Level': Must be exactly 'EASY', 'MEDIUM', or 'HARD' (required)",
            "- 'imageUrl': Always null for text questions (required field)",
            "- 'marks': Integer value, typically 1 for MCQS, 2-5 for others (required)",
            "- 'options': Array of option objects, ONLY for MCQS type questions",
            "",
            "For MCQS: Always include exactly 4 options with these exact fields:",
            "  - 'option_text': The option text (string, required) - NOT 'text', 'option', or 'choice'",
            "  - 'is_correct': Boolean true/false (required, only one should be true) - NOT 'correct' or 'answer'",
            "For SHORT/LONG: Do NOT include 'options' field at all.",
            "",
            "CRITICAL OPTION VALIDATION:",
            "- Each option MUST be a JSON object/dictionary with exactly 2 fields",
            "- Each option MUST have 'option_text' field (exact spelling, string value)",
            "- Each option MUST have 'is_correct' field (exact spelling, boolean value)",
            "- Exactly ONE option should have 'is_correct': true",
            "- All other options should have 'is_correct': false",
            "- DO NOT use arrays of strings like ['A', 'B', 'C', 'D']",
            "- DO NOT use any other format except the exact structure shown in the example",
            "",
            "CRITICAL RESPONSE FORMAT:",
            "- Return ONLY a valid JSON array starting with [ and ending with ]",
            "- Do NOT include any explanatory text, markdown, or code blocks",
            "- Do NOT wrap in ```json``` markers",
            "- Do NOT add extra fields beyond those specified",
            "- Do NOT use alternative field names",
            "",
            "COMMON MISTAKES TO AVOID:",
            "- DO NOT use string arrays for options: ['A', 'B', 'C', 'D']",
            "- DO NOT use numbered options: ['1. Answer A', '2. Answer B']",
            "- DO NOT use simple strings for options",
            "- ALWAYS use the exact object structure shown in the example",
            "",
            "Questions must be original, educationally appropriate, and test understanding of the topic."
        ])
    prompt = " ".join(prompt_parts)

    payload = {
        "contents": [
            {
                "parts": [
                    {
                        "text": prompt
                    }
                ]
            }
        ]
    }

    # Step 3: Make async HTTP request with proper error handling
    try:
        async with httpx.AsyncClient(timeout=60.0) as client:
            response = await client.post(url, json=payload, headers={"Content-Type": "application/json"})

            # Handle different HTTP status codes gracefully
            if response.status_code == 503:
                return {
                    "status": "error",
                    "message": "AI service is temporarily unavailable. Please try again in a few minutes.",
                    "questions": [],
                    "context_available": context_available,
                    "error_code": "SERVICE_UNAVAILABLE"
                }
            elif response.status_code == 429:
                return {
                    "status": "error",
                    "message": "AI service rate limit exceeded. Please try again later.",
                    "questions": [],
                    "context_available": context_available,
                    "error_code": "RATE_LIMIT_EXCEEDED"
                }
            elif response.status_code >= 400:
                return {
                    "status": "error",
                    "message": f"AI service error (HTTP {response.status_code}). Please try again later.",
                    "questions": [],
                    "context_available": context_available,
                    "error_code": "API_ERROR"
                }

            gemini_result = response.json()

    except httpx.TimeoutException:
        return {
            "status": "error",
            "message": "AI service request timed out. Please try again.",
            "questions": [],
            "context_available": context_available,
            "error_code": "TIMEOUT"
        }
    except httpx.RequestError as e:
        return {
            "status": "error",
            "message": "Failed to connect to AI service. Please check your internet connection and try again.",
            "questions": [],
            "context_available": context_available,
            "error_code": "CONNECTION_ERROR"
        }
    except Exception as e:
        return {
            "status": "error",
            "message": f"Unexpected error while calling AI service: {str(e)}",
            "questions": [],
            "context_available": context_available,
            "error_code": "UNKNOWN_ERROR"
        }

    # Step 4: Store prompt and answer in Training_data collection
    from config.mongodb import get_mongodb
    db = await get_mongodb()
    training_collection = db["Training_data"]

    try:
        parsed_questions = extract_questions_from_gemini_response(gemini_result)

        # Additional validation: Check that all questions have required fields
        for i, question in enumerate(parsed_questions):
            required_fields = ['text', 'answer', 'Type', 'Level', 'marks']
            missing_fields = [field for field in required_fields if field not in question or not question[field]]

            if missing_fields:
                raise ValueError(f"Question {i+1} missing required fields: {missing_fields}")

            # Validate MCQS have options
            if question['Type'] == 'MCQS' and ('options' not in question or not question['options']):
                raise ValueError(f"Question {i+1} is MCQS but missing options array")

    except ValueError as e:
        # Log the problematic response for debugging
        response_text = ""
        try:
            response_text = gemini_result["candidates"][0]["content"]["parts"][0]["text"][:1000]
        except:
            response_text = str(gemini_result)[:1000]

        print(f"AI Response Parsing Error: {str(e)}")
        print(f"Response preview: {response_text}")

        # Store the failed attempt for debugging
        await training_collection.insert_one({
            "prompt": prompt,
            "llm_answer": gemini_result,
            "parsed_questions": None,
            "error": str(e),
            "response_preview": response_text,
            "meta": {
                "class": class_,
                "subject": subject,
                "chapter": chapter,
                "topic": topic,
                "subtopic": subtopic,
                "no_of_questions": no_of_questions,
                "no_of_easy": no_of_easy,
                "no_of_medium": no_of_medium,
                "no_of_hard": no_of_hard,
                "context_available": context_available,
            }
        })
        return {
            "status": "error",
            "message": f"Failed to parse AI response: {str(e)}",
            "questions": [],
            "context_available": context_available
        }

    await training_collection.insert_one({
        "prompt": prompt,
        "llm_answer": gemini_result,
        "parsed_questions": parsed_questions,
        "meta": {
            "class": class_,
            "subject": subject,
            "chapter": chapter,
            "topic": topic,
            "subtopic": subtopic,
            "no_of_questions": no_of_questions,
            "no_of_easy": no_of_easy,
            "no_of_medium": no_of_medium,
            "no_of_hard": no_of_hard,
            "context_available": context_available,
        }
    })

    return {
        "status": "generated",
        "questions": parsed_questions,
        "context_available": context_available,
        "message": "Questions generated successfully"
    }

async def enhanced_ai_question_generation(
    db: Session,
    class_: str,
    subject: str,
    chapter: str,
    no_of_questions: int = 3,
    topic: Optional[str] = None,
    subtopic: Optional[str] = None,
    no_of_easy: Optional[int] = None,
    no_of_medium: Optional[int] = None,
    no_of_hard: Optional[int] = None
):
    """Enhanced AI question generation with automatic context detection and difficulty distribution"""
    # Always try to generate, automatically use context if available
    result = await call_gemini_with_ai_data(
        class_=class_,
        subject=subject,
        chapter=chapter,
        no_of_questions=no_of_questions,
        topic=topic,
        subtopic=subtopic,
        no_of_easy=no_of_easy,
        no_of_medium=no_of_medium,
        no_of_hard=no_of_hard
    )

    # If generation was successful, add the required curriculum IDs to each question
    if result.get("status") == "generated" and "questions" in result:
        try:
            # Validate that questions is a list of dictionaries
            if not isinstance(result["questions"], list):
                return {
                    "status": "error",
                    "message": "Generated questions is not a list",
                    "questions": []
                }

            for i, question in enumerate(result["questions"]):
                if not isinstance(question, dict):
                    return {
                        "status": "error",
                        "message": f"Question at index {i} is not a dictionary: {type(question)}",
                        "questions": []
                    }

            # Resolve curriculum IDs
            curriculum_ids = resolve_curriculum_ids(db, class_, subject, chapter, topic, subtopic)

            # Add the required fields to each question
            for question in result["questions"]:
                question["class_id"] = curriculum_ids["class_id"]
                question["subject_id"] = curriculum_ids["subject_id"]
                question["chapter_id"] = curriculum_ids["chapter_id"]
                question["topic_id"] = curriculum_ids["topic_id"]
                question["subtopic_id"] = curriculum_ids["subtopic_id"]

        except HTTPException as e:
            # If curriculum IDs can't be resolved, return error
            return {
                "status": "error",
                "message": f"Failed to resolve curriculum data: {e.detail}",
                "questions": []
            }
        except Exception as e:
            # Handle any other errors during question processing
            return {
                "status": "error",
                "message": f"Failed to process generated questions: {str(e)}",
                "questions": []
            }

    return result


