"""
Competition Exam Attempt Routes

This module provides API endpoints for students to attempt exams
linked to competition events.
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from typing import Optional
from uuid import UUID

from config.session import get_db
from config.deps import oauth2_scheme, get_current_user
from config.permission import require_type
from Models.users import User
from Schemas.Events.CompetitionExam import (
    CompetitionExamInfo, CompetitionExamAttemptRequest, CompetitionExamAttemptResponse,
    CompetitionExamForStudent, CompetitionExamQuestions, CompetitionExamStatus,
    CompetitionResults
)
from Cruds.Events.CompetitionExam import (
    get_competition_exam_info, register_for_competition_exam,
    start_competition_exam_attempt, get_competition_exam_questions,
    get_competition_exam_status, get_competition_results
)

router = APIRouter()


# ==================== COMPETITION EXAM INFORMATION ====================

@router.get("/{event_id}/exam/info", response_model=CompetitionExamForStudent)
def get_competition_exam_info_endpoint(
    event_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    current_user: User = Depends(require_type(["student"]))
):
    """
    Get competition exam information for a student.
    
    Returns detailed information about the competition exam including:
    - Competition and exam details
    - Registration status
    - Timing information
    - Student's current status
    
    **Authentication:** Requires student role.
    """
    return get_competition_exam_info(db, event_id, current_user.id)


@router.get("/{event_id}/exam/status", response_model=CompetitionExamStatus)
def get_competition_exam_status_endpoint(
    event_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    current_user: User = Depends(require_type(["student"]))
):
    """
    Get competition exam attempt status for the current student.
    
    Returns the student's current status for the competition exam:
    - Registration status
    - Attempt status
    - Timing information
    - Score if completed
    
    **Authentication:** Requires student role.
    """
    return get_competition_exam_status(db, event_id, current_user.id)


# ==================== COMPETITION EXAM REGISTRATION ====================

@router.post("/{event_id}/exam/register")
def register_for_competition_exam_endpoint(
    event_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    current_user: User = Depends(require_type(["student"]))
):
    """
    Register a student for a competition exam.
    
    Creates an exam attempt record for the student, allowing them
    to attempt the competition exam when it becomes active.
    
    **Requirements:**
    - Competition must exist and be linked to an exam
    - Registration must be open
    - Competition must not have started
    - Student must not already be registered
    - Competition must not be full (if max_attendees is set)
    
    **Authentication:** Requires student role.
    """
    return register_for_competition_exam(db, event_id, current_user.id)


# ==================== COMPETITION EXAM ATTEMPTS ====================

@router.post("/{event_id}/exam/start", response_model=CompetitionExamAttemptResponse)
def start_competition_exam_attempt_endpoint(
    event_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    current_user: User = Depends(require_type(["student"]))
):
    """
    Start a competition exam attempt.
    
    Initiates the exam session for a registered student. This creates
    an active exam session and marks the attempt as started.
    
    **Requirements:**
    - Student must be registered for the competition
    - Competition must be currently active (within start/end time)
    - Student must not have already completed the exam
    
    **Returns:**
    - Exam attempt ID
    - Exam session ID for tracking
    - Basic exam information
    
    **Authentication:** Requires student role.
    """
    return start_competition_exam_attempt(db, event_id, current_user.id)


@router.get("/{event_id}/exam/questions", response_model=CompetitionExamQuestions)
def get_competition_exam_questions_endpoint(
    event_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    current_user: User = Depends(require_type(["student"]))
):
    """
    Get competition exam questions for student attempt.
    
    Returns the exam questions for an active attempt. The student
    must have an active exam session to access the questions.
    
    **Requirements:**
    - Student must have an active exam attempt
    - Exam session must be active
    - Competition must be currently active
    
    **Returns:**
    - All exam questions in student format (without correct answers)
    - Attempt and session IDs
    - Time remaining information
    
    **Authentication:** Requires student role.
    """
    return get_competition_exam_questions(db, event_id, current_user.id)


# ==================== COMPETITION RESULTS ====================

@router.get("/{event_id}/results", response_model=CompetitionResults)
def get_competition_results_endpoint(
    event_id: UUID,
    limit: int = Query(10, ge=1, le=100, description="Number of top results to return"),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    current_user: User = Depends(require_type(["student", "teacher", "institute"]))
):
    """
    Get competition results and leaderboard.
    
    Returns competition statistics and leaderboard. Students can see
    their own results and the top performers. Teachers and institutes
    can see full results.
    
    **Query Parameters:**
    - limit: Number of top results to return (1-100, default: 10)
    
    **Returns:**
    - Competition statistics (total participants, average score, etc.)
    - Leaderboard with top performers
    - Current user's result (if they participated)
    
    **Authentication:** Requires student, teacher, or institute role.
    """
    # Students can only see their own results in the leaderboard
    student_id = current_user.id if current_user.user_type.value == "student" else None
    return get_competition_results(db, event_id, student_id, limit)


# ==================== ADMIN ENDPOINTS ====================

@router.get("/{event_id}/admin/results", response_model=CompetitionResults)
def get_competition_admin_results_endpoint(
    event_id: UUID,
    limit: int = Query(50, ge=1, le=500, description="Number of results to return"),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    current_user: User = Depends(require_type(["teacher", "institute", "admin"]))
):
    """
    Get comprehensive competition results for administrators.
    
    Returns detailed competition results with higher limits for
    teachers, institutes, and administrators.
    
    **Query Parameters:**
    - limit: Number of results to return (1-500, default: 50)
    
    **Returns:**
    - Complete competition statistics
    - Extended leaderboard
    - All participant results
    
    **Authentication:** Requires teacher, institute, or admin role.
    """
    return get_competition_results(db, event_id, None, limit)


# ==================== HELPER ENDPOINTS ====================

@router.get("/{event_id}/exam/validate")
def validate_competition_exam_endpoint(
    event_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    current_user: User = Depends(require_type(["student", "teacher", "institute"]))
):
    """
    Validate if an event is a valid competition with an exam.
    
    Quick validation endpoint to check if an event ID corresponds
    to a valid competition that has an exam linked to it.
    
    **Returns:**
    - success: Boolean indicating if it's a valid competition exam
    - message: Description of the validation result
    - exam_id: ID of the linked exam (if valid)
    
    **Authentication:** Requires student, teacher, or institute role.
    """
    try:
        from Models.Events import Event
        from sqlalchemy import or_

        # Check for competition with exam in either field
        competition = db.query(Event).filter(
            Event.id == event_id,
            Event.is_competition == True,
            or_(
                Event.competition_exam_id.isnot(None),
                Event.exam_id.isnot(None)
            )
        ).first()

        if not competition:
            return {
                "success": False,
                "message": "Event is not a valid competition or not linked to an exam",
                "exam_id": None
            }

        # Get the exam ID from whichever field has it
        exam_id = competition.competition_exam_id or competition.exam_id

        return {
            "success": True,
            "message": "Valid competition exam found",
            "exam_id": exam_id,
            "event_title": competition.title,
            "field_used": "competition_exam_id" if competition.competition_exam_id else "exam_id"
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error validating competition exam: {str(e)}"
        )
