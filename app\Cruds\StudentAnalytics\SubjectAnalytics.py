"""
Subject-wise Analytics CRUD Operations for EduFair Platform

This module contains CRUD operations for subject-wise student performance analytics.
"""

import uuid
from datetime import datetime, timedelta, timezone
from typing import List, Dict, Any, Optional
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import func, desc, asc, and_, or_, case
from fastapi import HTTPException, status

# Import Models
from Models.users import User, UserTypeEnum, Subject
from Models.Classroom import Classroom, StudentClassroom
from Models.Tasks import Task, TaskStudents, TaskStatus
from Models.Exam import Exam, StudentExamAssignment, StudentExamAttempt, StudentExamAIResult, StudentExamTeacherResult
from Models.Questions import Question
from Models.Chapter import Chapter
from Models.Class import ClassNumber

# Import Schemas
from Schemas.StudentAnalytics import (
    SubjectAnalyticsRequest, SubjectAnalyticsResponse, SubjectPerformanceDetail,
    AnalyticsTimeRange, PerformanceTrend, RankingInfo
)

# Import caching utilities (temporarily disabled)
# from utils.AnalyticsCache import cache_analytics, get_cache_ttl


# @cache_analytics("subject_analytics", ttl_minutes=get_cache_ttl("subject_analytics"))
def get_subject_analytics(
    db: Session,
    student_id: uuid.UUID,
    request: SubjectAnalyticsRequest
) -> SubjectAnalyticsResponse:
    """
    Get comprehensive subject-wise analytics for a student
    """
    # Verify student exists
    student = db.query(User).filter(
        User.id == student_id,
        User.user_type == UserTypeEnum.student
    ).first()
    
    if not student:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Student not found"
        )
    
    # Get subjects to analyze
    if request.subject_ids:
        subjects = db.query(Subject).filter(Subject.id.in_(request.subject_ids)).all()
    else:
        # Get all subjects the student has interacted with
        subjects = _get_student_subjects(db, student_id, request.time_range)
    
    # Analyze each subject
    subject_performances = []
    total_study_hours = 0.0
    overall_scores = []
    
    for subject in subjects:
        performance = _analyze_subject_performance(
            db, student_id, subject, request.time_range, request.include_chapter_breakdown
        )
        subject_performances.append(performance)
        total_study_hours += performance.study_time_hours
        if performance.average_score > 0:
            overall_scores.append(performance.average_score)
    
    # Calculate overall metrics
    overall_gpa = sum(overall_scores) / len(overall_scores) if overall_scores else 0.0
    strongest_subject = max(subject_performances, key=lambda x: x.average_score).subject_name if subject_performances else None
    weakest_subject = min(subject_performances, key=lambda x: x.average_score).subject_name if subject_performances else None
    
    return SubjectAnalyticsResponse(
        student_id=student_id,
        time_range=request.time_range,
        subjects=subject_performances,
        overall_gpa=overall_gpa,
        strongest_subject=strongest_subject,
        weakest_subject=weakest_subject,
        total_study_hours=total_study_hours,
        last_updated=datetime.now(timezone.utc)
    )


def _get_student_subjects(db: Session, student_id: uuid.UUID, time_range: AnalyticsTimeRange) -> List[Subject]:
    """Get all subjects the student has interacted with in the given time range"""
    
    # Get subjects from exams using the many-to-many relationship
    from Models.Exam import exam_question_association
    exam_subjects = db.query(Subject).join(
        Question, Subject.id == Question.subject_id
    ).join(
        exam_question_association, Question.id == exam_question_association.c.question_id
    ).join(
        Exam, exam_question_association.c.exam_id == Exam.id
    ).join(
        StudentExamAttempt, Exam.id == StudentExamAttempt.exam_id
    ).filter(
        StudentExamAttempt.student_id == student_id,
        StudentExamAttempt.started_at >= time_range.start_date,
        StudentExamAttempt.started_at <= time_range.end_date
    ).distinct().all()
    
    # Get subjects from tasks
    task_subjects = db.query(Subject).join(
        Task, Subject.id == Task.subject_id
    ).join(
        TaskStudents, Task.id == TaskStudents.task_id
    ).filter(
        TaskStudents.student_id == student_id,
        Task.created_at >= time_range.start_date,
        Task.created_at <= time_range.end_date
    ).distinct().all()
    
    # Combine and deduplicate
    all_subjects = {subject.id: subject for subject in exam_subjects + task_subjects}
    return list(all_subjects.values())


def _analyze_subject_performance(
    db: Session,
    student_id: uuid.UUID,
    subject: Subject,
    time_range: AnalyticsTimeRange,
    include_chapter_breakdown: bool
) -> SubjectPerformanceDetail:
    """Analyze performance for a specific subject"""
    
    # Get exam performance
    exam_data = _get_subject_exam_performance(db, student_id, subject.id, time_range)
    
    # Get assignment performance
    assignment_data = _get_subject_assignment_performance(db, student_id, subject.id, time_range)
    
    # Get ranking information
    ranking = _calculate_subject_ranking(db, student_id, subject.id, time_range)
    
    # Get performance trends
    trends = []
    if time_range.period_type in ['monthly', 'quarterly', 'yearly']:
        trends = _calculate_subject_trends(db, student_id, subject.id, time_range)
    
    # Get chapter performance if requested
    chapter_performance = {}
    strongest_chapters = []
    weakest_chapters = []
    if include_chapter_breakdown:
        chapter_data = _get_chapter_performance(db, student_id, subject.id, time_range)
        chapter_performance = chapter_data['performance']
        strongest_chapters = chapter_data['strongest']
        weakest_chapters = chapter_data['weakest']
    
    # Calculate improvement areas and recommendations
    improvement_areas, recommendations = _generate_subject_recommendations(
        exam_data, assignment_data, chapter_performance
    )
    
    # Combine exam and assignment data
    total_exams = exam_data['total_exams']
    total_assignments = assignment_data['total_assignments']
    
    # Calculate weighted average (exams 70%, assignments 30%)
    exam_weight = 0.7
    assignment_weight = 0.3
    
    if exam_data['average_score'] > 0 and assignment_data['average_score'] > 0:
        average_score = (exam_data['average_score'] * exam_weight + 
                        assignment_data['average_score'] * assignment_weight)
    elif exam_data['average_score'] > 0:
        average_score = exam_data['average_score']
    elif assignment_data['average_score'] > 0:
        average_score = assignment_data['average_score']
    else:
        average_score = 0.0
    
    return SubjectPerformanceDetail(
        subject_id=subject.id,
        subject_name=subject.name,
        total_exams=total_exams,
        total_assignments=total_assignments,
        average_score=average_score,
        highest_score=max(exam_data['highest_score'], assignment_data['highest_score']),
        lowest_score=min(exam_data['lowest_score'], assignment_data['lowest_score']) if exam_data['lowest_score'] > 0 and assignment_data['lowest_score'] > 0 else max(exam_data['lowest_score'], assignment_data['lowest_score']),
        median_score=exam_data['median_score'],  # Primarily from exams
        total_marks_obtained=exam_data['total_marks_obtained'] + assignment_data['total_marks_obtained'],
        total_marks_possible=exam_data['total_marks_possible'] + assignment_data['total_marks_possible'],
        success_rate=exam_data['success_rate'],
        class_average=exam_data['class_average'],
        performance_vs_class=average_score - exam_data['class_average'],
        ranking=ranking,
        performance_trend=trends,
        improvement_rate=_calculate_improvement_rate(trends),
        chapter_performance=chapter_performance,
        strongest_chapters=strongest_chapters,
        weakest_chapters=weakest_chapters,
        study_time_hours=assignment_data['study_time_hours'],
        assignment_completion_rate=assignment_data['completion_rate'],
        on_time_submission_rate=assignment_data['on_time_rate'],
        improvement_areas=improvement_areas,
        recommended_actions=recommendations
    )


def _get_subject_exam_performance(
    db: Session, 
    student_id: uuid.UUID, 
    subject_id: uuid.UUID, 
    time_range: AnalyticsTimeRange
) -> Dict[str, Any]:
    """Get exam performance data for a subject"""
    
    # Get exam attempts for this subject using the many-to-many relationship
    from Models.Exam import exam_question_association
    attempts = db.query(StudentExamAttempt).join(
        Exam, StudentExamAttempt.exam_id == Exam.id
    ).join(
        exam_question_association, exam_question_association.c.exam_id == Exam.id
    ).join(
        Question, exam_question_association.c.question_id == Question.id
    ).filter(
        Question.subject_id == subject_id,
        StudentExamAttempt.student_id == student_id,
        StudentExamAttempt.started_at >= time_range.start_date,
        StudentExamAttempt.started_at <= time_range.end_date,
        StudentExamAttempt.completed_at.isnot(None)
    ).all()
    
    if not attempts:
        return {
            'total_exams': 0,
            'average_score': 0.0,
            'highest_score': 0.0,
            'lowest_score': 0.0,
            'median_score': 0.0,
            'total_marks_obtained': 0,
            'total_marks_possible': 0,
            'success_rate': 0.0,
            'class_average': 0.0
        }
    
    # Calculate scores for each attempt
    scores = []
    total_obtained = 0
    total_possible = 0
    passing_count = 0
    
    for attempt in attempts:
        # Get AI or teacher results
        ai_result = db.query(StudentExamAIResult).filter(
            StudentExamAIResult.attempt_id == attempt.id
        ).first()
        
        teacher_result = db.query(StudentExamTeacherResult).filter(
            StudentExamTeacherResult.attempt_id == attempt.id
        ).first()
        
        # Prefer teacher results over AI results
        if teacher_result:
            obtained = teacher_result.total_score or 0
            possible = teacher_result.total_marks or 1
        elif ai_result:
            obtained = ai_result.total_score or 0
            possible = ai_result.total_marks or 1
        else:
            continue
        
        score_percentage = (obtained / possible * 100) if possible > 0 else 0
        scores.append(score_percentage)
        total_obtained += obtained
        total_possible += possible
        
        if score_percentage >= 60:  # Assuming 60% is passing
            passing_count += 1
    
    if not scores:
        return {
            'total_exams': len(attempts),
            'average_score': 0.0,
            'highest_score': 0.0,
            'lowest_score': 0.0,
            'median_score': 0.0,
            'total_marks_obtained': 0,
            'total_marks_possible': 0,
            'success_rate': 0.0,
            'class_average': 0.0
        }
    
    # Calculate statistics
    scores.sort()
    average_score = sum(scores) / len(scores)
    median_score = scores[len(scores) // 2] if scores else 0.0
    success_rate = (passing_count / len(attempts) * 100) if attempts else 0.0
    
    # Calculate class average from all students in the same classrooms
    # Get all students in the same classrooms as this student
    student_classrooms = db.query(StudentClassroom.classroom_id).filter(
        StudentClassroom.student_id == student_id
    ).subquery()

    class_students = db.query(StudentClassroom.student_id).filter(
        StudentClassroom.classroom_id.in_(student_classrooms)
    ).all()

    class_student_ids = [s[0] for s in class_students]

    # Get exam attempts for all class students for this subject
    class_attempts = db.query(StudentExamAttempt).join(
        Exam, StudentExamAttempt.exam_id == Exam.id
    ).join(
        exam_question_association, exam_question_association.c.exam_id == Exam.id
    ).join(
        Question, exam_question_association.c.question_id == Question.id
    ).filter(
        Question.subject_id == subject_id,
        StudentExamAttempt.student_id.in_(class_student_ids),
        StudentExamAttempt.started_at >= time_range.start_date,
        StudentExamAttempt.started_at <= time_range.end_date,
        StudentExamAttempt.completed_at.isnot(None)
    ).all()

    # Calculate class average
    if class_attempts:
        class_scores = []
        for attempt in class_attempts:
            # Get results (prefer teacher results over AI results)
            teacher_result = db.query(StudentExamTeacherResult).filter(
                StudentExamTeacherResult.attempt_id == attempt.id
            ).first()

            if teacher_result and teacher_result.total_score is not None:
                percentage = (teacher_result.total_score / (teacher_result.total_marks or 100)) * 100
                class_scores.append(percentage)
            else:
                ai_result = db.query(StudentExamAIResult).filter(
                    StudentExamAIResult.attempt_id == attempt.id
                ).first()

                if ai_result and ai_result.total_score is not None:
                    percentage = (ai_result.total_score / (ai_result.total_marks or 100)) * 100
                    class_scores.append(percentage)

        class_average = sum(class_scores) / len(class_scores) if class_scores else average_score
    else:
        class_average = average_score
    
    return {
        'total_exams': len(attempts),
        'average_score': average_score,
        'highest_score': max(scores),
        'lowest_score': min(scores),
        'median_score': median_score,
        'total_marks_obtained': total_obtained,
        'total_marks_possible': total_possible,
        'success_rate': success_rate,
        'class_average': class_average
    }


def _get_subject_assignment_performance(
    db: Session,
    student_id: uuid.UUID,
    subject_id: uuid.UUID,
    time_range: AnalyticsTimeRange
) -> Dict[str, Any]:
    """Get assignment performance data for a subject"""

    # Get assignments for this subject
    assignments = db.query(TaskStudents).join(
        Task, TaskStudents.task_id == Task.id
    ).filter(
        Task.subject_id == subject_id,
        TaskStudents.student_id == student_id,
        Task.created_at >= time_range.start_date,
        Task.created_at <= time_range.end_date
    ).all()

    if not assignments:
        return {
            'total_assignments': 0,
            'average_score': 0.0,
            'highest_score': 0.0,
            'lowest_score': 0.0,
            'total_marks_obtained': 0,
            'total_marks_possible': 0,
            'completion_rate': 0.0,
            'on_time_rate': 0.0,
            'study_time_hours': 0.0
        }

    scores = []
    total_obtained = 0
    total_possible = 0
    completed_count = 0
    on_time_count = 0
    total_study_time = 0.0

    for assignment in assignments:
        task = assignment.task

        if assignment.grade is not None:
            scores.append(assignment.grade)
            total_obtained += assignment.grade
            total_possible += 100  # Assuming grades are out of 100
            completed_count += 1

            # Check if submitted on time
            if (assignment.submission_date and task.deadline and
                assignment.submission_date <= task.deadline):
                on_time_count += 1

        # Estimate study time (placeholder calculation)
        if assignment.submission_date and task.created_at:
            time_diff = assignment.submission_date - task.created_at
            total_study_time += time_diff.total_seconds() / 3600  # Convert to hours

    completion_rate = (completed_count / len(assignments) * 100) if assignments else 0.0
    on_time_rate = (on_time_count / completed_count * 100) if completed_count > 0 else 0.0
    average_score = sum(scores) / len(scores) if scores else 0.0

    return {
        'total_assignments': len(assignments),
        'average_score': average_score,
        'highest_score': max(scores) if scores else 0.0,
        'lowest_score': min(scores) if scores else 0.0,
        'total_marks_obtained': total_obtained,
        'total_marks_possible': total_possible,
        'completion_rate': completion_rate,
        'on_time_rate': on_time_rate,
        'study_time_hours': total_study_time
    }


def _calculate_subject_ranking(
    db: Session,
    student_id: uuid.UUID,
    subject_id: uuid.UUID,
    time_range: AnalyticsTimeRange
) -> RankingInfo:
    """Calculate student's ranking in a subject"""

    # Get all students' performance in this subject (simplified calculation)
    # In a real implementation, this would be more complex

    # Since we don't have detailed ranking implementation,
    # return default ranking info
    return RankingInfo(
        current_rank=1,
        total_students=1,
        percentile=0.0,
        rank_change=0,
        rank_trend="stable"
    )


def _calculate_subject_trends(
    db: Session,
    student_id: uuid.UUID,
    subject_id: uuid.UUID,
    time_range: AnalyticsTimeRange
) -> List[PerformanceTrend]:
    """Calculate performance trends for a subject"""

    # Since we don't have time-series analysis implemented,
    # return empty list for now
    return []


def _get_chapter_performance(
    db: Session,
    student_id: uuid.UUID,
    subject_id: uuid.UUID,
    time_range: AnalyticsTimeRange
) -> Dict[str, Any]:
    """Get chapter-wise performance breakdown"""

    # Get chapters for this subject
    chapters = db.query(Chapter).filter(Chapter.subject_id == subject_id).all()

    chapter_performance = {}
    chapter_scores = []

    for chapter in chapters:
        # Get performance in this chapter (simplified)
        # This would involve analyzing questions from this chapter
        # For now, skip chapter analysis since it's not implemented
        score = 0.0
        chapter_performance[chapter.name] = score
        chapter_scores.append((chapter.name, score))

    # Sort by performance
    chapter_scores.sort(key=lambda x: x[1], reverse=True)

    strongest_chapters = [name for name, score in chapter_scores[:3]]
    weakest_chapters = [name for name, score in chapter_scores[-3:]]

    return {
        'performance': chapter_performance,
        'strongest': strongest_chapters,
        'weakest': weakest_chapters
    }


def _generate_subject_recommendations(
    exam_data: Dict[str, Any],
    assignment_data: Dict[str, Any],
    chapter_performance: Dict[str, float]
) -> tuple[List[str], List[str]]:
    """Generate improvement areas and recommendations"""

    improvement_areas = []
    recommendations = []

    # Analyze exam performance
    if exam_data['average_score'] < 70:
        improvement_areas.append("Exam Performance")
        recommendations.append("Focus on exam preparation and practice tests")

    # Analyze assignment performance
    if assignment_data['completion_rate'] < 80:
        improvement_areas.append("Assignment Completion")
        recommendations.append("Improve time management for assignments")

    if assignment_data['on_time_rate'] < 70:
        improvement_areas.append("Time Management")
        recommendations.append("Create a study schedule and set reminders")

    # Analyze chapter performance
    weak_chapters = [name for name, score in chapter_performance.items() if score < 60]
    if weak_chapters:
        improvement_areas.append("Chapter Understanding")
        recommendations.append(f"Focus on weak chapters: {', '.join(weak_chapters[:3])}")

    return improvement_areas, recommendations


def _calculate_improvement_rate(trends: List[PerformanceTrend]) -> float:
    """Calculate monthly improvement rate from trends"""

    if len(trends) < 2:
        return 0.0

    # Simple calculation based on trend changes
    total_change = sum(trend.change_percentage or 0 for trend in trends)
    return total_change / len(trends)
