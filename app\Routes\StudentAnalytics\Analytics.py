"""
Student Analytics API Routes for EduFair Platform

This module contains FastAPI routes for comprehensive student analytics including
subject-wise, class/grade-wise, classroom-wise, and competition-wise analytics.
"""

import uuid
from datetime import datetime, timezone
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session

# Import dependencies
from config.deps import get_current_user, get_db
from config.security import oauth2_scheme

# Import CRUD operations
from Cruds.StudentAnalytics.SubjectAnalytics import get_subject_analytics
from Cruds.StudentAnalytics.ClassGradeAnalytics import get_class_grade_analytics
from Cruds.StudentAnalytics.ClassroomAnalytics import get_classroom_analytics
from Cruds.StudentAnalytics.CompetitionAnalytics import get_competition_analytics

# Import schemas
from Schemas.StudentAnalytics import (
    # Request schemas
    SubjectAnalyticsRequest, ClassGradeAnalyticsRequest, 
    ClassroomAnalyticsRequest, CompetitionAnalyticsRequest,
    AnalyticsTimeRange, StudentComprehensiveAnalytics,
    
    # Response schemas
    SubjectAnalyticsResponse, ClassGradeAnalyticsResponse,
    ClassroomAnalyticsResponse, CompetitionAnalyticsResponse
)

# Import models
from Models.users import User, UserTypeEnum

# Import logging
from config.logging import get_logger

logger = get_logger(__name__)

router = APIRouter()


@router.get("/subject", response_model=SubjectAnalyticsResponse)
async def get_student_subject_analytics(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    start_date: datetime = Query(..., description="Start date for analytics period"),
    end_date: datetime = Query(..., description="End date for analytics period"),
    period_type: str = Query("monthly", description="Period type: daily, weekly, monthly, quarterly, yearly"),
    subject_ids: Optional[List[str]] = Query(None, description="Optional list of subject IDs to analyze"),
    include_trends: bool = Query(True, description="Include performance trends"),
    include_comparisons: bool = Query(True, description="Include peer comparisons"),
    include_recommendations: bool = Query(True, description="Include improvement recommendations"),
    include_chapter_breakdown: bool = Query(True, description="Include chapter-wise breakdown")
):
    """
    Get comprehensive subject-wise analytics for the current student
    
    This endpoint provides detailed analytics including:
    - Performance metrics for each subject
    - Comparative analysis with class averages
    - Ranking information and trends
    - Chapter-wise performance breakdown
    - Personalized recommendations for improvement
    """
    try:
        # Verify user is a student
        if current_user.user_type != UserTypeEnum.student:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Only students can access student analytics"
            )
        
        # Convert subject IDs to UUIDs if provided
        subject_uuids = None
        if subject_ids:
            try:
                subject_uuids = [uuid.UUID(sid) for sid in subject_ids]
            except ValueError:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Invalid subject ID format"
                )
        
        # Create request object
        request = SubjectAnalyticsRequest(
            time_range=AnalyticsTimeRange(
                start_date=start_date,
                end_date=end_date,
                period_type=period_type
            ),
            subject_ids=subject_uuids,
            include_trends=include_trends,
            include_comparisons=include_comparisons,
            include_recommendations=include_recommendations,
            include_chapter_breakdown=include_chapter_breakdown
        )
        
        # Get analytics
        analytics = get_subject_analytics(db, current_user.id, request)
        
        logger.info(f"Subject analytics retrieved for student {current_user.id}")
        return analytics
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving subject analytics for student {current_user.id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve subject analytics"
        )


@router.get("/class-grade", response_model=ClassGradeAnalyticsResponse)
async def get_student_class_grade_analytics(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    start_date: datetime = Query(..., description="Start date for analytics period"),
    end_date: datetime = Query(..., description="End date for analytics period"),
    period_type: str = Query("monthly", description="Period type: daily, weekly, monthly, quarterly, yearly"),
    include_trends: bool = Query(True, description="Include performance trends"),
    include_comparisons: bool = Query(True, description="Include peer comparisons"),
    include_recommendations: bool = Query(True, description="Include improvement recommendations"),
    include_peer_comparison: bool = Query(True, description="Include detailed peer comparison"),
    include_rank_history: bool = Query(True, description="Include ranking history")
):
    """
    Get comprehensive class and grade-level analytics for the current student
    
    This endpoint provides detailed analytics including:
    - Performance metrics within each classroom
    - Grade-level ranking and percentile information
    - Peer comparison and similar performers
    - Historical ranking trends
    - Academic standing assessment
    """
    try:
        # Verify user is a student
        if current_user.user_type != UserTypeEnum.student:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Only students can access student analytics"
            )
        
        # Create request object
        request = ClassGradeAnalyticsRequest(
            time_range=AnalyticsTimeRange(
                start_date=start_date,
                end_date=end_date,
                period_type=period_type
            ),
            include_trends=include_trends,
            include_comparisons=include_comparisons,
            include_recommendations=include_recommendations,
            include_peer_comparison=include_peer_comparison,
            include_rank_history=include_rank_history
        )
        
        # Get analytics
        analytics = get_class_grade_analytics(db, current_user.id, request)
        
        logger.info(f"Class/Grade analytics retrieved for student {current_user.id}")
        return analytics
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving class/grade analytics for student {current_user.id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve class/grade analytics"
        )


@router.get("/classroom", response_model=ClassroomAnalyticsResponse)
async def get_student_classroom_analytics(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    start_date: datetime = Query(..., description="Start date for analytics period"),
    end_date: datetime = Query(..., description="End date for analytics period"),
    period_type: str = Query("monthly", description="Period type: daily, weekly, monthly, quarterly, yearly"),
    classroom_ids: Optional[List[str]] = Query(None, description="Optional list of classroom IDs to analyze"),
    include_trends: bool = Query(True, description="Include performance trends"),
    include_comparisons: bool = Query(True, description="Include peer comparisons"),
    include_recommendations: bool = Query(True, description="Include improvement recommendations"),
    include_engagement_details: bool = Query(True, description="Include detailed engagement metrics")
):
    """
    Get comprehensive classroom-wise analytics for the current student
    
    This endpoint provides detailed analytics including:
    - Engagement metrics for each classroom
    - Assignment completion and submission rates
    - Teacher feedback and performance impact
    - Time spent and participation levels
    - Classroom-specific recommendations
    """
    try:
        # Verify user is a student
        if current_user.user_type != UserTypeEnum.student:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Only students can access student analytics"
            )
        
        # Convert classroom IDs to UUIDs if provided
        classroom_uuids = None
        if classroom_ids:
            try:
                classroom_uuids = [uuid.UUID(cid) for cid in classroom_ids]
            except ValueError:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Invalid classroom ID format"
                )
        
        # Create request object
        request = ClassroomAnalyticsRequest(
            time_range=AnalyticsTimeRange(
                start_date=start_date,
                end_date=end_date,
                period_type=period_type
            ),
            classroom_ids=classroom_uuids,
            include_trends=include_trends,
            include_comparisons=include_comparisons,
            include_recommendations=include_recommendations,
            include_engagement_details=include_engagement_details
        )
        
        # Get analytics
        analytics = get_classroom_analytics(db, current_user.id, request)
        
        logger.info(f"Classroom analytics retrieved for student {current_user.id}")
        return analytics
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving classroom analytics for student {current_user.id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve classroom analytics"
        )


@router.get("/competition", response_model=CompetitionAnalyticsResponse)
async def get_student_competition_analytics(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    start_date: datetime = Query(..., description="Start date for analytics period"),
    end_date: datetime = Query(..., description="End date for analytics period"),
    period_type: str = Query("monthly", description="Period type: daily, weekly, monthly, quarterly, yearly"),
    competition_types: Optional[List[str]] = Query(None, description="Optional list of competition types to filter"),
    include_trends: bool = Query(True, description="Include performance trends"),
    include_comparisons: bool = Query(True, description="Include peer comparisons"),
    include_recommendations: bool = Query(True, description="Include improvement recommendations"),
    include_upcoming: bool = Query(True, description="Include upcoming competitions"),
):
    """
    Get comprehensive competition-wise analytics for the current student
    
    This endpoint provides detailed analytics including:
    - Performance history across all competitions
    - Ranking and achievement tracking
    - Category-wise performance analysis
    - Awards and certificates earned
    - Upcoming competition recommendations
    """
    try:
        # Verify user is a student
        if current_user.user_type != UserTypeEnum.student:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Only students can access student analytics"
            )
        
        # Create request object
        request = CompetitionAnalyticsRequest(
            time_range=AnalyticsTimeRange(
                start_date=start_date,
                end_date=end_date,
                period_type=period_type
            ),
            competition_types=competition_types,
            include_trends=include_trends,
            include_comparisons=include_comparisons,
            include_recommendations=include_recommendations,
            include_upcoming=include_upcoming
        )
        
        # Get analytics
        analytics = get_competition_analytics(db, current_user.id, request)
        
        logger.info(f"Competition analytics retrieved for student {current_user.id}")
        return analytics
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving competition analytics for student {current_user.id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve competition analytics"
        )


@router.get("/comprehensive", response_model=StudentComprehensiveAnalytics)
async def get_comprehensive_student_analytics(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    start_date: datetime = Query(..., description="Start date for analytics period"),
    end_date: datetime = Query(..., description="End date for analytics period"),
    period_type: str = Query("monthly", description="Period type: daily, weekly, monthly, quarterly, yearly"),
    include_trends: bool = Query(True, description="Include performance trends"),
    include_comparisons: bool = Query(True, description="Include peer comparisons"),
    include_recommendations: bool = Query(True, description="Include improvement recommendations")
):
    """
    Get comprehensive analytics combining all aspects for the current student

    This endpoint provides a complete analytics overview including:
    - Subject-wise performance analysis
    - Class and grade-level standings
    - Classroom engagement metrics
    - Competition performance history
    - Overall insights and personalized recommendations
    - Goal tracking and achievement progress
    """
    try:
        # Verify user is a student
        if current_user.user_type != UserTypeEnum.student:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Only students can access student analytics"
            )

        # Create time range
        time_range = AnalyticsTimeRange(
            start_date=start_date,
            end_date=end_date,
            period_type=period_type
        )

        # Get all analytics components
        subject_request = SubjectAnalyticsRequest(
            time_range=time_range,
            include_trends=include_trends,
            include_comparisons=include_comparisons,
            include_recommendations=include_recommendations,
            include_chapter_breakdown=True
        )

        class_grade_request = ClassGradeAnalyticsRequest(
            time_range=time_range,
            include_trends=include_trends,
            include_comparisons=include_comparisons,
            include_recommendations=include_recommendations,
            include_peer_comparison=True,
            include_rank_history=True
        )

        classroom_request = ClassroomAnalyticsRequest(
            time_range=time_range,
            include_trends=include_trends,
            include_comparisons=include_comparisons,
            include_recommendations=include_recommendations,
            include_engagement_details=True
        )

        competition_request = CompetitionAnalyticsRequest(
            time_range=time_range,
            include_trends=include_trends,
            include_comparisons=include_comparisons,
            include_recommendations=include_recommendations,
            include_upcoming=True
        )

        # Fetch all analytics
        subject_analytics = get_subject_analytics(db, current_user.id, subject_request)
        class_grade_analytics = get_class_grade_analytics(db, current_user.id, class_grade_request)
        classroom_analytics = get_classroom_analytics(db, current_user.id, classroom_request)
        competition_analytics = get_competition_analytics(db, current_user.id, competition_request)

        # Generate overall insights
        overall_insights = _generate_overall_insights(
            subject_analytics, class_grade_analytics, classroom_analytics, competition_analytics
        )

        # Create comprehensive response
        comprehensive_analytics = StudentComprehensiveAnalytics(
            student_id=current_user.id,
            student_name=current_user.username,
            time_range=time_range,
            subject_analytics=subject_analytics,
            class_grade_analytics=class_grade_analytics,
            classroom_analytics=classroom_analytics,
            competition_analytics=competition_analytics,
            overall_performance_score=overall_insights['performance_score'],
            academic_strength_areas=overall_insights['strength_areas'],
            improvement_opportunities=overall_insights['improvement_opportunities'],
            personalized_recommendations=overall_insights['personalized_recommendations'],
            current_goals=overall_insights['current_goals'],
            achievement_progress=overall_insights['achievement_progress'],
            last_updated=datetime.now(timezone.utc)
        )

        logger.info(f"Comprehensive analytics retrieved for student {current_user.id}")
        return comprehensive_analytics

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving comprehensive analytics for student {current_user.id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve comprehensive analytics"
        )


def _generate_overall_insights(
    subject_analytics: SubjectAnalyticsResponse,
    class_grade_analytics: ClassGradeAnalyticsResponse,
    classroom_analytics: ClassroomAnalyticsResponse,
    competition_analytics: CompetitionAnalyticsResponse
) -> dict:
    """Generate overall insights from all analytics components"""

    # Calculate overall performance score (weighted average)
    weights = {
        'subject': 0.4,
        'class_grade': 0.3,
        'classroom': 0.2,
        'competition': 0.1
    }

    subject_score = subject_analytics.overall_gpa
    class_grade_score = class_grade_analytics.grade_analytics.grade_percentile
    classroom_score = classroom_analytics.overall_engagement_score
    competition_score = competition_analytics.summary.average_score

    overall_performance_score = (
        subject_score * weights['subject'] +
        class_grade_score * weights['class_grade'] +
        classroom_score * weights['classroom'] +
        competition_score * weights['competition']
    )

    # Identify strength areas
    strength_areas = []
    if subject_analytics.strongest_subject:
        strength_areas.append(f"Subject: {subject_analytics.strongest_subject}")
    if classroom_analytics.most_engaged_classroom:
        strength_areas.append(f"Classroom Engagement: {classroom_analytics.most_engaged_classroom}")
    if competition_analytics.summary.strongest_competition_categories:
        strength_areas.extend([f"Competition: {cat}" for cat in competition_analytics.summary.strongest_competition_categories[:2]])

    # Identify improvement opportunities
    improvement_opportunities = []
    if subject_analytics.weakest_subject:
        improvement_opportunities.append(f"Subject Performance: {subject_analytics.weakest_subject}")
    if classroom_analytics.least_engaged_classroom:
        improvement_opportunities.append(f"Classroom Engagement: {classroom_analytics.least_engaged_classroom}")
    if class_grade_analytics.grade_analytics.achievement_level == "needs_improvement":
        improvement_opportunities.append("Overall Grade Performance")

    # Generate personalized recommendations
    personalized_recommendations = []

    # Combine recommendations from all analytics
    all_recommendations = (
        class_grade_analytics.improvement_recommendations +
        classroom_analytics.recommendations
    )

    # Add subject-specific recommendations
    for subject in subject_analytics.subjects:
        if subject.improvement_areas:
            personalized_recommendations.extend(subject.recommended_actions[:2])

    # Deduplicate and limit recommendations
    personalized_recommendations = list(set(all_recommendations + personalized_recommendations))[:10]

    # Generate current goals based on actual performance data
    current_goals = []

    # Add GPA improvement goal if below 80%
    if subject_analytics.overall_gpa < 80.0:
        current_goals.append({
            "goal": "Improve overall GPA",
            "target": min(subject_analytics.overall_gpa + 10.0, 95.0),
            "current": subject_analytics.overall_gpa,
            "deadline": "End of semester"
        })

    # Add engagement goal if below 85%
    if classroom_analytics.overall_engagement_score < 85.0:
        current_goals.append({
            "goal": "Increase classroom engagement",
            "target": min(classroom_analytics.overall_engagement_score + 15.0, 95.0),
            "current": classroom_analytics.overall_engagement_score,
            "deadline": "Next month"
        })

    # Add competition participation goal if low participation
    if competition_analytics.summary.total_competitions_participated < 3:
        current_goals.append({
            "goal": "Participate in more competitions",
            "target": 5,
            "current": competition_analytics.summary.total_competitions_participated,
            "deadline": "End of quarter"
        })

    # Calculate achievement progress
    achievement_progress = {
        "academic_performance": min(100, subject_analytics.overall_gpa),
        "classroom_engagement": classroom_analytics.overall_engagement_score,
        "competition_participation": min(100, competition_analytics.summary.total_competitions_participated * 10),
        "grade_ranking": class_grade_analytics.grade_analytics.grade_percentile
    }

    return {
        'performance_score': round(overall_performance_score, 2),
        'strength_areas': strength_areas,
        'improvement_opportunities': improvement_opportunities,
        'personalized_recommendations': personalized_recommendations,
        'current_goals': current_goals,
        'achievement_progress': achievement_progress
    }


# Health check endpoint for analytics service
@router.get("/health")
async def analytics_health_check():
    """Health check endpoint for student analytics service"""
    return {
        "status": "healthy",
        "service": "student_analytics",
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "version": "1.0.0"
    }
