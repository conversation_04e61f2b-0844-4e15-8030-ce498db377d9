"""
User Event Dashboard CRUD Operations for EduFair Platform

This module contains CRUD operations for user event dashboard functionality including:
- User event history retrieval
- Event participation statistics
- Registration status tracking
- Event filtering and searching
"""

from datetime import datetime, timezone, timedelta
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, or_, func, desc, asc
from fastapi import HTTPException, status
from typing import List, Optional, Dict, Any
from uuid import UUID
from decimal import Decimal

# Import Models
from Models.Events import (
    Event, EventRegistration, EventTicket, EventPayment,
    RegistrationStatusEnum, PaymentStatusEnum, EventStatusEnum, EventCategoryEnum
)
from Models.users import User, UserTypeEnum

# Import Schemas
from Schemas.Users.EventDashboard import (
    UserEventSummary, UserEventStats, UserEventDashboard,
    EventFilterOptions, UserEventListResponse, EventParticipationStatus,
    UserEventDetails, EventTicketInfo, UserEventAction
)


def calculate_participation_status(
    event_start: datetime, 
    event_end: datetime, 
    registration_status: RegistrationStatusEnum,
    event_status: EventStatusEnum
) -> EventParticipationStatus:
    """Calculate user's participation status for an event"""
    now = datetime.now(timezone.utc)
    
    if registration_status == RegistrationStatusEnum.CANCELLED:
        return EventParticipationStatus.CANCELLED
    
    if event_status == EventStatusEnum.CANCELLED:
        return EventParticipationStatus.CANCELLED
    
    if registration_status == RegistrationStatusEnum.ATTENDED:
        return EventParticipationStatus.COMPLETED
    
    if now < event_start:
        return EventParticipationStatus.UPCOMING
    elif event_start <= now <= event_end:
        return EventParticipationStatus.ONGOING
    elif now > event_end:
        if registration_status == RegistrationStatusEnum.CONFIRMED:
            return EventParticipationStatus.MISSED
        else:
            return EventParticipationStatus.COMPLETED
    
    return EventParticipationStatus.UPCOMING


def get_user_event_stats(db: Session, user_id: UUID) -> UserEventStats:
    """Get user's event participation statistics"""
    
    # Get all user registrations
    registrations = db.query(EventRegistration).filter(
        EventRegistration.user_id == user_id
    ).all()
    
    if not registrations:
        return UserEventStats()
    
    # Calculate basic stats
    total_registrations = len(registrations)
    confirmed_registrations = len([r for r in registrations if r.status == RegistrationStatusEnum.CONFIRMED])
    attended_events = len([r for r in registrations if r.status == RegistrationStatusEnum.ATTENDED])
    cancelled_registrations = len([r for r in registrations if r.status == RegistrationStatusEnum.CANCELLED])
    
    # Calculate upcoming and completed events
    now = datetime.now(timezone.utc)
    upcoming_events = 0
    completed_events = 0
    
    # Calculate total spent
    total_spent = Decimal('0.00')
    
    # Category breakdown
    events_by_category = {}
    
    # Track dates
    last_registration_date = None
    last_attended_event = None
    
    for registration in registrations:
        # Get event details
        event = db.query(Event).filter(Event.id == registration.event_id).first()
        if not event:
            continue
        
        # Count by participation status
        participation_status = calculate_participation_status(
            event.start_datetime, event.end_datetime, 
            registration.status, event.status
        )
        
        if participation_status == EventParticipationStatus.UPCOMING:
            upcoming_events += 1
        elif participation_status in [EventParticipationStatus.COMPLETED, EventParticipationStatus.MISSED]:
            completed_events += 1
        
        # Add to total spent
        total_spent += registration.total_amount
        
        # Category breakdown
        category_name = event.category.value if hasattr(event.category, 'value') else str(event.category)
        events_by_category[category_name] = events_by_category.get(category_name, 0) + 1
        
        # Track dates
        if not last_registration_date or registration.registered_at > last_registration_date:
            last_registration_date = registration.registered_at
        
        if registration.attended_at and (not last_attended_event or registration.attended_at > last_attended_event):
            last_attended_event = registration.attended_at
    
    return UserEventStats(
        total_registrations=total_registrations,
        confirmed_registrations=confirmed_registrations,
        attended_events=attended_events,
        cancelled_registrations=cancelled_registrations,
        upcoming_events=upcoming_events,
        completed_events=completed_events,
        total_spent=total_spent,
        events_by_category=events_by_category,
        last_registration_date=last_registration_date,
        last_attended_event=last_attended_event
    )


def build_user_event_summary(
    registration: EventRegistration, 
    event: Event, 
    ticket: Optional[EventTicket] = None,
    organizer: Optional[User] = None
) -> UserEventSummary:
    """Build a UserEventSummary from registration and event data"""
    
    # Calculate participation status
    participation_status = calculate_participation_status(
        event.start_datetime, event.end_datetime,
        registration.status, event.status
    )
    
    # Calculate days until event
    now = datetime.now(timezone.utc)
    days_until_event = None
    if event.start_datetime > now:
        days_until_event = (event.start_datetime.date() - now.date()).days
    
    # Determine if user can cancel or modify
    can_cancel = (
        registration.status in [RegistrationStatusEnum.PENDING, RegistrationStatusEnum.CONFIRMED] and
        event.start_datetime > now + timedelta(hours=24)  # Can cancel up to 24 hours before
    )
    
    can_modify = (
        registration.status in [RegistrationStatusEnum.PENDING, RegistrationStatusEnum.CONFIRMED] and
        event.start_datetime > now + timedelta(hours=48)  # Can modify up to 48 hours before
    )
    
    return UserEventSummary(
        event_id=event.id,
        event_title=event.title,
        event_description=event.description,
        event_category=event.category,
        event_status=event.status,
        event_location=event.location,
        event_start_datetime=event.start_datetime,
        event_end_datetime=event.end_datetime,
        
        registration_id=registration.id,
        registration_status=registration.status,
        registration_number=registration.registration_number,
        registered_at=registration.registered_at,
        confirmed_at=registration.confirmed_at,
        attended_at=registration.attended_at,
        cancelled_at=registration.cancelled_at,
        
        ticket_id=ticket.id if ticket else None,
        ticket_name=ticket.name if ticket else "Free Event",
        ticket_price=ticket.price if ticket else Decimal('0.00'),
        quantity=registration.quantity,
        
        payment_status=registration.payment_status,
        payment_reference=registration.payment_reference,
        total_amount=registration.total_amount,
        currency=registration.currency,
        
        qr_code=registration.qr_code,
        check_in_code=registration.check_in_code,
        
        participation_status=participation_status,
        can_cancel=can_cancel,
        can_modify=can_modify,
        days_until_event=days_until_event,
        
        organizer_name=organizer.username if organizer else None,
        organizer_email=organizer.email if organizer else None
    )


def get_user_events(
    db: Session, 
    user_id: UUID, 
    filters: Optional[EventFilterOptions] = None,
    skip: int = 0, 
    limit: int = 100
) -> UserEventListResponse:
    """Get user's events with filtering and pagination"""
    
    # Build base query
    query = db.query(EventRegistration).options(
        joinedload(EventRegistration.event),
        joinedload(EventRegistration.ticket)
    ).filter(EventRegistration.user_id == user_id)
    
    # Apply filters
    if filters:
        if filters.status:
            query = query.filter(EventRegistration.status.in_(filters.status))
        
        if filters.payment_status:
            query = query.filter(EventRegistration.payment_status.in_(filters.payment_status))
        
        if filters.date_from:
            query = query.join(Event).filter(Event.start_datetime >= filters.date_from)
        
        if filters.date_to:
            query = query.join(Event).filter(Event.start_datetime <= filters.date_to)
        
        if filters.category:
            query = query.join(Event).filter(Event.category.in_(filters.category))
        
        if filters.search_query:
            search_term = f"%{filters.search_query}%"
            query = query.join(Event).filter(
                or_(
                    Event.title.ilike(search_term),
                    Event.description.ilike(search_term),
                    Event.location.ilike(search_term)
                )
            )
    
    # Get total count
    total_count = query.count()
    
    # Get paginated results
    registrations = query.order_by(desc(EventRegistration.registered_at)).offset(skip).limit(limit).all()
    
    # Build event summaries
    event_summaries = []
    for registration in registrations:
        # Get organizer info
        organizer = db.query(User).filter(User.id == registration.event.organizer_id).first()
        
        summary = build_user_event_summary(
            registration, registration.event, registration.ticket, organizer
        )
        event_summaries.append(summary)
    
    return UserEventListResponse(
        events=event_summaries,
        total_count=total_count,
        page=skip // limit + 1,
        per_page=limit,
        has_next=skip + limit < total_count,
        has_prev=skip > 0,
        filters_applied=filters
    )


def get_user_event_dashboard(db: Session, user_id: UUID) -> UserEventDashboard:
    """Get complete user event dashboard"""

    # Get user info
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )

    # Get user stats
    stats = get_user_event_stats(db, user_id)

    # Get upcoming events (next 30 days)
    now = datetime.now(timezone.utc)
    future_date = now + timedelta(days=30)

    upcoming_query = db.query(EventRegistration).options(
        joinedload(EventRegistration.event),
        joinedload(EventRegistration.ticket)
    ).join(Event).filter(
        EventRegistration.user_id == user_id,
        Event.start_datetime >= now,
        Event.start_datetime <= future_date,
        EventRegistration.status.in_([RegistrationStatusEnum.PENDING, RegistrationStatusEnum.CONFIRMED])
    ).order_by(Event.start_datetime)

    upcoming_registrations = upcoming_query.all()
    upcoming_events = []
    next_event = None

    for registration in upcoming_registrations:
        organizer = db.query(User).filter(User.id == registration.event.organizer_id).first()
        summary = build_user_event_summary(registration, registration.event, registration.ticket, organizer)
        upcoming_events.append(summary)

        if not next_event:
            next_event = summary

    # Get past events (last 90 days)
    past_date = now - timedelta(days=90)

    past_query = db.query(EventRegistration).options(
        joinedload(EventRegistration.event),
        joinedload(EventRegistration.ticket)
    ).join(Event).filter(
        EventRegistration.user_id == user_id,
        Event.end_datetime < now,
        Event.end_datetime >= past_date,
        EventRegistration.status.in_([RegistrationStatusEnum.CONFIRMED, RegistrationStatusEnum.ATTENDED])
    ).order_by(desc(Event.end_datetime))

    past_registrations = past_query.limit(10).all()
    past_events = []

    for registration in past_registrations:
        organizer = db.query(User).filter(User.id == registration.event.organizer_id).first()
        summary = build_user_event_summary(registration, registration.event, registration.ticket, organizer)
        past_events.append(summary)

    # Get cancelled events (last 30 days)
    cancelled_date = now - timedelta(days=30)

    cancelled_query = db.query(EventRegistration).options(
        joinedload(EventRegistration.event),
        joinedload(EventRegistration.ticket)
    ).filter(
        EventRegistration.user_id == user_id,
        EventRegistration.status == RegistrationStatusEnum.CANCELLED,
        EventRegistration.cancelled_at >= cancelled_date
    ).order_by(desc(EventRegistration.cancelled_at))

    cancelled_registrations = cancelled_query.limit(5).all()
    cancelled_events = []

    for registration in cancelled_registrations:
        organizer = db.query(User).filter(User.id == registration.event.organizer_id).first()
        summary = build_user_event_summary(registration, registration.event, registration.ticket, organizer)
        cancelled_events.append(summary)

    # Get recent registrations (last 7 days)
    recent_date = now - timedelta(days=7)

    recent_query = db.query(EventRegistration).options(
        joinedload(EventRegistration.event),
        joinedload(EventRegistration.ticket)
    ).filter(
        EventRegistration.user_id == user_id,
        EventRegistration.registered_at >= recent_date
    ).order_by(desc(EventRegistration.registered_at))

    recent_registrations_data = recent_query.limit(5).all()
    recent_registrations = []

    for registration in recent_registrations_data:
        organizer = db.query(User).filter(User.id == registration.event.organizer_id).first()
        summary = build_user_event_summary(registration, registration.event, registration.ticket, organizer)
        recent_registrations.append(summary)

    return UserEventDashboard(
        user_id=user.id,
        user_name=user.username,
        user_email=user.email,
        user_type=user.user_type.value if hasattr(user.user_type, 'value') else str(user.user_type),
        stats=stats,
        upcoming_events=upcoming_events,
        past_events=past_events,
        cancelled_events=cancelled_events,
        next_event=next_event,
        recent_registrations=recent_registrations
    )


def get_user_event_details(db: Session, user_id: UUID, registration_id: UUID) -> UserEventDetails:
    """Get detailed view of user's event registration"""

    # Get registration with related data
    registration = db.query(EventRegistration).options(
        joinedload(EventRegistration.event),
        joinedload(EventRegistration.ticket)
    ).filter(
        EventRegistration.id == registration_id,
        EventRegistration.user_id == user_id
    ).first()

    if not registration:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Registration not found"
        )

    # Get organizer info
    organizer = db.query(User).filter(User.id == registration.event.organizer_id).first()

    # Build event summary
    event_summary = build_user_event_summary(registration, registration.event, registration.ticket, organizer)

    # Build ticket info if exists
    ticket_info = None
    if registration.ticket:
        ticket_info = EventTicketInfo(
            ticket_id=registration.ticket.id,
            event_id=registration.event.id,
            event_title=registration.event.title,
            ticket_name=registration.ticket.name,
            ticket_description=registration.ticket.description,
            price=registration.ticket.price,
            currency=registration.ticket.currency,
            quantity=registration.quantity,
            qr_code=registration.qr_code,
            check_in_code=registration.check_in_code,
            event_location=registration.event.location,
            event_start_datetime=registration.event.start_datetime,
            event_end_datetime=registration.event.end_datetime,
            registration_status=registration.status,
            payment_status=registration.payment_status,
            can_transfer=event_summary.can_modify,
            can_refund=event_summary.can_cancel,
            organizer_contact=organizer.email if organizer else None
        )

    # Build available actions
    available_actions = []

    if event_summary.can_cancel:
        available_actions.append(UserEventAction(
            action_type="cancel",
            action_label="Cancel Registration",
            requires_confirmation=True,
            confirmation_message="Are you sure you want to cancel this registration? This action cannot be undone."
        ))

    if event_summary.can_modify:
        available_actions.append(UserEventAction(
            action_type="modify",
            action_label="Modify Registration",
            requires_confirmation=False
        ))

    if registration.qr_code:
        available_actions.append(UserEventAction(
            action_type="download_ticket",
            action_label="Download Ticket",
            requires_confirmation=False
        ))

    if organizer:
        available_actions.append(UserEventAction(
            action_type="contact_organizer",
            action_label="Contact Organizer",
            requires_confirmation=False
        ))

    return UserEventDetails(
        event_summary=event_summary,
        ticket_info=ticket_info,
        available_actions=available_actions,
        event_agenda=registration.event.agenda if hasattr(registration.event, 'agenda') else None,
        attendee_info=registration.attendee_info,
        special_requirements=registration.special_requirements,
        emergency_contact=registration.emergency_contact,
        dietary_preferences=registration.dietary_preferences,
        accessibility_needs=registration.accessibility_needs
    )
