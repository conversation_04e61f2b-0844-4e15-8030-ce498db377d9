"""
Institute Payout Dashboard CRUD Operations for EduFair Platform

This module contains CRUD operations for institute payout dashboard functionality including:
- Sales reports and revenue tracking
- Payout history and status
- Pending revenue calculations
- Bank account management
"""

from datetime import datetime, timezone, timedelta
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, or_, func, desc, asc
from fastapi import HTTPException, status
from typing import List, Optional, Dict, Any
from uuid import UUID
from decimal import Decimal

# Import Models
from Models.Events import (
    Event, EventRegistration, EventTicket, EventPayment, Payout,
    RegistrationStatusEnum, PaymentStatusEnum, PayoutStatusEnum, PayoutMethodEnum
)
from Models.users import User, InstituteProfile

# Import Schemas
from Schemas.Payouts import (
    PayoutOut, InstitutePayoutSummary, EventRevenueBreakdown
)


def get_institute_payout_summary(db: Session, institute_id: UUID) -> InstitutePayoutSummary:
    """Get comprehensive payout summary for an institute"""
    
    # Get institute details
    institute = db.query(User).join(InstituteProfile).filter(User.id == institute_id).first()
    if not institute:
        raise HTTPException(status_code=404, detail="Institute not found")
    
    institute_name = institute.institute_profile.institute_name if institute.institute_profile else institute.username
    
    # Get payout statistics
    total_payouts_received = db.query(Payout).filter(
        Payout.institute_id == institute_id,
        Payout.status == PayoutStatusEnum.COMPLETED
    ).count()
    
    total_amount_received = db.query(func.sum(Payout.amount)).filter(
        Payout.institute_id == institute_id,
        Payout.status == PayoutStatusEnum.COMPLETED
    ).scalar() or Decimal('0.00')
    
    pending_payouts = db.query(Payout).filter(
        Payout.institute_id == institute_id,
        Payout.status == PayoutStatusEnum.PENDING
    ).count()
    
    pending_amount = db.query(func.sum(Payout.amount)).filter(
        Payout.institute_id == institute_id,
        Payout.status == PayoutStatusEnum.PENDING
    ).scalar() or Decimal('0.00')
    
    # Get recent payouts
    recent_payouts_query = db.query(Payout).options(
        joinedload(Payout.event),
        joinedload(Payout.creator),
        joinedload(Payout.processor)
    ).filter(
        Payout.institute_id == institute_id
    ).order_by(desc(Payout.created_at)).limit(10)
    
    recent_payouts = []
    for payout in recent_payouts_query.all():
        payout_dict = payout.__dict__.copy()
        payout_dict['institute_name'] = institute_name
        payout_dict['event_title'] = payout.event.title
        payout_dict['creator_name'] = payout.creator.username
        payout_dict['processor_name'] = payout.processor.username if payout.processor else None
        recent_payouts.append(PayoutOut(**payout_dict))
    
    # Get events with pending payouts (events with revenue but no payout)
    institute_events = db.query(Event).filter(Event.organizer_id == institute_id).all()
    events_pending_payout = []
    
    for event in institute_events:
        # Check if event has revenue
        total_revenue = db.query(func.sum(EventPayment.amount)).filter(
            EventPayment.event_id == event.id,
            EventPayment.status == PaymentStatusEnum.COMPLETED
        ).scalar() or Decimal('0.00')
        
        if total_revenue > 0:
            # Check if payout already exists
            existing_payout = db.query(Payout).filter(Payout.event_id == event.id).first()
            if not existing_payout:
                # Calculate revenue breakdown
                try:
                    from Cruds.Admin.Payouts import calculate_event_revenue
                    revenue_breakdown = calculate_event_revenue(db, event.id)
                    events_pending_payout.append(revenue_breakdown)
                except:
                    continue  # Skip events with calculation errors
    
    # Check bank account details
    has_bank_details = False
    preferred_payout_method = None
    
    if institute.institute_profile:
        profile = institute.institute_profile
        if profile.bank_account_number and profile.bank_name:
            has_bank_details = True
            preferred_payout_method = PayoutMethodEnum.BANK_TRANSFER
        elif profile.jazzcash_number:
            has_bank_details = True
            preferred_payout_method = PayoutMethodEnum.JAZZCASH
        elif profile.easypaisa_number:
            has_bank_details = True
            preferred_payout_method = PayoutMethodEnum.EASYPAISA
        elif profile.paypal_email:
            has_bank_details = True
            preferred_payout_method = PayoutMethodEnum.PAYPAL
    
    return InstitutePayoutSummary(
        institute_id=institute_id,
        institute_name=institute_name,
        total_payouts_received=total_payouts_received,
        total_amount_received=total_amount_received,
        pending_payouts=pending_payouts,
        pending_amount=pending_amount,
        recent_payouts=recent_payouts,
        events_pending_payout=events_pending_payout,
        has_bank_details=has_bank_details,
        preferred_payout_method=preferred_payout_method
    )


def get_institute_payouts(
    db: Session, 
    institute_id: UUID, 
    skip: int = 0, 
    limit: int = 20
) -> List[PayoutOut]:
    """Get paginated list of payouts for an institute"""
    
    # Verify institute exists
    institute = db.query(User).join(InstituteProfile).filter(User.id == institute_id).first()
    if not institute:
        raise HTTPException(status_code=404, detail="Institute not found")
    
    institute_name = institute.institute_profile.institute_name if institute.institute_profile else institute.username
    
    # Get payouts
    payouts_query = db.query(Payout).options(
        joinedload(Payout.event),
        joinedload(Payout.creator),
        joinedload(Payout.processor)
    ).filter(
        Payout.institute_id == institute_id
    ).order_by(desc(Payout.created_at)).offset(skip).limit(limit)
    
    payouts = []
    for payout in payouts_query.all():
        payout_dict = payout.__dict__.copy()
        payout_dict['institute_name'] = institute_name
        payout_dict['event_title'] = payout.event.title
        payout_dict['creator_name'] = payout.creator.username
        payout_dict['processor_name'] = payout.processor.username if payout.processor else None
        payouts.append(PayoutOut(**payout_dict))
    
    return payouts


def get_institute_sales_report(db: Session, institute_id: UUID, days: int = 30) -> Dict[str, Any]:
    """Get sales report for an institute"""
    
    # Verify institute exists
    institute = db.query(User).join(InstituteProfile).filter(User.id == institute_id).first()
    if not institute:
        raise HTTPException(status_code=404, detail="Institute not found")
    
    # Calculate date range
    end_date = datetime.now(timezone.utc)
    start_date = end_date - timedelta(days=days)
    
    # Get institute events
    events = db.query(Event).filter(Event.organizer_id == institute_id).all()
    event_ids = [event.id for event in events]
    
    if not event_ids:
        return {
            "period_days": days,
            "total_events": 0,
            "total_registrations": 0,
            "total_revenue": 0.0,
            "events_breakdown": []
        }
    
    # Get registrations in the period
    registrations = db.query(EventRegistration).filter(
        EventRegistration.event_id.in_(event_ids),
        EventRegistration.registered_at >= start_date,
        EventRegistration.registered_at <= end_date
    ).all()
    
    # Get payments in the period
    payments = db.query(EventPayment).filter(
        EventPayment.event_id.in_(event_ids),
        EventPayment.status == PaymentStatusEnum.COMPLETED,
        EventPayment.processed_at >= start_date,
        EventPayment.processed_at <= end_date
    ).all()
    
    total_revenue = sum(p.amount for p in payments)
    
    # Break down by event
    events_breakdown = []
    for event in events:
        event_registrations = [r for r in registrations if r.event_id == event.id]
        event_payments = [p for p in payments if p.event_id == event.id]
        event_revenue = sum(p.amount for p in event_payments)
        
        # Get payout status for this event
        payout = db.query(Payout).filter(Payout.event_id == event.id).first()
        payout_status = payout.status.value if payout else "No payout created"
        
        events_breakdown.append({
            "event_id": str(event.id),
            "event_title": event.title,
            "event_date": event.start_datetime.isoformat() if event.start_datetime else None,
            "registrations_count": len(event_registrations),
            "revenue": float(event_revenue),
            "payout_status": payout_status,
            "payout_amount": float(payout.amount) if payout else 0.0
        })
    
    return {
        "period_days": days,
        "start_date": start_date.isoformat(),
        "end_date": end_date.isoformat(),
        "total_events": len(events),
        "total_registrations": len(registrations),
        "total_revenue": float(total_revenue),
        "events_breakdown": events_breakdown
    }


def update_institute_bank_details(
    db: Session, 
    institute_id: UUID, 
    bank_details: Dict[str, Any]
) -> Dict[str, str]:
    """Update institute bank account details"""
    
    # Get institute profile
    institute = db.query(User).join(InstituteProfile).filter(User.id == institute_id).first()
    if not institute or not institute.institute_profile:
        raise HTTPException(status_code=404, detail="Institute profile not found")
    
    profile = institute.institute_profile
    
    # Update bank details
    if 'bank_account_number' in bank_details:
        profile.bank_account_number = bank_details['bank_account_number']
    if 'bank_name' in bank_details:
        profile.bank_name = bank_details['bank_name']
    if 'account_holder_name' in bank_details:
        profile.account_holder_name = bank_details['account_holder_name']
    if 'bank_branch' in bank_details:
        profile.bank_branch = bank_details['bank_branch']
    if 'iban' in bank_details:
        profile.iban = bank_details['iban']
    if 'swift_code' in bank_details:
        profile.swift_code = bank_details['swift_code']
    if 'jazzcash_number' in bank_details:
        profile.jazzcash_number = bank_details['jazzcash_number']
    if 'easypaisa_number' in bank_details:
        profile.easypaisa_number = bank_details['easypaisa_number']
    if 'paypal_email' in bank_details:
        profile.paypal_email = bank_details['paypal_email']
    
    db.commit()
    
    return {"message": "Bank details updated successfully"}
