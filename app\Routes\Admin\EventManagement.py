"""
Admin Event Management Routes

This module provides admin-level routes for managing events and registrations.
Only users with admin privileges can access these endpoints.
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from typing import List, Optional
from uuid import UUID

# Database and Authentication
from config.session import get_db
from config.deps import get_current_user
from config.security import oauth2_scheme
from config.permission import require_type

# Models
from Models.Events import Event, EventTicket, EventRegistration, EventPayment
from Models.users import User, UserTypeEnum

# Schemas
from Schemas.Events.Events import EventOut, EventDetailedOut
from Schemas.Events.EventManagement import (
    EventRegistrationOut, EventRegistrationUpdate, 
    AdminEventRegistrationOut, AdminEventStatsOut
)
from Schemas.Admin import AdminEventListResponse, AdminRegistrationListResponse

# CRUD Operations
from Cruds.Events.Events import get_event_by_id, delete_event
from Cruds.Events.EventRegistrations import (
    get_registration_by_id, update_event_registration, delete_event_registration
)

from datetime import datetime, timezone
from decimal import Decimal

router = APIRouter()


@router.get("/events", response_model=AdminEventListResponse)
def get_all_events_admin(
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    status_filter: Optional[str] = Query(None),
    category_filter: Optional[str] = Query(None),
    search: Optional[str] = Query(None),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("admin"))
):
    """
    Get all events with admin-level details including statistics.
    
    Admin can view all events regardless of organizer.
    """
    current_user = get_current_user(token, db)
    
    try:
        query = db.query(Event)
        
        # Apply filters
        if status_filter:
            query = query.filter(Event.status == status_filter)
        
        if category_filter:
            query = query.filter(Event.category == category_filter)
        
        if search:
            search_term = f"%{search}%"
            query = query.filter(
                Event.title.ilike(search_term) |
                Event.description.ilike(search_term) |
                Event.location.ilike(search_term)
            )
        
        # Get total count
        total_count = query.count()
        
        # Get paginated results
        events = query.order_by(Event.created_at.desc()).offset(skip).limit(limit).all()
        
        # Enhance events with admin statistics
        enhanced_events = []
        for event in events:
            # Get registration statistics
            total_registrations = db.query(EventRegistration).filter(
                EventRegistration.event_id == event.id
            ).count()
            
            confirmed_registrations = db.query(EventRegistration).filter(
                EventRegistration.event_id == event.id,
                EventRegistration.status == "CONFIRMED"
            ).count()
            
            # Get revenue statistics
            total_revenue = db.query(EventPayment).filter(
                EventPayment.event_id == event.id,
                EventPayment.status == "COMPLETED"
            ).with_entities(
                db.func.sum(EventPayment.amount)
            ).scalar() or Decimal('0.00')
            
            # Get ticket statistics
            total_tickets = db.query(EventTicket).filter(
                EventTicket.event_id == event.id
            ).with_entities(
                db.func.sum(EventTicket.total_quantity)
            ).scalar() or 0
            
            sold_tickets = db.query(EventTicket).filter(
                EventTicket.event_id == event.id
            ).with_entities(
                db.func.sum(EventTicket.sold_quantity)
            ).scalar() or 0
            
            # Get organizer info
            organizer = db.query(User).filter(User.id == event.organizer_id).first()
            
            enhanced_event = {
                **event.__dict__,
                "total_registrations": total_registrations,
                "confirmed_registrations": confirmed_registrations,
                "total_revenue": float(total_revenue),
                "total_tickets": total_tickets,
                "sold_tickets": sold_tickets,
                "available_tickets": total_tickets - sold_tickets,
                "organizer_email": organizer.email if organizer else None,
                "organizer_name": organizer.username if organizer else None
            }
            enhanced_events.append(enhanced_event)
        
        return AdminEventListResponse(
            events=enhanced_events,
            total_count=total_count,
            page=skip // limit + 1,
            per_page=limit,
            has_next=skip + limit < total_count,
            has_prev=skip > 0
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve events: {str(e)}"
        )


@router.get("/events/{event_id}", response_model=EventDetailedOut)
def get_event_admin(
    event_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("admin"))
):
    """
    Get detailed event information with admin-level access.
    """
    current_user = get_current_user(token, db)
    
    try:
        event = get_event_by_id(db, event_id)
        if not event:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Event not found"
            )
        
        return event
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve event: {str(e)}"
        )


@router.delete("/events/{event_id}")
def delete_event_admin(
    event_id: UUID,
    force: bool = Query(False, description="Force delete even with registrations"),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("admin"))
):
    """
    Delete an event and all associated data.
    
    Admin can delete any event. Use force=true to delete events with registrations.
    """
    current_user = get_current_user(token, db)
    
    try:
        event = db.query(Event).filter(Event.id == event_id).first()
        if not event:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Event not found"
            )
        
        # Check for existing registrations
        registration_count = db.query(EventRegistration).filter(
            EventRegistration.event_id == event_id
        ).count()
        
        if registration_count > 0 and not force:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Cannot delete event with {registration_count} registrations. Use force=true to override."
            )
        
        # Delete associated data in correct order
        # 1. Delete payments
        db.query(EventPayment).filter(EventPayment.event_id == event_id).delete()
        
        # 2. Delete registrations
        db.query(EventRegistration).filter(EventRegistration.event_id == event_id).delete()
        
        # 3. Delete tickets
        db.query(EventTicket).filter(EventTicket.event_id == event_id).delete()
        
        # 4. Delete event
        db.delete(event)
        db.commit()
        
        return {
            "message": "Event deleted successfully",
            "event_id": event_id,
            "deleted_registrations": registration_count,
            "deleted_by": current_user.email
        }
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete event: {str(e)}"
        )


@router.get("/events/{event_id}/stats", response_model=AdminEventStatsOut)
def get_event_statistics_admin(
    event_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("admin"))
):
    """
    Get comprehensive event statistics for admin dashboard.
    """
    current_user = get_current_user(token, db)
    
    try:
        event = db.query(Event).filter(Event.id == event_id).first()
        if not event:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Event not found"
            )
        
        # Registration statistics
        registrations = db.query(EventRegistration).filter(
            EventRegistration.event_id == event_id
        ).all()
        
        registration_stats = {
            "total": len(registrations),
            "confirmed": len([r for r in registrations if r.status == "CONFIRMED"]),
            "pending": len([r for r in registrations if r.status == "PENDING"]),
            "cancelled": len([r for r in registrations if r.status == "CANCELLED"]),
            "attended": len([r for r in registrations if r.status == "ATTENDED"])
        }
        
        # Revenue statistics
        payments = db.query(EventPayment).filter(
            EventPayment.event_id == event_id
        ).all()
        
        revenue_stats = {
            "total_revenue": sum(p.amount for p in payments if p.status == "COMPLETED"),
            "pending_revenue": sum(p.amount for p in payments if p.status == "PENDING"),
            "refunded_amount": sum(p.amount for p in payments if p.status == "REFUNDED"),
            "payment_count": len(payments)
        }
        
        # Ticket statistics
        tickets = db.query(EventTicket).filter(EventTicket.event_id == event_id).all()
        
        ticket_stats = {
            "total_tickets": sum(t.total_quantity for t in tickets),
            "sold_tickets": sum(t.sold_quantity for t in tickets),
            "available_tickets": sum(t.available_quantity for t in tickets),
            "ticket_types": len(tickets)
        }
        
        return AdminEventStatsOut(
            event_id=event_id,
            registration_stats=registration_stats,
            revenue_stats=revenue_stats,
            ticket_stats=ticket_stats,
            generated_at=datetime.now(timezone.utc)
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get event statistics: {str(e)}"
        )


# ==================== REGISTRATION MANAGEMENT ====================

@router.get("/events/{event_id}/registrations", response_model=AdminRegistrationListResponse)
def get_event_registrations_admin(
    event_id: UUID,
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    status_filter: Optional[str] = Query(None),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("admin"))
):
    """
    Get all registrations for a specific event with admin-level details.

    Admin can view registrations for any event regardless of organizer.
    """
    current_user = get_current_user(token, db)

    try:
        # Verify event exists
        event = db.query(Event).filter(Event.id == event_id).first()
        if not event:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Event not found"
            )

        # Build query for registrations of this specific event
        query = db.query(EventRegistration).filter(EventRegistration.event_id == event_id)

        # Apply status filter if provided (convert to uppercase to match database enum)
        if status_filter:
            status_filter_upper = status_filter.upper()
            query = query.filter(EventRegistration.status == status_filter_upper)

        # Get total count
        total_count = query.count()

        # Get paginated results
        registrations = query.order_by(EventRegistration.created_at.desc()).offset(skip).limit(limit).all()

        # Enhance registrations with additional info
        enhanced_registrations = []
        for registration in registrations:
            # Get user info
            user = db.query(User).filter(User.id == registration.user_id).first()

            # Get ticket info
            ticket = db.query(EventTicket).filter(EventTicket.id == registration.ticket_id).first()

            # Get payment info with error handling
            payment_status = None
            payment_id = None
            try:
                payment = db.query(EventPayment).filter(
                    EventPayment.registration_id == registration.id
                ).first()

                if payment:
                    payment_status = payment.status.value if hasattr(payment.status, 'value') else str(payment.status)
                    payment_id = payment.id
            except Exception as e:
                print(f"Error querying payment data: {e}")
                payment_status = "unknown"

            enhanced_registration = {
                **registration.__dict__,
                "user_email": user.email if user else None,
                "user_name": user.username if user else None,
                "user_type": user.user_type.value if user else None,
                "event_title": event.title,
                "event_location": event.location,
                "event_start_date": event.start_datetime.isoformat(),
                "ticket_name": ticket.name if ticket else "Free Event",
                "ticket_price": float(ticket.price) if ticket else 0.0,
                "payment_status": payment_status,
                "payment_id": payment_id
            }
            enhanced_registrations.append(enhanced_registration)

        return AdminRegistrationListResponse(
            registrations=enhanced_registrations,
            total_count=total_count,
            page=skip // limit + 1,
            per_page=limit,
            has_next=skip + limit < total_count,
            has_prev=skip > 0
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve event registrations: {str(e)}"
        )


@router.get("/registrations", response_model=AdminRegistrationListResponse)
def get_all_registrations_admin(
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    event_id: Optional[UUID] = Query(None),
    status_filter: Optional[str] = Query(None),
    user_email: Optional[str] = Query(None),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("admin"))
):
    """
    Get all event registrations with admin-level details.

    Admin can view all registrations across all events.
    """
    current_user = get_current_user(token, db)

    try:
        query = db.query(EventRegistration)

        # Apply filters
        if event_id:
            query = query.filter(EventRegistration.event_id == event_id)

        if status_filter:
            status_filter_upper = status_filter.upper()
            query = query.filter(EventRegistration.status == status_filter_upper)

        if user_email:
            # Join with User table to filter by email
            query = query.join(User).filter(User.email.ilike(f"%{user_email}%"))

        # Get total count
        total_count = query.count()

        # Get paginated results
        registrations = query.order_by(EventRegistration.created_at.desc()).offset(skip).limit(limit).all()

        # Enhance registrations with additional info
        enhanced_registrations = []
        for registration in registrations:
            # Get user info
            user = db.query(User).filter(User.id == registration.user_id).first()

            # Get event info
            event = db.query(Event).filter(Event.id == registration.event_id).first()

            # Get ticket info
            ticket = db.query(EventTicket).filter(EventTicket.id == registration.ticket_id).first()

            # Get payment info with error handling
            payment_status = None
            payment_id = None
            try:
                payment = db.query(EventPayment).filter(
                    EventPayment.registration_id == registration.id
                ).first()

                if payment:
                    payment_status = payment.status.value if hasattr(payment.status, 'value') else str(payment.status)
                    payment_id = payment.id
            except Exception as e:
                print(f"Error querying payment data: {e}")
                payment_status = "unknown"

            enhanced_registration = {
                **registration.__dict__,
                "user_email": user.email if user else None,
                "user_name": user.username if user else None,
                "user_type": user.user_type.value if user else None,
                "event_title": event.title if event else None,
                "event_location": event.location if event else None,
                "event_start_date": event.start_datetime.isoformat() if event else None,
                "ticket_name": ticket.name if ticket else None,
                "ticket_price": float(ticket.price) if ticket else 0.0,
                "payment_status": payment_status,
                "payment_id": payment_id
            }
            enhanced_registrations.append(enhanced_registration)

        return AdminRegistrationListResponse(
            registrations=enhanced_registrations,
            total_count=total_count,
            page=skip // limit + 1,
            per_page=limit,
            has_next=skip + limit < total_count,
            has_prev=skip > 0
        )

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve registrations: {str(e)}"
        )


@router.get("/registrations/{registration_id}", response_model=AdminEventRegistrationOut)
def get_registration_admin(
    registration_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("admin"))
):
    """
    Get detailed registration information with admin-level access.
    """
    current_user = get_current_user(token, db)

    try:
        registration = get_registration_by_id(db, registration_id, current_user.id)
        if not registration:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Registration not found"
            )

        # Get additional details
        user = db.query(User).filter(User.id == registration.user_id).first()
        event = db.query(Event).filter(Event.id == registration.event_id).first()
        ticket = db.query(EventTicket).filter(EventTicket.id == registration.ticket_id).first()
        # Get payment info with error handling
        payment_status = None
        payment_reference = None
        try:
            payment = db.query(EventPayment).filter(
                EventPayment.registration_id == registration_id
            ).first()

            if payment:
                payment_status = payment.status.value if hasattr(payment.status, 'value') else str(payment.status)
                payment_reference = payment.gateway_transaction_id
        except Exception as e:
            print(f"Error querying payment data: {e}")
            payment_status = "unknown"

        return AdminEventRegistrationOut(
            **registration.__dict__,
            user_email=user.email if user else None,
            user_name=user.username if user else None,
            user_phone=user.phone if user else None,
            event_title=event.title if event else None,
            event_location=event.location if event else None,
            ticket_name=ticket.name if ticket else None,
            payment_status=payment_status,
            payment_reference=payment_reference
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve registration: {str(e)}"
        )


@router.put("/registrations/{registration_id}", response_model=AdminEventRegistrationOut)
def update_registration_admin(
    registration_id: UUID,
    registration_update: EventRegistrationUpdate,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("admin"))
):
    """
    Update an event registration with admin privileges.

    Admin can modify any registration field including status.
    """
    current_user = get_current_user(token, db)

    try:
        registration = get_registration_by_id(db, registration_id, current_user.id)
        if not registration:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Registration not found"
            )

        # Update registration
        updated_registration = update_event_registration(db, registration_id, registration_update)

        # Log admin action
        print(f"Admin {current_user.email} updated registration {registration_id}")

        # Get enhanced details for response
        user = db.query(User).filter(User.id == updated_registration.user_id).first()
        event = db.query(Event).filter(Event.id == updated_registration.event_id).first()
        ticket = db.query(EventTicket).filter(EventTicket.id == updated_registration.ticket_id).first()
        # Get payment info with error handling
        payment_status = None
        payment_reference = None
        try:
            payment = db.query(EventPayment).filter(
                EventPayment.registration_id == registration_id
            ).first()

            if payment:
                payment_status = payment.status.value if hasattr(payment.status, 'value') else str(payment.status)
                payment_reference = payment.gateway_transaction_id
        except Exception as e:
            print(f"Error querying payment data: {e}")
            payment_status = "unknown"

        return AdminEventRegistrationOut(
            **updated_registration.__dict__,
            user_email=user.email if user else None,
            user_name=user.username if user else None,
            user_phone=user.phone if user else None,
            event_title=event.title if event else None,
            event_location=event.location if event else None,
            ticket_name=ticket.name if ticket else None,
            payment_status=payment_status,
            payment_reference=payment_reference
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update registration: {str(e)}"
        )


@router.delete("/registrations/{registration_id}")
def delete_registration_admin(
    registration_id: UUID,
    reason: str = Query("Admin deletion", description="Reason for deletion"),
    refund: bool = Query(False, description="Process refund if payment exists"),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("admin"))
):
    """
    Delete an event registration with admin privileges.

    Admin can delete any registration and optionally process refunds.
    """
    current_user = get_current_user(token, db)

    try:
        registration = get_registration_by_id(db, registration_id, current_user.id)
        if not registration:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Registration not found"
            )

        # Get payment info before deletion
        payment = db.query(EventPayment).filter(
            EventPayment.registration_id == registration_id
        ).first()

        refund_processed = False
        refund_amount = 0.0

        if payment and refund and payment.status == "COMPLETED":
            # Mark payment for refund (actual refund would be processed by payment gateway)
            payment.status = "REFUNDED"
            payment.refund_reason = reason
            payment.refunded_at = datetime.now(timezone.utc)
            refund_processed = True
            refund_amount = float(payment.amount)

        # Update ticket availability
        if registration.ticket_id:
            ticket = db.query(EventTicket).filter(EventTicket.id == registration.ticket_id).first()
            if ticket:
                ticket.sold_quantity = max(0, ticket.sold_quantity - registration.quantity)
                ticket.available_quantity = ticket.total_quantity - ticket.sold_quantity

        # Delete registration
        delete_event_registration(db, registration_id)

        # Log admin action
        print(f"Admin {current_user.email} deleted registration {registration_id} - Reason: {reason}")

        return {
            "message": "Registration deleted successfully",
            "registration_id": registration_id,
            "reason": reason,
            "refund_processed": refund_processed,
            "refund_amount": refund_amount,
            "deleted_by": current_user.email,
            "deleted_at": datetime.now(timezone.utc).isoformat()
        }

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete registration: {str(e)}"
        )
