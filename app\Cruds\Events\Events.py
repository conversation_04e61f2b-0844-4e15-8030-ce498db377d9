"""
Event CRUD Operations for EduFair Platform

This module contains all CRUD operations for the Event Management system including:
- Event creation, reading, updating, and deletion
- Event filtering and searching
- Event registration management
- Event analytics and statistics
"""

import uuid
from datetime import datetime, timezone
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, or_, func, desc
from fastapi import HTTPException, status
from typing import List, Optional, Dict, Any
from uuid import UUID

# Import Models
from Models.Events import (
    Event, EventSpeaker, EventTicket, EventRegistration,
    EventPayment, EventFeedback, EventCalendar, EventAnalytics,
    EventStatusEnum, EventCategoryEnum, RegistrationStatusEnum, PaymentStatusEnum
)
from Models.users import User, UserTypeEnum

# Import Schemas
from Schemas.Events.Events import (
    EventCreate, EventUpdate, EventOut, EventDetailedOut, EventMinimalOut,
    EventTicketCreateForEvent, EventSpeakerCreate, EventSpeakerUpdate, EventSpeakerOut,
    EventTicketCreate, EventTicketUpdate, EventTicketOut,
    EventRegistrationCreate, EventRegistrationOut,
    CalendarEventOut
)
from Schemas.Events.EventManagement import (
    EventListFilter, EventAnalyticsOut
)


# ==================== EVENT CRUD OPERATIONS ====================

def create_event(db: Session, event_data: EventCreate, organizer_id: UUID) -> EventOut:
    """Create a new event with optional tickets."""
    try:
        # Verify organizer exists and is an institute
        organizer = db.query(User).filter(
            User.id == organizer_id,
            User.user_type == UserTypeEnum.institute
        ).first()

        if not organizer:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Institute organizer not found"
            )

        # Validate exam_id if provided (for competitions)
        if event_data.exam_id:
            from Models.Exam import Exam
            from Models.Questions import Question

            # Check if exam exists and belongs to the institute
            exam = db.query(Exam).filter(Exam.id == event_data.exam_id).first()
            if not exam:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Exam not found"
                )

            # Verify the exam belongs to the institute by checking if any questions were created by the institute
            institute_questions = db.query(Question).filter(
                Question.teacher_id == organizer_id,
                Question.exams.any(id=event_data.exam_id)
            ).first()

            if not institute_questions:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="You can only use exams that belong to your institute"
                )

        # Create event data (exclude tickets from event creation)
        event_dict = event_data.model_dump(exclude={'tickets'})
        event_dict['organizer_id'] = organizer_id
        event_dict['institute_id'] = organizer_id  # Same as organizer for institutes

        db_event = Event(**event_dict)
        db.add(db_event)
        db.flush()  # Flush to get the event ID without committing

        # Create tickets if provided
        if event_data.tickets:
            from Models.Events import EventTicket

            for ticket_data in event_data.tickets:
                ticket_dict = ticket_data.model_dump()
                ticket_dict['event_id'] = db_event.id
                ticket_dict['sold_quantity'] = 0
                ticket_dict['available_quantity'] = ticket_dict.get('total_quantity', 0)

                db_ticket = EventTicket(**ticket_dict)
                db.add(db_ticket)

        db.commit()
        db.refresh(db_event)

        return db_event

    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create event: {str(e)}"
        )


def get_event_by_id(db: Session, event_id: UUID, user_id: Optional[UUID] = None) -> EventDetailedOut:
    """Get event by ID with detailed information."""
    event = db.query(Event).options(
        joinedload(Event.speakers),
        joinedload(Event.tickets),
        joinedload(Event.organizer),
        joinedload(Event.institute)
    ).filter(Event.id == event_id).first()
    
    if not event:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Event not found"
        )
    
    # Check if user is registered (if user_id provided)
    is_registered = False
    registration_status = None
    if user_id:
        registration = db.query(EventRegistration).filter(
            EventRegistration.event_id == event_id,
            EventRegistration.user_id == user_id
        ).first()
        if registration:
            is_registered = True
            registration_status = registration.status
    
    # Get registration count
    total_registrations = db.query(EventRegistration).filter(
        EventRegistration.event_id == event_id,
        EventRegistration.status.in_([RegistrationStatusEnum.CONFIRMED, RegistrationStatusEnum.ATTENDED])
    ).count()
    
    # Calculate available tickets
    total_tickets = sum(ticket.total_quantity or 0 for ticket in event.tickets if ticket.total_quantity)
    sold_tickets = db.query(func.sum(EventRegistration.quantity)).filter(
        EventRegistration.event_id == event_id,
        EventRegistration.status.in_([RegistrationStatusEnum.CONFIRMED, RegistrationStatusEnum.ATTENDED])
    ).scalar() or 0
    
    available_tickets = max(0, total_tickets - sold_tickets) if total_tickets > 0 else 0
    
    # Build response
    event_dict = {
        **event.__dict__,
        'speakers': event.speakers,
        'tickets': event.tickets,
        'organizer': event.organizer,
        'institute': event.institute,
        'total_registrations': total_registrations,
        'available_tickets': available_tickets,
        'is_registered': is_registered,
        'registration_status': registration_status
    }
    
    return EventDetailedOut(**event_dict)


def get_all_events(
    db: Session,
    skip: int = 0,
    limit: int = 100,
    filters: Optional[EventListFilter] = None,
    user_id: Optional[UUID] = None
) -> List[EventOut]:
    """Get all events with optional filtering."""
    query = db.query(Event).options(
        joinedload(Event.organizer)
    )
    
    # Apply filters
    if filters:
        if filters.category:
            query = query.filter(Event.category == filters.category)
        if filters.location:
            query = query.filter(Event.location.ilike(f'%{filters.location}%'))
        if filters.start_date:
            query = query.filter(Event.start_datetime >= filters.start_date)
        if filters.end_date:
            query = query.filter(Event.end_datetime <= filters.end_date)
        if filters.is_featured is not None:
            query = query.filter(Event.is_featured == filters.is_featured)
        if filters.is_virtual is not None:
            if filters.is_virtual:
                query = query.filter(Event.location.ilike('%online%'))
            else:
                query = query.filter(~Event.location.ilike('%online%'))
    
    # Only show published events for non-organizers
    if not user_id or not _is_event_organizer(db, user_id):
        query = query.filter(Event.status == EventStatusEnum.PUBLISHED)
    
    events = query.order_by(desc(Event.start_datetime)).offset(skip).limit(limit).all()
    return events


def update_event(db: Session, event_id: UUID, event_data: EventUpdate, user_id: UUID) -> EventOut:
    """Update an existing event."""
    event = db.query(Event).filter(Event.id == event_id).first()
    
    if not event:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Event not found"
        )
    
    # Check if user is authorized to update
    if event.organizer_id != user_id and event.institute_id != user_id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to update this event"
        )
    
    try:
        # Update event fields
        update_data = event_data.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(event, field, value)
        
        event.updated_at = datetime.now(timezone.utc)
        db.commit()
        db.refresh(event)
        
        return event
        
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update event: {str(e)}"
        )


def delete_event(db: Session, event_id: UUID, user_id: UUID) -> None:
    """Delete an event."""
    event = db.query(Event).filter(Event.id == event_id).first()
    
    if not event:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Event not found"
        )
    
    # Check if user is authorized to delete
    if event.organizer_id != user_id and event.institute_id != user_id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to delete this event"
        )
    
    # Check if event has registrations
    registrations_count = db.query(EventRegistration).filter(
        EventRegistration.event_id == event_id
    ).count()
    
    if registrations_count > 0:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Cannot delete event with existing registrations"
        )
    
    try:
        db.delete(event)
        db.commit()
        
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete event: {str(e)}"
        )


def get_events_by_organizer(db: Session, organizer_id: UUID, skip: int = 0, limit: int = 100) -> List[EventOut]:
    """Get all events created by a specific organizer."""
    events = db.query(Event).filter(
        Event.organizer_id == organizer_id
    ).order_by(desc(Event.created_at)).offset(skip).limit(limit).all()

    return events


def get_featured_events(db: Session, limit: int = 10) -> List[EventMinimalOut]:
    """Get featured events for homepage slider."""
    events = db.query(Event).filter(
        Event.is_featured == True,
        Event.status == EventStatusEnum.PUBLISHED,
        Event.start_datetime > datetime.now(timezone.utc)
    ).order_by(Event.start_datetime).limit(limit).all()

    return events


def get_events_by_category(db: Session, category: EventCategoryEnum, limit: int = 20) -> List[EventMinimalOut]:
    """Get events by category."""
    events = db.query(Event).filter(
        Event.category == category,
        Event.status == EventStatusEnum.PUBLISHED
    ).order_by(desc(Event.start_datetime)).limit(limit).all()

    return events


# ==================== EVENT LOCATION CRUD OPERATIONS ====================
# EventLocation CRUD operations removed - location is now a simple string field


# ==================== EVENT SPEAKER CRUD OPERATIONS ====================

def create_event_speaker(db: Session, speaker_data: EventSpeakerCreate) -> EventSpeakerOut:
    """Create a new event speaker."""
    try:
        db_speaker = EventSpeaker(**speaker_data.model_dump())
        db.add(db_speaker)
        db.commit()
        db.refresh(db_speaker)
        return db_speaker

    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create speaker: {str(e)}"
        )


def get_event_speaker_by_id(db: Session, speaker_id: UUID) -> EventSpeakerOut:
    """Get event speaker by ID."""
    speaker = db.query(EventSpeaker).filter(EventSpeaker.id == speaker_id).first()

    if not speaker:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Event speaker not found"
        )

    return speaker


def get_all_event_speakers(db: Session, skip: int = 0, limit: int = 100) -> List[EventSpeakerOut]:
    """Get all event speakers."""
    speakers = db.query(EventSpeaker).offset(skip).limit(limit).all()
    return speakers


def update_event_speaker(db: Session, speaker_id: UUID, speaker_data: EventSpeakerUpdate) -> EventSpeakerOut:
    """Update an existing event speaker."""
    speaker = db.query(EventSpeaker).filter(EventSpeaker.id == speaker_id).first()

    if not speaker:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Event speaker not found"
        )

    try:
        update_data = speaker_data.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(speaker, field, value)

        speaker.updated_at = datetime.now(timezone.utc)
        db.commit()
        db.refresh(speaker)

        return speaker

    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update speaker: {str(e)}"
        )


def delete_event_speaker(db: Session, speaker_id: UUID) -> None:
    """Delete an event speaker."""
    speaker = db.query(EventSpeaker).filter(EventSpeaker.id == speaker_id).first()

    if not speaker:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Event speaker not found"
        )

    try:
        db.delete(speaker)
        db.commit()

    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete speaker: {str(e)}"
        )


# ==================== HELPER FUNCTIONS ====================

def _is_event_organizer(db: Session, user_id: UUID) -> bool:
    """Check if user is an event organizer (institute)."""
    user = db.query(User).filter(
        User.id == user_id,
        User.user_type == UserTypeEnum.institute
    ).first()
    return user is not None
