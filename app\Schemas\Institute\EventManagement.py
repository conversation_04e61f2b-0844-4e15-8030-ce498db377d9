from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field, EmailStr
from datetime import datetime
from uuid import UUID
from decimal import Decimal


class InstituteEventCreate(BaseModel):
    """Create event for institute"""
    title: str = Field(..., min_length=3, max_length=200, description="Event title")
    description: str = Field(..., min_length=10, description="Event description")
    event_type: str = Field(..., description="Type of event (academic, workshop, competition, etc.)")
    category_id: Optional[UUID] = Field(None, description="Event category ID")
    start_datetime: datetime = Field(..., description="Event start date and time")
    end_datetime: datetime = Field(..., description="Event end date and time")
    location: Dict[str, Any] = Field(..., description="Event location details")
    registration_required: bool = Field(default=True, description="Whether registration is required")
    registration_deadline: Optional[datetime] = Field(None, description="Registration deadline")
    is_public: bool = Field(default=True, description="Whether event is public")
    target_audience: List[str] = Field(default=[], description="Target audience")
    organizers: List[UUID] = Field(default=[], description="Event organizer IDs")
    budget: Optional[Decimal] = Field(None, description="Event budget")
    expected_attendees: Optional[int] = Field(None, description="Expected number of attendees")
    max_attendees: Optional[int] = Field(None, description="Maximum attendees allowed")
    banner_image_url: Optional[str] = Field(None, description="Event banner image URL")
    tags: List[str] = Field(default=[], description="Event tags")


class InstituteEventOut(BaseModel):
    """Institute event output"""
    id: UUID
    title: str
    description: str
    event_type: str
    status: str
    start_datetime: datetime
    end_datetime: datetime
    location: Dict[str, Any]
    registration_required: bool
    is_public: bool
    target_audience: List[str]
    budget: Optional[Decimal]
    expected_attendees: Optional[int]
    max_attendees: Optional[int]
    current_attendees: int = Field(default=0, description="Current number of registered attendees")
    banner_image_url: Optional[str]
    tags: List[str]
    created_at: datetime
    updated_at: datetime


class EventAttendeeOut(BaseModel):
    """Event attendee information"""
    id: UUID
    user_id: UUID
    first_name: str
    last_name: str
    email: EmailStr
    phone: Optional[str]
    registration_date: datetime
    attendance_status: str = Field(..., description="Registration status (confirmed, pending, cancelled)")
    check_in_status: str = Field(default="not_checked_in", description="Check-in status")
    check_in_time: Optional[datetime] = Field(None, description="Check-in timestamp")
    ticket_type: Optional[str] = Field(None, description="Ticket type")
    quantity: int = Field(default=1, description="Number of tickets")
    special_requirements: Optional[str] = Field(None, description="Special requirements")
    attendee_info: Optional[Dict[str, Any]] = Field(None, description="Additional attendee information")


class EventAttendeesResponse(BaseModel):
    """Response for event attendees"""
    attendees: List[EventAttendeeOut]
    total: int
    confirmed: int
    pending: int
    checked_in: int
    no_show: int


class EventRegistrationRequest(BaseModel):
    """Request to register user for event"""
    user_id: UUID = Field(..., description="User ID to register")
    ticket_id: Optional[UUID] = Field(None, description="Ticket type ID")
    quantity: int = Field(default=1, ge=1, description="Number of tickets")
    attendee_info: Optional[Dict[str, Any]] = Field(None, description="Additional attendee information")
    special_requirements: Optional[str] = Field(None, description="Special requirements")
    emergency_contact: Optional[Dict[str, str]] = Field(None, description="Emergency contact information")


class EventCheckInRequest(BaseModel):
    """Request to check in attendee"""
    check_in_time: Optional[datetime] = Field(None, description="Check-in time (defaults to now)")
    notes: Optional[str] = Field(None, description="Check-in notes")


class EventReminderRequest(BaseModel):
    """Request to send event reminder"""
    message: str = Field(..., description="Reminder message")
    send_to: str = Field(default="all", description="Who to send to (all, confirmed, pending)")
    send_via: List[str] = Field(default=["email"], description="Communication channels")
    schedule_time: Optional[datetime] = Field(None, description="When to send (defaults to now)")


# Event categories are now fixed enums (WORKSHOP, CONFERENCE, WEBINAR, COMPETITION)
# EventCategoryCreate and EventCategoryOut schemas removed


class EventTemplateCreate(BaseModel):
    """Create event template"""
    name: str = Field(..., description="Template name")
    description: str = Field(..., description="Template description")
    event_type: str = Field(..., description="Default event type")
    default_duration_hours: int = Field(default=2, description="Default event duration in hours")
    default_settings: Dict[str, Any] = Field(default={}, description="Default event settings")
    target_audience: List[str] = Field(default=[], description="Default target audience")
    required_fields: List[str] = Field(default=[], description="Required fields for events using this template")


class EventTemplateOut(BaseModel):
    """Event template output"""
    id: UUID
    name: str
    description: str
    event_type: str
    default_duration_hours: int
    default_settings: Dict[str, Any]
    target_audience: List[str]
    required_fields: List[str]
    usage_count: int = Field(default=0, description="Number of times template has been used")
    created_at: datetime


class EventAnalyticsOut(BaseModel):
    """Event analytics data"""
    event_id: UUID
    event_title: str
    total_registrations: int
    confirmed_registrations: int
    actual_attendees: int
    attendance_rate: float = Field(..., description="Percentage of confirmed attendees who showed up")
    registration_rate: float = Field(..., description="Percentage of expected attendees who registered")
    no_show_rate: float = Field(..., description="Percentage of confirmed attendees who didn't show up")
    peak_registration_day: Optional[str] = Field(None, description="Day with most registrations")
    average_registration_time: Optional[float] = Field(None, description="Average time from event creation to registration")
    demographic_breakdown: Dict[str, Any] = Field(default={}, description="Attendee demographic breakdown")
    feedback_summary: Optional[Dict[str, Any]] = Field(None, description="Event feedback summary")


class EventAttendanceReportOut(BaseModel):
    """Event attendance report"""
    event_id: UUID
    event_title: str
    event_date: datetime
    total_registered: int
    total_attended: int
    attendance_percentage: float
    on_time_arrivals: int
    late_arrivals: int
    early_departures: int
    full_attendance: int
    attendee_breakdown: Dict[str, int] = Field(default={}, description="Breakdown by attendee type")
    hourly_checkins: List[Dict[str, Any]] = Field(default=[], description="Check-ins by hour")


class EventSuccessMetricsOut(BaseModel):
    """Event success metrics"""
    total_events: int
    successful_events: int = Field(..., description="Events with >80% attendance rate")
    average_attendance_rate: float
    average_satisfaction_score: Optional[float]
    total_attendees: int
    repeat_attendee_rate: float = Field(..., description="Percentage of attendees who attend multiple events")
    event_completion_rate: float = Field(..., description="Percentage of planned events that were completed")
    average_event_rating: Optional[float]


class EventFeedbackSurveyCreate(BaseModel):
    """Create feedback survey for event"""
    survey_title: str = Field(..., description="Survey title")
    questions: List[Dict[str, Any]] = Field(..., description="Survey questions")
    is_anonymous: bool = Field(default=True, description="Whether survey is anonymous")
    send_immediately: bool = Field(default=False, description="Send survey immediately after event")
    send_delay_hours: int = Field(default=24, description="Hours to wait before sending survey")
    reminder_enabled: bool = Field(default=True, description="Send reminder to non-respondents")


class EventFeedbackResultsOut(BaseModel):
    """Event feedback survey results"""
    survey_id: UUID
    event_id: UUID
    event_title: str
    total_sent: int
    total_responses: int
    response_rate: float
    average_rating: Optional[float]
    satisfaction_score: Optional[float]
    question_results: List[Dict[str, Any]] = Field(default=[], description="Results for each question")
    sentiment_analysis: Optional[Dict[str, Any]] = Field(None, description="Sentiment analysis of text responses")
    recommendations: List[str] = Field(default=[], description="AI-generated recommendations based on feedback")


class InstituteEventsResponse(BaseModel):
    """Response for institute events list"""
    events: List[InstituteEventOut]
    total: int
    upcoming: int
    ongoing: int
    completed: int
    cancelled: int


class EventPublishRequest(BaseModel):
    """Request to publish event"""
    publish_immediately: bool = Field(default=True, description="Publish immediately or schedule")
    publish_at: Optional[datetime] = Field(None, description="When to publish (if not immediate)")
    notification_message: Optional[str] = Field(None, description="Custom notification message")
    notify_followers: bool = Field(default=True, description="Notify institute followers")


class EventCancelRequest(BaseModel):
    """Request to cancel event"""
    cancellation_reason: str = Field(..., description="Reason for cancellation")
    refund_policy: str = Field(default="full", description="Refund policy (full, partial, none)")
    notification_message: str = Field(..., description="Message to send to attendees")
    notify_immediately: bool = Field(default=True, description="Send notifications immediately")
