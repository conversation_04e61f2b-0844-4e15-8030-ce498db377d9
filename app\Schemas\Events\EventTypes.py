"""
Event Types Schemas for Enhanced Event System
"""
from pydantic import BaseModel, Field
from typing import Optional, Dict, Any, List
from uuid import UUID
from datetime import datetime
from enum import Enum

# Import enums from models
from Models.Events import EventTypeEnum, CollaborationLevelEnum


class EventTypeBase(BaseModel):
    """Base schema for event types"""
    name: str = Field(..., max_length=100, description="Event type name (workshop, conference, webinar, competition)")
    display_name: str = Field(..., max_length=100, description="Display name for the event type")
    description: Optional[str] = Field(None, description="Description of the event type")
    default_settings: Optional[Dict[str, Any]] = Field(None, description="Default settings for this event type")
    is_active: bool = Field(True, description="Whether this event type is active")


class EventTypeCreate(EventTypeBase):
    """Schema for creating event types"""
    pass


class EventTypeUpdate(BaseModel):
    """Schema for updating event types"""
    display_name: Optional[str] = Field(None, max_length=100)
    description: Optional[str] = None
    default_settings: Optional[Dict[str, Any]] = None
    is_active: Optional[bool] = None


class EventTypeOut(EventTypeBase):
    """Schema for event type output"""
    id: UUID
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class EventTypeConfigurationBase(BaseModel):
    """Base schema for event type configurations"""
    event_type_id: UUID = Field(..., description="Event type ID")
    institute_id: UUID = Field(..., description="Institute ID")
    custom_settings: Optional[Dict[str, Any]] = Field(None, description="Institute-specific settings")
    is_enabled: bool = Field(True, description="Whether this event type is enabled for the institute")


class EventTypeConfigurationCreate(EventTypeConfigurationBase):
    """Schema for creating event type configurations"""
    pass


class EventTypeConfigurationUpdate(BaseModel):
    """Schema for updating event type configurations"""
    custom_settings: Optional[Dict[str, Any]] = None
    is_enabled: Optional[bool] = None


class EventTypeConfigurationOut(EventTypeConfigurationBase):
    """Schema for event type configuration output"""
    id: UUID
    created_at: datetime
    updated_at: datetime
    
    # Related data
    event_type: Optional[EventTypeOut] = None

    class Config:
        from_attributes = True


# Enhanced Event Schemas with Event Types
class EnhancedEventBase(BaseModel):
    """Enhanced base schema for events with new event type system"""
    title: str = Field(..., max_length=300, description="Event title")
    description: Optional[str] = Field(None, description="Event description")
    short_description: Optional[str] = Field(None, max_length=500)
    banner_image_url: Optional[str] = Field(None, max_length=500)
    gallery_images: Optional[List[str]] = Field(None, description="Gallery image URLs")
    
    # Timing
    start_datetime: datetime = Field(..., description="Event start time")
    end_datetime: datetime = Field(..., description="Event end time")
    registration_start: Optional[datetime] = Field(None)
    registration_end: Optional[datetime] = Field(None)
    
    # Event type and categorization
    event_type: EventTypeEnum = Field(..., description="Type of event")
    category_id: UUID = Field(..., description="Event category ID")
    location_id: Optional[UUID] = Field(None, description="Event location ID")
    
    # Enhanced collaboration settings
    requires_collaboration: bool = Field(False, description="Whether event requires mentor-institute collaboration")
    collaboration_required_level: CollaborationLevelEnum = Field(
        CollaborationLevelEnum.BASIC, 
        description="Level of collaboration required"
    )
    
    # Basic settings
    is_public: bool = Field(True, description="Whether event is public")
    requires_approval: bool = Field(False, description="Whether registration requires approval")
    max_attendees: Optional[int] = Field(None, ge=1, description="Maximum number of attendees")
    min_attendees: Optional[int] = Field(None, ge=1, description="Minimum number of attendees")
    
    # Additional info
    agenda: Optional[List[Dict[str, Any]]] = Field(None, description="Event agenda")
    requirements: Optional[str] = Field(None, description="Prerequisites or requirements")
    tags: Optional[List[str]] = Field(None, description="Event tags")
    external_links: Optional[Dict[str, str]] = Field(None, description="External links")


class EnhancedEventCreate(EnhancedEventBase):
    """Schema for creating enhanced events"""
    organizer_id: Optional[UUID] = Field(None, description="Organizer ID (auto-filled from current user)")
    institute_id: Optional[UUID] = Field(None, description="Institute ID (auto-filled for institute users)")


class EnhancedEventUpdate(BaseModel):
    """Schema for updating enhanced events"""
    title: Optional[str] = Field(None, max_length=300)
    description: Optional[str] = None
    short_description: Optional[str] = Field(None, max_length=500)
    banner_image_url: Optional[str] = Field(None, max_length=500)
    gallery_images: Optional[List[str]] = None
    start_datetime: Optional[datetime] = None
    end_datetime: Optional[datetime] = None
    registration_start: Optional[datetime] = None
    registration_end: Optional[datetime] = None
    event_type: Optional[EventTypeEnum] = None
    category_id: Optional[UUID] = None
    location_id: Optional[UUID] = None
    requires_collaboration: Optional[bool] = None
    collaboration_required_level: Optional[CollaborationLevelEnum] = None
    is_public: Optional[bool] = None
    requires_approval: Optional[bool] = None
    max_attendees: Optional[int] = Field(None, ge=1)
    min_attendees: Optional[int] = Field(None, ge=1)
    agenda: Optional[List[Dict[str, Any]]] = None
    requirements: Optional[str] = None
    tags: Optional[List[str]] = None
    external_links: Optional[Dict[str, str]] = None


class EnhancedEventOut(EnhancedEventBase):
    """Schema for enhanced event output"""
    id: UUID
    organizer_id: UUID
    institute_id: UUID
    status: str  # EventStatusEnum
    is_featured: bool
    is_competition: bool
    created_at: datetime
    updated_at: datetime
    
    # Related data
    event_type_info: Optional[EventTypeOut] = None
    category: Optional[Dict[str, Any]] = None
    location: Optional[Dict[str, Any]] = None
    organizer: Optional[Dict[str, Any]] = None
    institute: Optional[Dict[str, Any]] = None

    class Config:
        from_attributes = True


class EventTypeListResponse(BaseModel):
    """Response schema for event type lists"""
    event_types: List[EventTypeOut]
    total: int
    
    
class EventsByTypeResponse(BaseModel):
    """Response schema for events grouped by type"""
    event_type: EventTypeEnum
    events: List[EnhancedEventOut]
    count: int


class EventTypeSummary(BaseModel):
    """Summary schema for event types with counts"""
    event_type: EventTypeEnum
    display_name: str
    count: int
    upcoming_count: int
    active_count: int


class EventTypeAnalytics(BaseModel):
    """Analytics schema for event types"""
    summary: List[EventTypeSummary]
    total_events: int
    total_upcoming: int
    most_popular_type: Optional[EventTypeEnum] = None
    growth_trends: Optional[Dict[str, Any]] = None
