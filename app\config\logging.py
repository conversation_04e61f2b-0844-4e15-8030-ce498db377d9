"""
Structured Logging Configuration for EduFair
Provides comprehensive logging with security audit trails
"""

import logging
import logging.config
import sys
import os
from datetime import datetime
from typing import Dict, Any
import json


class SecurityAuditFilter(logging.Filter):
    """Filter for security-related log events"""
    
    def filter(self, record):
        # Only pass through security-related events
        return hasattr(record, 'security_event') and record.security_event


class J<PERSON>NFormatter(logging.Formatter):
    """JSON formatter for structured logging"""
    
    def format(self, record):
        log_entry = {
            'timestamp': datetime.utcnow().isoformat() + 'Z',
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno,
        }
        
        # Add extra fields if present
        if hasattr(record, 'request_id'):
            log_entry['request_id'] = record.request_id
        
        if hasattr(record, 'user_id'):
            log_entry['user_id'] = record.user_id
        
        if hasattr(record, 'method'):
            log_entry['http_method'] = record.method
        
        if hasattr(record, 'url'):
            log_entry['url'] = record.url
        
        if hasattr(record, 'client_ip'):
            log_entry['client_ip'] = record.client_ip
        
        if hasattr(record, 'user_agent'):
            log_entry['user_agent'] = record.user_agent
        
        if hasattr(record, 'status_code'):
            log_entry['status_code'] = record.status_code
        
        if hasattr(record, 'security_event'):
            log_entry['security_event'] = record.security_event
        
        if hasattr(record, 'validation_errors'):
            log_entry['validation_errors'] = record.validation_errors
        
        if hasattr(record, 'exception_type'):
            log_entry['exception_type'] = record.exception_type
        
        if hasattr(record, 'traceback'):
            log_entry['traceback'] = record.traceback
        
        # Add exception info if present
        if record.exc_info:
            log_entry['exception'] = self.formatException(record.exc_info)
        
        return json.dumps(log_entry, ensure_ascii=False)


class SecurityJSONFormatter(JSONFormatter):
    """Specialized JSON formatter for security audit logs"""
    
    def format(self, record):
        log_entry = {
            'timestamp': datetime.utcnow().isoformat() + 'Z',
            'event_type': 'SECURITY_AUDIT',
            'level': record.levelname,
            'message': record.getMessage(),
            'severity': self._get_security_severity(record),
        }
        
        # Security-specific fields
        if hasattr(record, 'request_id'):
            log_entry['request_id'] = record.request_id
        
        if hasattr(record, 'user_id'):
            log_entry['user_id'] = record.user_id
        
        if hasattr(record, 'method'):
            log_entry['http_method'] = record.method
        
        if hasattr(record, 'url'):
            log_entry['url'] = record.url
        
        if hasattr(record, 'client_ip'):
            log_entry['client_ip'] = record.client_ip
        
        if hasattr(record, 'user_agent'):
            log_entry['user_agent'] = record.user_agent
        
        if hasattr(record, 'status_code'):
            log_entry['status_code'] = record.status_code
            log_entry['threat_level'] = self._assess_threat_level(record.status_code)
        
        return json.dumps(log_entry, ensure_ascii=False)
    
    def _get_security_severity(self, record):
        """Determine security severity based on log level and content"""
        if record.levelname == 'CRITICAL':
            return 'HIGH'
        elif record.levelname == 'ERROR':
            return 'MEDIUM'
        elif record.levelname == 'WARNING':
            return 'LOW'
        else:
            return 'INFO'
    
    def _assess_threat_level(self, status_code):
        """Assess threat level based on HTTP status code"""
        if status_code == 401:
            return 'AUTHENTICATION_FAILURE'
        elif status_code == 403:
            return 'AUTHORIZATION_FAILURE'
        elif status_code == 429:
            return 'RATE_LIMIT_VIOLATION'
        elif status_code >= 500:
            return 'SYSTEM_ERROR'
        else:
            return 'LOW'


def setup_logging(log_level: str = "INFO", log_dir: str = "logs") -> Dict[str, logging.Logger]:
    """
    Setup comprehensive logging configuration
    
    Args:
        log_level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_dir: Directory to store log files
    
    Returns:
        Dictionary of configured loggers
    """
    
    # Create logs directory if it doesn't exist
    os.makedirs(log_dir, exist_ok=True)
    
    # Logging configuration
    LOGGING_CONFIG = {
        'version': 1,
        'disable_existing_loggers': False,
        'formatters': {
            'standard': {
                'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                'datefmt': '%Y-%m-%d %H:%M:%S'
            },
            'detailed': {
                'format': '%(asctime)s - %(name)s - %(levelname)s - %(module)s - %(funcName)s:%(lineno)d - %(message)s',
                'datefmt': '%Y-%m-%d %H:%M:%S'
            },
            'json': {
                '()': JSONFormatter,
            },
            'security_json': {
                '()': SecurityJSONFormatter,
            }
        },
        'filters': {
            'security_filter': {
                '()': SecurityAuditFilter,
            }
        },
        'handlers': {
            'console': {
                'level': log_level,
                'class': 'logging.StreamHandler',
                'formatter': 'standard',
                'stream': sys.stdout
            },
            'file_app': {
                'level': log_level,
                'class': 'logging.handlers.RotatingFileHandler',
                'formatter': 'json',
                'filename': f'{log_dir}/app.log',
                'maxBytes': 10485760,  # 10MB
                'backupCount': 5,
                'encoding': 'utf8'
            },
            'file_error': {
                'level': 'ERROR',
                'class': 'logging.handlers.RotatingFileHandler',
                'formatter': 'detailed',
                'filename': f'{log_dir}/error.log',
                'maxBytes': 10485760,  # 10MB
                'backupCount': 5,
                'encoding': 'utf8'
            },
            'file_security': {
                'level': 'WARNING',
                'class': 'logging.handlers.RotatingFileHandler',
                'formatter': 'security_json',
                'filename': f'{log_dir}/security_audit.log',
                'maxBytes': 10485760,  # 10MB
                'backupCount': 10,  # Keep more security logs
                'encoding': 'utf8',
                'filters': ['security_filter']
            },
            'file_access': {
                'level': 'INFO',
                'class': 'logging.handlers.RotatingFileHandler',
                'formatter': 'json',
                'filename': f'{log_dir}/access.log',
                'maxBytes': 10485760,  # 10MB
                'backupCount': 5,
                'encoding': 'utf8'
            }
        },
        'loggers': {
            '': {  # Root logger
                'handlers': ['console', 'file_app'],
                'level': log_level,
                'propagate': False
            },
            'security': {
                'handlers': ['file_security', 'console'],
                'level': 'WARNING',
                'propagate': False
            },
            'errors': {
                'handlers': ['file_error', 'console'],
                'level': 'ERROR',
                'propagate': False
            },
            'access': {
                'handlers': ['file_access'],
                'level': 'INFO',
                'propagate': False
            },
            'uvicorn.access': {
                'handlers': ['file_access'],
                'level': 'INFO',
                'propagate': False
            },
            'uvicorn.error': {
                'handlers': ['file_error', 'console'],
                'level': 'ERROR',
                'propagate': False
            },
            'sqlalchemy.engine': {
                'handlers': ['file_app'],
                'level': 'WARNING',  # Only log SQL warnings/errors
                'propagate': False
            }
        }
    }
    
    # Apply logging configuration
    logging.config.dictConfig(LOGGING_CONFIG)
    
    # Return configured loggers
    loggers = {
        'app': logging.getLogger(''),
        'security': logging.getLogger('security'),
        'errors': logging.getLogger('errors'),
        'access': logging.getLogger('access')
    }
    
    # Log startup message
    loggers['app'].info("Logging system initialized", extra={
        'log_level': log_level,
        'log_dir': log_dir,
        'startup': True
    })
    
    return loggers


def get_logger(name: str = None) -> logging.Logger:
    """Get a logger instance"""
    return logging.getLogger(name)


def log_security_event(
    event_type: str,
    message: str,
    user_id: str = None,
    request_id: str = None,
    client_ip: str = None,
    additional_data: Dict[str, Any] = None
):
    """
    Log a security event with structured data
    
    Args:
        event_type: Type of security event (AUTH_FAILURE, PERMISSION_DENIED, etc.)
        message: Human-readable message
        user_id: User ID if available
        request_id: Request ID for tracing
        client_ip: Client IP address
        additional_data: Additional structured data
    """
    security_logger = logging.getLogger('security')
    
    extra_data = {
        'security_event': True,
        'event_type': event_type
    }
    
    if user_id:
        extra_data['user_id'] = user_id
    if request_id:
        extra_data['request_id'] = request_id
    if client_ip:
        extra_data['client_ip'] = client_ip
    if additional_data:
        extra_data.update(additional_data)
    
    security_logger.warning(message, extra=extra_data)


def log_access_event(
    method: str,
    url: str,
    status_code: int,
    response_time: float,
    user_id: str = None,
    request_id: str = None,
    client_ip: str = None
):
    """
    Log an API access event
    
    Args:
        method: HTTP method
        url: Request URL
        status_code: HTTP status code
        response_time: Response time in milliseconds
        user_id: User ID if authenticated
        request_id: Request ID for tracing
        client_ip: Client IP address
    """
    access_logger = logging.getLogger('access')
    
    extra_data = {
        'method': method,
        'url': url,
        'status_code': status_code,
        'response_time_ms': response_time,
        'access_log': True
    }
    
    if user_id:
        extra_data['user_id'] = user_id
    if request_id:
        extra_data['request_id'] = request_id
    if client_ip:
        extra_data['client_ip'] = client_ip
    
    access_logger.info(f"{method} {url} - {status_code}", extra=extra_data)
