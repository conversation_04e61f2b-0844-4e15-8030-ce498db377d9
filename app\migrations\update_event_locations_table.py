"""
Migration to update event_locations table to match the model definition.

This migration adds missing columns to the event_locations table:
- description (Text)
- images (JSON)
- virtual_platform (String)
- virtual_link (String)
- virtual_meeting_id (String)
- virtual_passcode (String)
- is_active (Boolean)
"""

from sqlalchemy import text
from config.session import engine


def migrate_event_locations():
    """
    Execute the migration to update event_locations table.
    """
    
    with engine.connect() as connection:
        # Start transaction
        trans = connection.begin()
        
        try:
            print("Starting migration to update event_locations table...")
            
            # Check if event_locations table exists
            table_exists = connection.execute(text("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_name = 'event_locations'
                );
            """)).scalar()
            
            if not table_exists:
                print("event_locations table does not exist. Creating it...")
                # Create the table if it doesn't exist
                connection.execute(text("""
                    CREATE TABLE event_locations (
                        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                        name VARCHAR(200) NOT NULL,
                        description TEXT,
                        address TEXT,
                        city VARCHAR(100),
                        state VARCHAR(100),
                        country VARCHAR(100),
                        postal_code VARCHAR(20),
                        latitude NUMERIC(10, 8),
                        longitude NUMERIC(11, 8),
                        capacity INTEGER,
                        facilities JSON,
                        contact_info JSON,
                        images JSON,
                        is_virtual BOOLEAN DEFAULT FALSE,
                        virtual_platform VARCHAR(100),
                        virtual_link VARCHAR(500),
                        virtual_meeting_id VARCHAR(100),
                        virtual_passcode VARCHAR(50),
                        is_active BOOLEAN DEFAULT TRUE,
                        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
                    );
                """))
                print("event_locations table created successfully!")
            else:
                print("event_locations table exists. Checking for missing columns...")
                
                # Get existing columns
                existing_columns = connection.execute(text("""
                    SELECT column_name 
                    FROM information_schema.columns 
                    WHERE table_name = 'event_locations'
                """)).fetchall()
                
                existing_column_names = [row[0] for row in existing_columns]
                print(f"Existing columns: {existing_column_names}")
                
                # Define required columns and their definitions
                required_columns = {
                    'description': 'TEXT',
                    'images': 'JSON',
                    'virtual_platform': 'VARCHAR(100)',
                    'virtual_link': 'VARCHAR(500)',
                    'virtual_meeting_id': 'VARCHAR(100)',
                    'virtual_passcode': 'VARCHAR(50)',
                    'is_active': 'BOOLEAN DEFAULT TRUE'
                }
                
                # Add missing columns
                for column_name, column_def in required_columns.items():
                    if column_name not in existing_column_names:
                        print(f"Adding missing column: {column_name}")
                        connection.execute(text(f"""
                            ALTER TABLE event_locations 
                            ADD COLUMN IF NOT EXISTS {column_name} {column_def};
                        """))
                    else:
                        print(f"Column {column_name} already exists")
            
            # Commit transaction
            trans.commit()
            print("Migration completed successfully!")
            
        except Exception as e:
            # Rollback on error
            trans.rollback()
            print(f"Migration failed: {e}")
            raise


def rollback_migration():
    """
    Rollback the migration (remove added columns).
    """
    
    with engine.connect() as connection:
        trans = connection.begin()
        
        try:
            print("Starting rollback of event_locations migration...")
            
            # Remove added columns
            columns_to_remove = [
                'description', 'images', 'virtual_platform', 'virtual_link',
                'virtual_meeting_id', 'virtual_passcode', 'is_active'
            ]
            
            for column_name in columns_to_remove:
                print(f"Removing column: {column_name}")
                connection.execute(text(f"""
                    ALTER TABLE event_locations 
                    DROP COLUMN IF EXISTS {column_name};
                """))
            
            trans.commit()
            print("Rollback completed successfully!")
            
        except Exception as e:
            trans.rollback()
            print(f"Rollback failed: {e}")
            raise


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "rollback":
        rollback_migration()
    else:
        migrate_event_locations()
