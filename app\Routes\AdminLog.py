"""
Admin Log Routes for EduFair Platform

This module provides APIs for admin logging functionality.
"""

from fastapi import APIRouter, Depends, HTTPException, status, Request
from sqlalchemy.orm import Session
from uuid import UUID
from typing import List, Optional

# Import dependencies
from config.session import get_db
from config.deps import oauth2_scheme, get_current_user
from config.permission import require_type

# Import CRUD operations
from Cruds.AdminLog import (
    get_admin_log, get_admin_logs, get_admin_log_stats,
    get_resource_audit_trail, get_user_activity_logs,
    cleanup_old_logs, log_user_action
)

# Import schemas
from Schemas.AdminLog import (
    AdminLogResponse, AdminLogWithUser, AdminLogFilter,
    AdminLogListRequest, AdminLogListResponse, AdminLogStats,
    LogAction, ResourceType, AuditTrailRequest, AuditTrailResponse
)

# Import models
from Models.users import User

router = APIRouter()


# ==================== ADMIN LOG VIEWING ====================

@router.get("/", response_model=AdminLogListResponse)
def get_admin_log_list(
    page: int = 1,
    page_size: int = 50,
    user_id: Optional[UUID] = None,
    action: Optional[str] = None,
    resource_type: Optional[str] = None,
    resource_id: Optional[UUID] = None,
    date_from: Optional[str] = None,
    date_to: Optional[str] = None,
    ip_address: Optional[str] = None,
    search_query: Optional[str] = None,
    sort_by: str = "timestamp",
    sort_order: str = "desc",
    db: Session = Depends(get_db),
    current_user: User = Depends(require_type(["admin"]))
):
    """
    Get admin logs with filtering and pagination.
    
    Only admins can view admin logs.
    """
    # current_user is already injected via dependency
    
    try:
        # Build filters
        filters = AdminLogFilter()
        if user_id:
            filters.user_id = user_id
        if action:
            try:
                filters.action = LogAction(action)
            except ValueError:
                raise HTTPException(status_code=400, detail=f"Invalid action: {action}")
        if resource_type:
            try:
                filters.resource_type = ResourceType(resource_type)
            except ValueError:
                raise HTTPException(status_code=400, detail=f"Invalid resource type: {resource_type}")
        if resource_id:
            filters.resource_id = resource_id
        if date_from:
            try:
                from datetime import datetime
                filters.date_from = datetime.fromisoformat(date_from.replace('Z', '+00:00'))
            except ValueError:
                raise HTTPException(status_code=400, detail="Invalid date_from format")
        if date_to:
            try:
                from datetime import datetime
                filters.date_to = datetime.fromisoformat(date_to.replace('Z', '+00:00'))
            except ValueError:
                raise HTTPException(status_code=400, detail="Invalid date_to format")
        if ip_address:
            filters.ip_address = ip_address
        if search_query:
            filters.search_query = search_query
        
        # Get logs
        logs = get_admin_logs(db, filters, page, page_size, sort_by, sort_order)
        return logs
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get admin logs: {str(e)}"
        )


@router.get("/{log_id}", response_model=AdminLogWithUser)
def get_admin_log_detail(
    log_id: UUID,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_type(["admin"]))
):
    """
    Get a specific admin log entry.
    
    Only admins can view admin log details.
    """
    # current_user is already injected via dependency
    
    try:
        log_entry = get_admin_log(db, log_id)
        return log_entry
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get admin log: {str(e)}"
        )


# ==================== ADMIN LOG STATISTICS ====================

@router.get("/stats/summary", response_model=AdminLogStats)
def get_admin_log_statistics(
    days: int = 30,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_type(["admin"]))
):
    """
    Get admin log statistics.
    
    Returns summary statistics for the specified time period.
    """
    # current_user is already injected via dependency
    
    try:
        stats = get_admin_log_stats(db, days)
        return stats
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get admin log stats: {str(e)}"
        )


# ==================== AUDIT TRAIL ====================

@router.get("/audit/{resource_type}/{resource_id}", response_model=List[AdminLogWithUser])
def get_audit_trail(
    resource_type: str,
    resource_id: UUID,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_type(["admin"]))
):
    """
    Get audit trail for a specific resource.
    
    Returns all log entries related to the specified resource.
    """
    # current_user is already injected via dependency
    
    try:
        # Validate resource type
        try:
            resource_type_enum = ResourceType(resource_type)
        except ValueError:
            raise HTTPException(status_code=400, detail=f"Invalid resource type: {resource_type}")
        
        audit_trail = get_resource_audit_trail(db, resource_type_enum, resource_id)
        return audit_trail
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get audit trail: {str(e)}"
        )


@router.get("/user/{user_id}/activity", response_model=List[AdminLogResponse])
def get_user_activity(
    user_id: UUID,
    days: int = 30,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_type(["admin"]))
):
    """
    Get activity logs for a specific user.
    
    Returns all actions performed by the specified user.
    """
    # current_user is already injected via dependency
    
    try:
        # Check if user exists
        user = db.query(User).filter(User.id == user_id).first()
        if not user:
            raise HTTPException(status_code=404, detail="User not found")
        
        activity_logs = get_user_activity_logs(db, user_id, days)
        return activity_logs
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get user activity: {str(e)}"
        )


# ==================== ADMIN OPERATIONS ====================

@router.post("/cleanup")
def cleanup_admin_logs(
    request: Request,
    days_to_keep: int = 365,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_type(["admin"]))
):
    """
    Clean up old admin log entries.
    
    Deletes log entries older than the specified number of days.
    """
    # current_user is already injected via dependency
    
    try:
        if days_to_keep < 30:
            raise HTTPException(
                status_code=400, 
                detail="Cannot delete logs newer than 30 days"
            )
        
        deleted_count = cleanup_old_logs(db, days_to_keep)
        
        # Log the cleanup action
        log_user_action(
            db=db,
            action=LogAction.SYSTEM_MAINTENANCE,
            resource_type=ResourceType.SYSTEM,
            user_id=current_user.id,
            details={
                "action": "admin_log_cleanup",
                "days_to_keep": days_to_keep,
                "deleted_count": deleted_count,
                "admin_username": current_user.username
            },
            request=request
        )
        
        return {
            "message": f"Successfully deleted {deleted_count} old log entries",
            "deleted_count": deleted_count,
            "days_to_keep": days_to_keep
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to cleanup admin logs: {str(e)}"
        )


# ==================== EXPORT OPERATIONS ====================

@router.get("/export/csv")
def export_admin_logs_csv(
    request: Request,
    user_id: Optional[UUID] = None,
    action: Optional[str] = None,
    resource_type: Optional[str] = None,
    date_from: Optional[str] = None,
    date_to: Optional[str] = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_type(["admin"]))
):
    """
    Export admin logs to CSV format.
    
    Returns filtered admin logs in CSV format for download.
    """
    # current_user is already injected via dependency
    
    try:
        # Build filters (same as get_admin_log_list)
        filters = AdminLogFilter()
        if user_id:
            filters.user_id = user_id
        if action:
            try:
                filters.action = LogAction(action)
            except ValueError:
                raise HTTPException(status_code=400, detail=f"Invalid action: {action}")
        if resource_type:
            try:
                filters.resource_type = ResourceType(resource_type)
            except ValueError:
                raise HTTPException(status_code=400, detail=f"Invalid resource type: {resource_type}")
        if date_from:
            try:
                from datetime import datetime
                filters.date_from = datetime.fromisoformat(date_from.replace('Z', '+00:00'))
            except ValueError:
                raise HTTPException(status_code=400, detail="Invalid date_from format")
        if date_to:
            try:
                from datetime import datetime
                filters.date_to = datetime.fromisoformat(date_to.replace('Z', '+00:00'))
            except ValueError:
                raise HTTPException(status_code=400, detail="Invalid date_to format")
        
        # Get all logs (no pagination for export)
        logs = get_admin_logs(db, filters, page=1, page_size=10000)
        
        # Log the export action
        log_user_action(
            db=db,
            action=LogAction.ADMIN_EXPORT,
            resource_type=ResourceType.SYSTEM,
            user_id=current_user.id,
            details={
                "action": "admin_log_export",
                "format": "csv",
                "record_count": logs.total_count,
                "admin_username": current_user.username,
                "filters_applied": filters.dict() if filters else None
            },
            request=request
        )
        
        # Create CSV content
        import csv
        import io
        
        output = io.StringIO()
        writer = csv.writer(output)
        
        # Write header
        writer.writerow([
            'ID', 'Timestamp', 'User ID', 'Username', 'Action', 
            'Resource Type', 'Resource ID', 'IP Address', 'User Agent', 'Details'
        ])
        
        # Write data
        for log in logs.logs:
            writer.writerow([
                str(log.id),
                log.timestamp.isoformat(),
                str(log.user_id) if log.user_id else '',
                log.username or '',
                log.action,
                log.resource_type,
                str(log.resource_id) if log.resource_id else '',
                log.ip_address or '',
                log.user_agent or '',
                str(log.details) if log.details else ''
            ])
        
        # Return CSV response
        from fastapi.responses import Response
        
        csv_content = output.getvalue()
        output.close()
        
        return Response(
            content=csv_content,
            media_type="text/csv",
            headers={
                "Content-Disposition": "attachment; filename=admin_logs.csv"
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to export admin logs: {str(e)}"
        )
