"""
Middleware setup module for FastAPI application.

This module configures all middleware including CORS, error handlers, and custom middleware.
"""

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.exceptions import RequestValidationError
from sqlalchemy.exc import SQLAlchemyError
from pydantic import ValidationError

from middleware.error_handler import (
    global_exception_handler,
    handle_business_logic_error,
    BusinessLogicError
)
from middleware.admin_logging import add_admin_logging_middleware


def setup_middleware(app: FastAPI):
    """
    Configure all middleware for the FastAPI application.
    
    Args:
        app (FastAPI): The FastAPI application instance
    """
    # Add exception handlers
    app.add_exception_handler(Exception, global_exception_handler)
    app.add_exception_handler(RequestValidationError, global_exception_handler)
    app.add_exception_handler(ValidationError, global_exception_handler)
    app.add_exception_handler(SQLAlchemyError, global_exception_handler)
    app.add_exception_handler(BusinessLogicError, handle_business_logic_error)

    # Add CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],  # Configure with specific origins in production
        allow_credentials=True,
        allow_methods=["*"],  # Allow all HTTP methods
        allow_headers=["*"],  # Allow all headers
    )

    # Add admin logging middleware
    add_admin_logging_middleware(app, exclude_paths=[
        "/docs",
        "/redoc",
        "/openapi.json",
        "/favicon.ico",
        "/api/health",
        "/static"
    ])
