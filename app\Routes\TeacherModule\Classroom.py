from typing import List
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from Cruds.TeacherModule.Classroom import (
    create_classroom,
    get_all_students_not_in_classroom,
    get_all_students_in_teacher_classes,
    get_classroom_by_id,
    update_classroom,
    delete_classroom,
    remove_student_from_classroom,
    request_student_to_join_classroom,
    get_all_own_classes,
    get_all_students_in_classroom,
    student_accept_request,
    reject_student_request,
    get_all_classroom_students,
    get_all_requests_for_student,
    get_classroom_by_id_for_student,
    get_all_sent_requests_by_teacher,
    get_classroom_detailed_for_student
)
from Schemas.users import UserOut
from Schemas.TeacherModule.Classroom import (
    ClassroomCreate,
    ClassroomOutForStudent,
    ClassroomOutList,
    ClassroomOutLite,
    ClassroomUpdate,
    ClassroomOut,
    StudentClassroomCreate,
    StudentClassroomOut,
    StudentClassroomUpdate,
    StudentClassRequestCreate,
    StudentClassRequestOut,
    StudentClassRequestUpdate,
    ClassroomDetailedOut
)
from config.session import get_db
from config.permission import require_type
from config.subscription_permission import require_classroom_creation, update_usage_counter
from Schemas.Token import Token
from config.security import oauth2_scheme
from config.deps import get_current_user
from uuid import UUID
import uuid

router = APIRouter()


@router.post("/create", response_model=ClassroomOut)
def Create_Classroom_Endpoint(
    classroom: ClassroomCreate,
    db: Session = Depends(get_db),
    subscription_check = Depends(require_classroom_creation())
):
    current_user = subscription_check["user"]

    # Create classroom
    new_classroom = create_classroom(db, classroom, UUID(str(current_user.id)))

    # Update usage counter
    update_usage_counter(db, current_user, "classrooms_created", 1)

    return new_classroom

@router.get("/all", response_model=List[ClassroomOutList])
def Get_All_Own_Classes_Endpoint(
    db: Session = Depends(get_db),
    token = Depends(oauth2_scheme),
    _ = Depends(require_type("teacher"))
):
    current_user = get_current_user(token,db)
    return get_all_own_classes(db, UUID(str(current_user.id)))

@router.get("/my/students", response_model=List[UserOut])
def Get_All_Students_In_Teacher_Classes_Endpoint(
    skip: int = 0,
    limit: int = 100,
    username_filter: str = "",
    db: Session = Depends(get_db),
    token = Depends(oauth2_scheme),
    _ = Depends(require_type("teacher"))
):
    """
    Get all students enrolled in any of the teacher's classrooms.
    Useful for exam assignment and student management.

    Query Parameters:
    - skip: Number of records to skip (pagination)
    - limit: Maximum number of records to return (default: 100)
    - username_filter: Filter students by username (case-insensitive partial match)

    Returns:
    - List of students with their basic information (id, username, email, etc.)
    - Students are deduplicated if they appear in multiple teacher's classes
    """
    current_user = get_current_user(token, db)
    return get_all_students_in_teacher_classes(
        db,
        teacher_id=current_user.id,
        skip=skip,
        limit=limit,
        username_filter=username_filter or None
    )

@router.get("/{classroom_id}", response_model=ClassroomOut)
def Get_Classroom_By_Id_Endpoint(
    classroom_id: UUID,
    db: Session = Depends(get_db),
    token = Depends(oauth2_scheme),
    _ = Depends(require_type("teacher"))
):
    
    
    return get_classroom_by_id(db, classroom_id)

@router.get("/student/{classroom_id}", response_model=ClassroomDetailedOut)
def Get_Classroom_By_Id_For_Student_Endpoint(
    classroom_id: UUID,
    db: Session = Depends(get_db),
    token = Depends(oauth2_scheme),
    _ = Depends(require_type("student"))
):
    """
    Get detailed classroom information for students including:
    - Complete classroom details (name, description)
    - Teacher information (name, email, profile picture)
    - Teacher profile (bio, experience, specialization, rating)
    - All student details (names, emails, profile pictures, IDs)
    - Student count
    Only accessible to students who are enrolled in the classroom.
    """
    current_user = get_current_user(token, db)
    return get_classroom_detailed_for_student(db, classroom_id, UUID(str(current_user.id)))

@router.put("/{classroom_id}", response_model=ClassroomOut)
def Update_Classroom_Endpoint(
    classroom_id: UUID,
    classroom_update: ClassroomUpdate,
    db: Session = Depends(get_db),
    token = Depends(oauth2_scheme),
    _ = Depends(require_type("teacher"))
):
    
    return update_classroom(db, classroom_id, classroom_update)


@router.delete("/{classroom_id}", response_model=ClassroomOut)
def Delete_Classroom_Endpoint(
    classroom_id: UUID,
    db: Session = Depends(get_db),
    token = Depends(oauth2_scheme),
    _ = Depends(require_type("teacher"))
):
    
    return delete_classroom(db, classroom_id)

@router.post("/request/students", response_model=StudentClassRequestOut)
def Request_Student_To_Join_Classroom_Endpoint(
    student_request: StudentClassRequestCreate,
    db: Session = Depends(get_db),
    token = Depends(oauth2_scheme),
    _ = Depends(require_type("teacher"))
):
    
    return request_student_to_join_classroom(db, student_request)

@router.get("/{classroom_id}/students", response_model=List[StudentClassroomOut])
def Get_All_Students_In_Classroom_Endpoint(
    classroom_id: UUID,
    db: Session = Depends(get_db),
    token = Depends(oauth2_scheme),
    _ = Depends(require_type("teacher"))
):
    return get_all_students_in_classroom(db, classroom_id)

@router.delete("/{classroom_id}/students/{student_id}")
def Remove_Student_From_Classroom_Endpoint(
    classroom_id: UUID,
    student_id: UUID,
    db: Session = Depends(get_db),
    token = Depends(oauth2_scheme),
    _ = Depends(require_type("teacher"))
):
    current_user = get_current_user(token,db)

    return remove_student_from_classroom(db, StudentClassroomUpdate(student_id=student_id, classroom_id=classroom_id))

@router.delete("/request/{request_id}/class/{classroom_id}")
def Remove_Student_Request(
    classroom_id: UUID,
    request_id: UUID,
    db: Session = Depends(get_db),
    token = Depends(oauth2_scheme),
    _ = Depends(require_type("teacher"))
):
    current_user = get_current_user(token,db)
    return reject_student_request(db, request_id, UUID(str(current_user.id)), classroom_id)

@router.post("/accept/{request_id}", response_model=StudentClassroomOut)
def Accept_Student_Request(
    request_id: UUID,
    db: Session = Depends(get_db),
    token = Depends(oauth2_scheme),
    _ = Depends(require_type("student"))
):
    current_user = get_current_user(token, db)
    user_id = current_user.id
    if not isinstance(user_id, uuid.UUID):
        user_id = uuid.UUID(str(user_id))
    return student_accept_request(db, user_id, request_id)

@router.get("/all/own/students", response_model=List[ClassroomOutLite])
def Get_All_Own_Students_Endpoint(
    db: Session = Depends(get_db),
    token = Depends(oauth2_scheme),
    _ = Depends(require_type("teacher"))
):
    current_user = get_current_user(token,db)
    return get_all_classroom_students(db, UUID(str(current_user.id)))

@router.get("/all/students/not/in/classroom/{classroom_id}", response_model=List[UserOut])
def Get_All_Students_Not_In_Classroom_Endpoint(
    classroom_id: UUID,
    skip: int = 0,
    limit: int = 10,
    username_filter: str = "",
    db: Session = Depends(get_db),
    token = Depends(oauth2_scheme),
    _ = Depends(require_type("teacher"))
):
    return get_all_students_not_in_classroom(db, classroom_id, skip=skip, limit=limit, username_filter=username_filter or None)

@router.get("/my/classrooms", response_model=List[ClassroomOutForStudent])
def Get_My_Classrooms_Endpoint(
    db: Session = Depends(get_db),
    token = Depends(oauth2_scheme),
    _ = Depends(require_type("student"))
):
    """
    Get all classrooms for the current student with full classroom details including:
    - Complete classroom information (name, description)
    - All enrolled students (names, emails, profile pictures, IDs)
    Note: Teacher profile details are not included in this list view for performance.
    Use the individual classroom endpoint for complete teacher profile information.
    """
    current_user = get_current_user(token,db)
    return get_all_classroom_students(db, UUID(str(current_user.id)))


@router.get("/all/requests/for/student", response_model=List[StudentClassRequestOut])
def Get_All_Requests_For_Student_Endpoint(
    db: Session = Depends(get_db),
    token = Depends(oauth2_scheme),
    _ = Depends(require_type("student"))
):
    try:
        current_user = get_current_user(token,db)
        return get_all_requests_for_student(db, UUID(str(current_user.id)))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@router.get("/all/sent/requests", response_model=List[StudentClassRequestOut])
def Get_All_Sent_Requests_By_Teacher_Endpoint(
    db: Session = Depends(get_db),
    token = Depends(oauth2_scheme),
    _ = Depends(require_type("teacher"))
):
    """
    Get all requests sent by the current teacher to students across all their classrooms.
    """
    try:
        current_user = get_current_user(token, db)
        return get_all_sent_requests_by_teacher(db, UUID(str(current_user.id)))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@router.get("all/fellow/students/{classroom_id}", response_model=List[StudentClassroomOut])
def Get_All_Fellow_Students_In_Classroom_Endpoint(
    classroom_id: UUID,
    db: Session = Depends(get_db),
    token = Depends(oauth2_scheme),
    _ = Depends(require_type("student"))
):
    return get_all_students_in_classroom(db, classroom_id)
