"""
Chat CRUD Operations for EduFair Platform

This module contains CRUD operations for chat functionality between users.
"""

from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func, desc, asc
from fastapi import HTTPException, status
from uuid import UUID
from typing import List, Optional, Tuple
from datetime import datetime, timezone

from Models.users import User, ChatMessage, UserFollow
from Schemas.Social.Chat import (
    ChatMessageCreate, ChatMessageResponse, ChatMessageWithUsers,
    ConversationSummary, ConversationListResponse, MessageHistoryResponse,
    ConversationUser, ChatStats
)


# ==================== MESSAGE OPERATIONS ====================

def send_message(db: Session, sender_id: UUID, message_data: ChatMessageCreate) -> ChatMessageResponse:
    """Send a chat message"""
    
    # Check if users exist
    sender = db.query(User).filter(User.id == sender_id).first()
    receiver = db.query(User).filter(User.id == message_data.receiver_id).first()
    
    if not sender:
        raise HTTPException(status_code=404, detail="Sender not found")
    if not receiver:
        raise HTTPException(status_code=404, detail="Receiver not found")
    
    # Check if sender cannot message themselves
    if sender_id == message_data.receiver_id:
        raise HTTPException(status_code=400, detail="Cannot send message to yourself")
    
    # Check if users follow each other (required for messaging)
    follow_check = check_follow_relationship(db, sender_id, message_data.receiver_id)
    if not follow_check["can_message"]:
        raise HTTPException(
            status_code=403, 
            detail="You can only message users who follow you back"
        )
    
    # Create message
    db_message = ChatMessage(
        sender_id=sender_id,
        receiver_id=message_data.receiver_id,
        message=message_data.message,
        sent_at=datetime.now(timezone.utc)
    )
    
    db.add(db_message)
    db.commit()
    db.refresh(db_message)
    
    return ChatMessageResponse.model_validate(db_message)


def get_message(db: Session, message_id: UUID, user_id: UUID) -> ChatMessageWithUsers:
    """Get a specific message with user details"""
    
    message = db.query(ChatMessage).filter(
        and_(
            ChatMessage.id == message_id,
            or_(
                ChatMessage.sender_id == user_id,
                ChatMessage.receiver_id == user_id
            ),
            ChatMessage.is_deleted == False
        )
    ).first()
    
    if not message:
        raise HTTPException(status_code=404, detail="Message not found")
    
    # Get sender and receiver info
    sender = db.query(User).filter(User.id == message.sender_id).first()
    receiver = db.query(User).filter(User.id == message.receiver_id).first()
    
    return ChatMessageWithUsers(
        **message.__dict__,
        sender_username=sender.username,
        receiver_username=receiver.username,
        sender_profile_picture=sender.profile_picture,
        receiver_profile_picture=receiver.profile_picture
    )


def mark_message_as_read(db: Session, message_id: UUID, user_id: UUID) -> bool:
    """Mark a message as read"""
    
    message = db.query(ChatMessage).filter(
        and_(
            ChatMessage.id == message_id,
            ChatMessage.receiver_id == user_id,
            ChatMessage.read_at.is_(None)
        )
    ).first()
    
    if not message:
        return False
    
    message.read_at = datetime.now(timezone.utc)
    db.commit()
    
    return True


def delete_message(db: Session, message_id: UUID, user_id: UUID) -> bool:
    """Delete a message (soft delete)"""
    
    message = db.query(ChatMessage).filter(
        and_(
            ChatMessage.id == message_id,
            ChatMessage.sender_id == user_id,  # Only sender can delete
            ChatMessage.is_deleted == False
        )
    ).first()
    
    if not message:
        raise HTTPException(status_code=404, detail="Message not found or cannot be deleted")
    
    message.is_deleted = True
    db.commit()
    
    return True


# ==================== CONVERSATION OPERATIONS ====================

def get_conversations(
    db: Session, 
    user_id: UUID, 
    page: int = 1, 
    page_size: int = 20
) -> ConversationListResponse:
    """Get list of conversations for a user"""
    
    offset = (page - 1) * page_size
    
    # Get unique conversation partners
    conversations_query = db.query(
        func.greatest(ChatMessage.sender_id, ChatMessage.receiver_id).label('user1'),
        func.least(ChatMessage.sender_id, ChatMessage.receiver_id).label('user2'),
        func.max(ChatMessage.sent_at).label('last_activity')
    ).filter(
        and_(
            or_(
                ChatMessage.sender_id == user_id,
                ChatMessage.receiver_id == user_id
            ),
            ChatMessage.is_deleted == False
        )
    ).group_by(
        func.greatest(ChatMessage.sender_id, ChatMessage.receiver_id),
        func.least(ChatMessage.sender_id, ChatMessage.receiver_id)
    ).order_by(desc('last_activity'))
    
    total_count = conversations_query.count()
    conversations = conversations_query.offset(offset).limit(page_size).all()
    
    conversation_summaries = []
    
    for conv in conversations:
        # Determine the other user
        other_user_id = conv.user1 if conv.user2 == user_id else conv.user2
        other_user = db.query(User).filter(User.id == other_user_id).first()
        
        if not other_user:
            continue
        
        # Get last message
        last_message = db.query(ChatMessage).filter(
            and_(
                or_(
                    and_(ChatMessage.sender_id == user_id, ChatMessage.receiver_id == other_user_id),
                    and_(ChatMessage.sender_id == other_user_id, ChatMessage.receiver_id == user_id)
                ),
                ChatMessage.is_deleted == False
            )
        ).order_by(desc(ChatMessage.sent_at)).first()
        
        # Count unread messages
        unread_count = db.query(ChatMessage).filter(
            and_(
                ChatMessage.sender_id == other_user_id,
                ChatMessage.receiver_id == user_id,
                ChatMessage.read_at.is_(None),
                ChatMessage.is_deleted == False
            )
        ).count()
        
        conversation_summaries.append(ConversationSummary(
            other_user=ConversationUser.model_validate(other_user),
            last_message=ChatMessageResponse.model_validate(last_message) if last_message else None,
            unread_count=unread_count,
            last_activity=conv.last_activity
        ))
    
    return ConversationListResponse(
        conversations=conversation_summaries,
        total_count=total_count,
        page=page,
        page_size=page_size,
        has_next=offset + page_size < total_count,
        has_previous=page > 1
    )


def get_message_history(
    db: Session,
    user_id: UUID,
    other_user_id: UUID,
    page: int = 1,
    page_size: int = 50
) -> MessageHistoryResponse:
    """Get message history between two users"""
    
    # Check if other user exists
    other_user = db.query(User).filter(User.id == other_user_id).first()
    if not other_user:
        raise HTTPException(status_code=404, detail="User not found")
    
    offset = (page - 1) * page_size
    
    # Get messages between users
    messages_query = db.query(ChatMessage).filter(
        and_(
            or_(
                and_(ChatMessage.sender_id == user_id, ChatMessage.receiver_id == other_user_id),
                and_(ChatMessage.sender_id == other_user_id, ChatMessage.receiver_id == user_id)
            ),
            ChatMessage.is_deleted == False
        )
    ).order_by(desc(ChatMessage.sent_at))
    
    total_count = messages_query.count()
    messages = messages_query.offset(offset).limit(page_size).all()
    
    # Get sender and receiver info for each message
    messages_with_users = []
    for message in messages:
        sender = db.query(User).filter(User.id == message.sender_id).first()
        receiver = db.query(User).filter(User.id == message.receiver_id).first()
        
        messages_with_users.append(ChatMessageWithUsers(
            **message.__dict__,
            sender_username=sender.username,
            receiver_username=receiver.username,
            sender_profile_picture=sender.profile_picture,
            receiver_profile_picture=receiver.profile_picture
        ))
    
    return MessageHistoryResponse(
        messages=messages_with_users,
        total_count=total_count,
        page=page,
        page_size=page_size,
        has_next=offset + page_size < total_count,
        has_previous=page > 1,
        conversation_partner=ConversationUser.model_validate(other_user)
    )


# ==================== UTILITY FUNCTIONS ====================

def check_follow_relationship(db: Session, user1_id: UUID, user2_id: UUID) -> dict:
    """Check if users can message each other (must follow each other)"""
    
    user1_follows_user2 = db.query(UserFollow).filter(
        and_(
            UserFollow.follower_id == user1_id,
            UserFollow.following_id == user2_id
        )
    ).first() is not None
    
    user2_follows_user1 = db.query(UserFollow).filter(
        and_(
            UserFollow.follower_id == user2_id,
            UserFollow.following_id == user1_id
        )
    ).first() is not None
    
    # Users can message if they follow each other
    can_message = user1_follows_user2 and user2_follows_user1
    
    return {
        "user1_follows_user2": user1_follows_user2,
        "user2_follows_user1": user2_follows_user1,
        "can_message": can_message
    }


def get_chat_stats(db: Session, user_id: UUID) -> ChatStats:
    """Get chat statistics for a user"""
    
    # Count total conversations using a simpler approach
    # Get unique conversation partners
    conversations_subquery = db.query(
        func.greatest(ChatMessage.sender_id, ChatMessage.receiver_id).label('user1'),
        func.least(ChatMessage.sender_id, ChatMessage.receiver_id).label('user2')
    ).filter(
        and_(
            or_(
                ChatMessage.sender_id == user_id,
                ChatMessage.receiver_id == user_id
            ),
            ChatMessage.is_deleted == False
        )
    ).distinct().subquery()

    total_conversations = db.query(func.count()).select_from(conversations_subquery).scalar() or 0
    
    # Count messages sent
    messages_sent = db.query(ChatMessage).filter(
        and_(
            ChatMessage.sender_id == user_id,
            ChatMessage.is_deleted == False
        )
    ).count()
    
    # Count messages received
    messages_received = db.query(ChatMessage).filter(
        and_(
            ChatMessage.receiver_id == user_id,
            ChatMessage.is_deleted == False
        )
    ).count()
    
    # Count unread messages
    unread_messages = db.query(ChatMessage).filter(
        and_(
            ChatMessage.receiver_id == user_id,
            ChatMessage.read_at.is_(None),
            ChatMessage.is_deleted == False
        )
    ).count()
    
    return ChatStats(
        user_id=user_id,
        total_conversations=total_conversations,
        total_messages_sent=messages_sent,
        total_messages_received=messages_received,
        unread_messages_count=unread_messages,
        active_conversations_count=total_conversations  # Simplified for now
    )


# ==================== BULK OPERATIONS ====================

def mark_conversation_as_read(db: Session, user_id: UUID, other_user_id: UUID) -> int:
    """Mark all messages in a conversation as read"""
    
    unread_messages = db.query(ChatMessage).filter(
        and_(
            ChatMessage.sender_id == other_user_id,
            ChatMessage.receiver_id == user_id,
            ChatMessage.read_at.is_(None),
            ChatMessage.is_deleted == False
        )
    ).all()
    
    read_time = datetime.now(timezone.utc)
    count = 0
    
    for message in unread_messages:
        message.read_at = read_time
        count += 1
    
    db.commit()
    return count
