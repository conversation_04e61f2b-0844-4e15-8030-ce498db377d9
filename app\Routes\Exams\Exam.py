from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from uuid import UUID
from Schemas.Exams.Exam import (
    ExamCreate, ExamUpdate, ExamOut, ExamCreateWithAssignment, ExamAssignmentOut,
    ExamStudentOut, ExamStudentMinimalOut, ExamListOut, ExamDetailedOut,
    ExamAssignmentRequest, ExamUnassignmentRequest, ExamAssignmentResponse, ExamAssignmentDetailedResponse,
    ExamComprehensiveDetails, AvailableStudent, ExamStatistics, ExamMarksStatistics, TopPerformer
)
from Cruds.Exams.Exam import (
    get_exam_by_id,
    get_exam_by_id_comprehensive,
    get_all_exams,
    update_exam,
    delete_exam,
    create_exam,
    create_exam_with_assignment,
    get_exams_by_teacher,
    get_all_exams_for_student,
    get_exam_for_student,
    get_all_exams_minimal,
    get_exams_by_teacher_minimal,
    get_exams_by_institute_minimal,
    assign_exam_to_students_and_classrooms,
    get_exam_assignments,
    unassign_exam_from_students,
    get_available_students_for_assignment,
    get_exam_by_id_for_institute,
    get_exam_competition_usage,
    get_exam_competition_statistics,
    calculate_exam_statistics,
    copy_exam,
    update_exam_with_copy,
    get_exam_usage_info,
    get_available_exams_for_competition
)
from config.session import get_db
from typing import List
from config.deps import oauth2_scheme, get_current_user
from config.permission import require_type
from config.subscription_permission import require_exam_creation, update_usage_counter
from Models.Questions import Question
from datetime import timedelta, datetime, timezone

router = APIRouter()

def get_utc_now():
    """Get current time in UTC"""
    return datetime.now(timezone.utc)

def ensure_utc_timezone(dt):
    """Ensure datetime is in UTC timezone"""
    if dt is None:
        return None
    if dt.tzinfo is None:
        return dt.replace(tzinfo=timezone.utc)
    else:
        return dt.astimezone(timezone.utc)

def is_teacher_exam(db: Session, exam_id: UUID, teacher_id) -> bool:
    # Ensure teacher_id is a value, not a Column
    question = db.query(Question).filter(
        Question.teacher_id == teacher_id,
        Question.exams.any(id=exam_id)
    ).first()
    return question is not None

@router.get("/", response_model=List[ExamOut])
def read_exams(db: Session = Depends(get_db), token: str = Depends(oauth2_scheme), _ = Depends(require_type("admin"))):
    return get_all_exams(db)

@router.get("/my-exams", response_model=List[ExamOut])
def get_teacher_exams(
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type(["institute", "teacher"]))
):
    current_user = get_current_user(token, db)
    teacher_id = getattr(current_user, 'id', None)
    if teacher_id is None:
        raise HTTPException(status_code=401, detail="Invalid user.")
    return get_exams_by_teacher(db, teacher_id)

@router.get("/list/all", response_model=List[ExamListOut])
def get_all_exams_list(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("admin"))
):
    """
    Get all exams with minimal information for listing purposes.

    Query Parameters:
    - skip: Number of records to skip (pagination)
    - limit: Maximum number of records to return (default: 100)

    Returns:
    - List of exams with basic info: id, title, description, total_marks,
      total_duration, start_time, end_time, total_questions, timestamps
    - No question objects included for better performance
    """
    exams = get_all_exams_minimal(db, skip=skip, limit=limit)
    return [ExamListOut.from_orm_with_calculated_fields(exam, db_session=db) for exam in exams]

@router.get("/my-exams/list", response_model=List[ExamListOut])
def get_teacher_exams_list(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type(["institute", "teacher"]))
):
    """
    Get all exams created by the current teacher with minimal information.

    Query Parameters:
    - skip: Number of records to skip (pagination)
    - limit: Maximum number of records to return (default: 100)

    Returns:
    - List of teacher's exams with basic info: id, title, description, total_marks,
      total_duration, start_time, end_time, total_questions, timestamps
    - No question objects included for better performance
    - Only includes exams that contain at least one question created by the teacher
    """
    try:
        current_user = get_current_user(token, db)
        teacher_id = getattr(current_user, 'id', None)
        if teacher_id is None:
            raise HTTPException(status_code=401, detail="Invalid user.")

        exams = get_exams_by_teacher_minimal(db, teacher_id, skip=skip, limit=limit)

        # Convert exams to response format with error handling
        result = []
        for exam in exams:
            try:
                exam_data = ExamListOut.from_orm_with_calculated_fields(exam, db_session=db)
                result.append(exam_data)
            except Exception as e:
                print(f"Error processing exam {getattr(exam, 'id', 'unknown')}: {e}")
                # Skip this exam and continue with others
                continue

        return result
    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
    except Exception as e:
        print(f"Unexpected error in get_teacher_exams_list: {e}")
        raise HTTPException(status_code=500, detail="An error occurred while fetching exams. Please try again later.")

@router.get("/institute/reference-exams", response_model=List[dict])
def get_institute_reference_exams_list(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """
    Get all reference exams created by the institute for competition use.

    **IMPORTANT:** Institute exams are REFERENCE EXAMS for competitions, not for classroom assignments.
    These exams have no student assignments - students access them through competition registrations.

    Query Parameters:
    - skip: Number of records to skip (pagination)
    - limit: Maximum number of records to return (default: 100)

    Returns:
    - List of institute reference exams with basic info and competition usage
    - No student assignments (because there are none)
    - Competition usage statistics for each exam
    """
    try:
        current_user = get_current_user(token, db)
        institute_id = getattr(current_user, 'id', None)
        if institute_id is None:
            raise HTTPException(status_code=401, detail="Invalid user.")

        print(f"DEBUG: Institute reference exams request - institute_id: {institute_id}, user_type: {current_user.user_type}")
        exams = get_exams_by_institute_minimal(db, institute_id, skip=skip, limit=limit)
        print(f"DEBUG: Found {len(exams)} exams for institute")

        # Convert exams to response format with competition usage info
        result = []
        print(f"DEBUG: Processing {len(exams)} exams for response")
        for i, exam in enumerate(exams):
            try:
                print(f"DEBUG: Processing exam {i+1}: {exam.id} - {exam.title}")
                # Get competition usage for this exam
                competition_usage = get_exam_competition_usage(db, exam.id, institute_id)
                competition_stats = get_exam_competition_statistics(db, exam.id)
                print(f"DEBUG: Competition usage: {len(competition_usage)} competitions")

                exam_data = {
                    "id": exam.id,
                    "title": exam.title,
                    "description": exam.description,
                    "total_marks": exam.total_marks,
                    "total_duration": exam.total_duration,
                    "start_time": exam.start_time,
                    "total_questions": len(exam.questions) if hasattr(exam, 'questions') and exam.questions else 0,
                    "created_at": exam.created_at,
                    "updated_at": exam.updated_at,
                    "exam_type": "reference",
                    "is_competition_exam": getattr(exam, 'is_competition_exam', False),
                    "competition_usage": {
                        "total_competitions": len(competition_usage),
                        "active_competitions": len([c for c in competition_usage if c.get("status") == "active"]),
                        "total_participants": sum(c.get("participant_count", 0) for c in competition_usage)
                    },
                    "competition_statistics": competition_stats
                }
                result.append(exam_data)
                print(f"DEBUG: Successfully added exam {exam.id} to result")
            except Exception as e:
                print(f"Error processing exam {getattr(exam, 'id', 'unknown')}: {e}")
                import traceback
                traceback.print_exc()
                # Skip this exam and continue with others
                continue

        print(f"DEBUG: Returning {len(result)} exams in final result")
        return result
    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
    except Exception as e:
        print(f"Unexpected error in get_institute_reference_exams_list: {e}")
        raise HTTPException(status_code=500, detail="An error occurred while fetching institute reference exams. Please try again later.")

@router.get("/{exam_id}", response_model=ExamDetailedOut)
def read_exam(
    exam_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type(["institute", "teacher"]))
):
    """
    Get comprehensive exam details including assigned students and class information.

    Returns:
    - Complete exam information with questions
    - List of assigned students with their details
    - Assignment tracking information (individual vs classroom)
    - Class number extracted from questions
    """
    current_user = get_current_user(token, db)
    teacher_id = getattr(current_user, 'id', None)
    return get_exam_by_id_comprehensive(db, exam_id, teacher_id=teacher_id)

@router.post("/", response_model=ExamOut)
def create_exam_endpoint(
    exam_in: ExamCreate,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type(["institute", "teacher"]))
):
    """
    Create a new exam with question objects.

    This endpoint creates an exam with embedded question objects instead of question IDs.
    Questions are created as part of the exam creation process.
    """
    current_user = get_current_user(token, db)
    return create_exam(db, exam_in, current_user)

@router.put("/{exam_id}", response_model=ExamOut)
def update_exam_endpoint(
    exam_id: UUID,
    exam_update: ExamUpdate,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type(["institute", "teacher"]))
):
    current_user = get_current_user(token, db)
    teacher_id = getattr(current_user, 'id', None)
    return update_exam(db, exam_id, exam_update, teacher_id=teacher_id)

@router.delete("/{exam_id}", status_code=204)
def delete_exam_endpoint(
    exam_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type(["institute", "teacher"]))
):
    current_user = get_current_user(token, db)
    teacher_id = getattr(current_user, 'id', None)
    delete_exam(db, exam_id, teacher_id=teacher_id)
    return None

@router.post("/create-with-assignment", response_model=ExamAssignmentOut)
def create_exam_with_assignment_endpoint(
    exam_in: ExamCreateWithAssignment,
    db: Session = Depends(get_db),
    subscription_check = Depends(require_exam_creation())
):
    """
    Create a new exam with assignment to classrooms (with subscription validation).
    """
    current_user = subscription_check["user"]

    # Create exam with assignment
    new_exam = create_exam_with_assignment(db, exam_in, current_user)

    # Update usage counter
    update_usage_counter(db, current_user, "exams_created", 1)

    return new_exam

@router.get("/student/all", response_model=List[ExamStudentMinimalOut])
def get_student_all_exams(
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("student"))
):
    """
    Get ALL assigned exams for the current student.
    Returns upcoming, ongoing, and completed exams.
    Client can filter by time/status as needed.
    """
    current_user = get_current_user(token, db)
    student_id = getattr(current_user, 'id', None)
    if student_id is None:
        raise HTTPException(status_code=401, detail="Invalid user.")
    exams = get_all_exams_for_student(db, student_id)
    result = []
    for exam in exams:
        start_time = getattr(exam, 'start_time', None)
        total_duration = getattr(exam, 'total_duration', None)
        end_time = None
        if start_time and total_duration:
            end_time = start_time + timedelta(minutes=int(total_duration))

        # Get status from the enhanced CRUD function
        status = getattr(exam, 'status', 'assigned')

        result.append(ExamStudentMinimalOut(
            id=getattr(exam, 'id', None),
            title=getattr(exam, 'title', None),
            description=getattr(exam, 'description', None),
            total_marks=getattr(exam, 'total_marks', 0),
            start_time=start_time,
            end_time=end_time,
            total_duration=int(total_duration) if total_duration is not None else 0,
            status=status
        ))
    return result

@router.get("/student/upcoming", response_model=List[ExamStudentMinimalOut])
def get_student_upcoming_exams_deprecated(
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("student"))
):
    """
    DEPRECATED: Use /student/all instead.
    This endpoint now returns all exams (same as /student/all).
    """
    return get_student_all_exams(db, token, _)

@router.get("/student/{exam_id}", response_model=ExamStudentOut)
def get_student_exam(
    exam_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("student"))
):
    current_user = get_current_user(token, db)
    student_id = getattr(current_user, 'id', None)
    if student_id is None:
        raise HTTPException(status_code=401, detail="Invalid user.")
    exam = get_exam_for_student(db, exam_id, student_id)
    now = get_utc_now()
    start_time = getattr(exam, 'start_time', None)
    total_duration = getattr(exam, 'total_duration', None)

    # Ensure timezone compatibility for comparison
    if start_time:
        start_time_utc = ensure_utc_timezone(start_time)
        if now < start_time_utc:
            raise HTTPException(status_code=403, detail="Exam not started yet.")
    # Calculate end_time using timezone-aware start_time
    end_time = None
    if start_time and total_duration:
        start_time_utc = ensure_utc_timezone(start_time)
        end_time = start_time_utc + timedelta(minutes=int(total_duration))

    return ExamStudentOut(
        id=getattr(exam, 'id', None),
        title=getattr(exam, 'title', ''),
        description=getattr(exam, 'description', None),
        total_marks=getattr(exam, 'total_marks', 0),
        total_duration=getattr(exam, 'total_duration', 0),
        questions=[q for q in exam.questions],
        start_time=ensure_utc_timezone(start_time) if start_time else None,
        end_time=end_time
    )


# ===== EXAM ASSIGNMENT ENDPOINTS =====

@router.post("/{exam_id}/assign", response_model=ExamAssignmentResponse)
def assign_exam(
    exam_id: UUID,
    assignment_request: ExamAssignmentRequest,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type(["institute", "teacher"]))
):
    """
    Assign an existing exam to students and/or classrooms.

    **Actions:**
    - `add`: Add new assignments (default)
    - `remove`: Remove specified assignments
    - `replace`: Replace all current assignments with new ones

    **Assignment Options:**
    - Assign to individual students using `student_ids`
    - Assign to entire classrooms using `classroom_ids`
    - Combine both for mixed assignments

    **Authentication:** Requires teacher role and ownership of the exam.
    """
    current_user = get_current_user(token, db)
    teacher_id = getattr(current_user, 'id', None)

    # Validate that at least one assignment target is provided
    if not assignment_request.student_ids and not assignment_request.classroom_ids:
        raise HTTPException(
            status_code=400,
            detail="At least one of student_ids or classroom_ids must be provided"
        )

    # Perform the assignment
    result = assign_exam_to_students_and_classrooms(
        db=db,
        exam_id=exam_id,
        student_ids=assignment_request.student_ids,
        classroom_ids=assignment_request.classroom_ids,
        teacher_id=teacher_id,
        action=assignment_request.action
    )

    # Create response message
    action_messages = {
        "add": f"Successfully assigned exam to {len(result.assigned_student_ids)} students",
        "remove": f"Successfully removed exam assignment from students. {len(result.assigned_student_ids)} students remain assigned",
        "replace": f"Successfully replaced exam assignments. Now assigned to {len(result.assigned_student_ids)} students"
    }

    return ExamAssignmentResponse(
        exam_id=result.exam_id,
        assigned_student_ids=result.assigned_student_ids,
        total_assigned=len(result.assigned_student_ids),
        action_performed=assignment_request.action,
        message=action_messages.get(assignment_request.action, "Assignment completed")
    )


@router.delete("/{exam_id}/unassign", response_model=ExamAssignmentResponse)
def unassign_exam(
    exam_id: UUID,
    unassignment_request: ExamUnassignmentRequest,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type(["institute", "teacher"]))
):
    """
    Remove exam assignment from specific students.

    **Authentication:** Requires teacher role and ownership of the exam.
    """
    current_user = get_current_user(token, db)
    teacher_id = getattr(current_user, 'id', None)

    # Perform the unassignment
    result = unassign_exam_from_students(
        db=db,
        exam_id=exam_id,
        student_ids=unassignment_request.student_ids,
        teacher_id=teacher_id
    )

    return ExamAssignmentResponse(
        exam_id=result.exam_id,
        assigned_student_ids=result.assigned_student_ids,
        total_assigned=len(result.assigned_student_ids),
        action_performed="remove",
        message=f"Successfully removed exam assignment from {len(unassignment_request.student_ids)} students"
    )


@router.get("/{exam_id}/assignments", response_model=ExamAssignmentOut)
def get_exam_assignment_details(
    exam_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type(["institute", "teacher"]))
):
    """
    Get current assignment details for an exam.

    Returns list of all students currently assigned to the exam.

    **Authentication:** Requires teacher role and ownership of the exam.
    """
    current_user = get_current_user(token, db)
    teacher_id = getattr(current_user, 'id', None)

    return get_exam_assignments(db=db, exam_id=exam_id, teacher_id=teacher_id)


@router.get("/{exam_id}/comprehensive", response_model=ExamComprehensiveDetails)
def get_comprehensive_exam_details(
    exam_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type(["institute", "teacher"]))
):
    """
    Get comprehensive exam details including:
    - Basic exam information (title, description, duration, etc.)
    - All exam questions
    - Current assignment details (which students are assigned)
    - Available students for assignment (from teacher's classrooms)
    - Comprehensive statistics:
      * Completion rate
      * Total submissions
      * Marks statistics (highest, lowest, average, standard deviation)
      * Top performers

    This endpoint provides everything needed for exam management and analytics.

    **Authentication:** Requires teacher role and ownership of the exam.
    """
    try:
        current_user = get_current_user(token, db)
        teacher_id = getattr(current_user, 'id', None)

        # Get basic exam details
        exam = get_exam_by_id_comprehensive(db=db, exam_id=exam_id, teacher_id=teacher_id)
        if not exam:
            raise HTTPException(status_code=404, detail="Exam not found")

        # Verify teacher ownership (already done in get_exam_by_id_comprehensive, but double-check)
        if hasattr(exam, 'questions') and exam.questions:
            question = exam.questions[0]
            if hasattr(question, 'teacher_id') and question.teacher_id != teacher_id:
                raise HTTPException(status_code=403, detail="You do not have permission to view this exam")

        # Get assignment details
        assignment_details = get_exam_assignments(db=db, exam_id=exam_id, teacher_id=teacher_id)

        # Get available students for assignment
        available_students_data = get_available_students_for_assignment(
            db=db,
            teacher_id=teacher_id,
            exam_id=exam_id
        )
        available_students = [AvailableStudent(**student) for student in available_students_data]

        # Calculate statistics
        stats_data = calculate_exam_statistics(db=db, exam_id=exam_id)

        # Format statistics
        marks_stats = ExamMarksStatistics(**stats_data["marks_statistics"])
        top_performers = [TopPerformer(**performer) for performer in stats_data["top_performers"]]

        statistics = ExamStatistics(
            total_assigned=stats_data["total_assigned"],
            total_attempts=stats_data["total_attempts"],
            completed_attempts=stats_data["completed_attempts"],
            completion_rate=stats_data["completion_rate"],
            marks_statistics=marks_stats,
            top_performers=top_performers
        )

        # Build comprehensive response
        return ExamComprehensiveDetails(
            id=exam.id,
            title=exam.title,
            description=exam.description,
            total_marks=exam.total_marks,
            total_duration=exam.total_duration,
            start_time=exam.start_time,
            questions=exam.questions,
            assignment_details=assignment_details,
            available_students=available_students,
            statistics=statistics
        )
    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
    except Exception as e:
        print(f"Unexpected error in get_comprehensive_exam_details: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail="An error occurred while fetching comprehensive exam details. Please try again later.")


@router.get("/{exam_id}/comprehensive-institute", response_model=dict)
def get_comprehensive_exam_details_for_institute(
    exam_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """
    Get comprehensive exam details specifically for institutes including:
    - Basic exam information (title, description, duration, etc.)
    - All exam questions
    - Competition usage information (where this exam is used)
    - Competition statistics (if used in competitions)
    - Exam usage analytics for competition planning

    **IMPORTANT:** Institute exams are REFERENCE EXAMS for competitions, not for classroom assignments.
    Students access these exams through competition registrations/tickets, not direct assignments.

    **Authentication:** Requires institute role. Institutes can view their own reference exams.
    """
    try:
        current_user = get_current_user(token, db)
        institute_id = getattr(current_user, 'id', None)

        # Get basic exam details - verify institute ownership
        exam = get_exam_by_id_for_institute(db=db, exam_id=exam_id, institute_id=institute_id)
        if not exam:
            raise HTTPException(status_code=404, detail="Exam not found or access denied")

        # Get competition usage information
        competition_usage = get_exam_competition_usage(db=db, exam_id=exam_id, institute_id=institute_id)

        # Get competition statistics if exam is used in competitions
        competition_stats = get_exam_competition_statistics(db=db, exam_id=exam_id)

        # Build comprehensive response for institute reference exam
        return {
            "id": exam.id,
            "title": exam.title,
            "description": exam.description,
            "total_marks": exam.total_marks,
            "total_duration": exam.total_duration,
            "start_time": exam.start_time,
            "questions": exam.questions,
            "total_questions": len(exam.questions) if exam.questions else 0,
            "created_at": exam.created_at,
            "updated_at": exam.updated_at,
            "exam_type": "reference",  # Institute exams are reference exams
            "competition_usage": competition_usage,
            "competition_statistics": competition_stats,
            "usage_summary": {
                "total_competitions": len(competition_usage),
                "active_competitions": len([c for c in competition_usage if c.get("status") == "active"]),
                "total_participants": sum(c.get("participant_count", 0) for c in competition_usage)
            }
        }
    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
    except Exception as e:
        print(f"Unexpected error in get_comprehensive_exam_details_for_institute: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail="An error occurred while fetching institute exam details. Please try again later.")


@router.get("/{exam_id}/available-students", response_model=List[AvailableStudent])
def get_available_students_for_exam_assignment(
    exam_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type(["institute", "teacher"]))
):
    """
    Get all students available for assignment to this exam.

    Returns students from the teacher's classrooms with their current assignment status.
    This is useful for assignment management interfaces.

    **Authentication:** Requires teacher role and ownership of the exam.
    """
    current_user = get_current_user(token, db)
    teacher_id = getattr(current_user, 'id', None)

    # Verify exam exists and teacher has permission
    exam = get_exam_by_id(db=db, exam_id=exam_id)
    if not exam:
        raise HTTPException(status_code=404, detail="Exam not found")

    # Get available students
    available_students_data = get_available_students_for_assignment(
        db=db,
        teacher_id=teacher_id,
        exam_id=exam_id
    )

    return [AvailableStudent(**student) for student in available_students_data]


@router.get("/{exam_id}/statistics", response_model=ExamStatistics)
def get_exam_statistics_only(
    exam_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type(["institute", "teacher"]))
):
    """
    Get comprehensive statistics for an exam.

    Returns detailed analytics including:
    - Completion rates
    - Submission counts
    - Marks statistics (highest, lowest, average, standard deviation)
    - Top performers

    **Authentication:** Requires teacher role and ownership of the exam.
    """
    current_user = get_current_user(token, db)
    teacher_id = getattr(current_user, 'id', None)

    # Verify exam exists and teacher has permission
    exam = get_exam_by_id(db=db, exam_id=exam_id)
    if not exam:
        raise HTTPException(status_code=404, detail="Exam not found")

    # Calculate statistics
    stats_data = calculate_exam_statistics(db=db, exam_id=exam_id)

    # Format statistics
    marks_stats = ExamMarksStatistics(**stats_data["marks_statistics"])
    top_performers = [TopPerformer(**performer) for performer in stats_data["top_performers"]]

    return ExamStatistics(
        total_assigned=stats_data["total_assigned"],
        total_attempts=stats_data["total_attempts"],
        completed_attempts=stats_data["completed_attempts"],
        completion_rate=stats_data["completion_rate"],
        marks_statistics=marks_stats,
        top_performers=top_performers
    )


# ==================== EXAM COPY AND REUSABILITY ROUTES ====================

@router.post("/{exam_id}/copy", response_model=ExamOut)
def copy_exam_endpoint(
    exam_id: UUID,
    new_title: str = None,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type(["institute", "teacher"]))
):
    """
    Create a copy of an existing exam.

    This allows for reusable exams while preserving the original.
    Useful when you want to modify an exam that's already being used.
    """
    current_user = get_current_user(token, db)
    user_id = getattr(current_user, 'id', None)

    copied_exam = copy_exam(db, exam_id, user_id, new_title)
    return ExamOut.from_orm(copied_exam)


@router.get("/{exam_id}/usage-info")
def get_exam_usage_info_endpoint(
    exam_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type(["teacher", "mentor", "institute"]))
):
    """
    Get information about how an exam is being used.

    Returns details about competitions, assignments, and attempts
    to help users understand if they need to copy before updating.
    """
    current_user = get_current_user(token, db)
    return get_exam_usage_info(db, exam_id)


@router.put("/{exam_id}/update-with-copy", response_model=ExamOut)
def update_exam_with_copy_endpoint(
    exam_id: UUID,
    exam_update: ExamUpdate,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type(["institute", "teacher"]))
):
    """
    Update an exam with automatic copy creation if needed.

    If the exam is being used in competitions or has assignments,
    this will create a copy and apply updates to the copy.
    Otherwise, it will update the original exam.
    """
    current_user = get_current_user(token, db)
    user_id = getattr(current_user, 'id', None)

    updated_exam = update_exam_with_copy(db, exam_id, exam_update, user_id)
    return ExamOut.from_orm(updated_exam)


@router.get("/available-for-competition", response_model=List[dict])
def get_available_exams_for_competition_endpoint(
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type(["institute", "teacher"]))
):
    """
    Get list of exams available for use in competitions.

    Returns exams with usage information to help users
    select appropriate exams for competitions.
    """
    current_user = get_current_user(token, db)
    user_id = getattr(current_user, 'id', None)
    user_type = getattr(current_user, 'user_type', None)

    if user_type:
        user_type_str = user_type.value if hasattr(user_type, 'value') else str(user_type)
    else:
        user_type_str = "teacher"  # Default fallback

    return get_available_exams_for_competition(db, user_id, user_type_str)