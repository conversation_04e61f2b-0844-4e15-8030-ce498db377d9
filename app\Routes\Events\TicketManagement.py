"""
Ticket Management Routes for EduFair Platform

This module provides APIs for:
- Viewing digital tickets
- Downloading tickets
- Ticket verification
- QR code generation
"""

from fastapi import APIRouter, Depends, HTTPException, status, Response
from fastapi.responses import JSONResponse, FileResponse
from sqlalchemy.orm import Session
from typing import Dict, Any
from uuid import UUID
from pydantic import BaseModel
import json
import io
import base64
from datetime import datetime

# Import dependencies
from config.session import get_db
from config.deps import oauth2_scheme, get_current_user
from config.permission import require_type

# Import services
from services.ticket_service import ticket_service
from services.ticket_email_service import ticket_email_service

# Import models
from Models.Events import EventRegistration, Event, EventTicket
from Models.users import User

router = APIRouter()


# ==================== TICKET SCHEMAS ====================

class TicketViewResponse(BaseModel):
    """Response for viewing a ticket"""
    registration: Dict[str, Any]
    event: Dict[str, Any]
    ticket: Dict[str, Any] = None
    user: Dict[str, Any]
    check_in_code: str = None
    generated_at: str


class TicketVerificationRequest(BaseModel):
    """Request for ticket verification"""
    registration_number: str = None
    check_in_code: str = None


class TicketVerificationResponse(BaseModel):
    """Response for ticket verification"""
    is_valid: bool
    message: str
    registration_id: str = None
    event_title: str = None
    user_name: str = None
    already_attended: bool = False


class ResendConfirmationRequest(BaseModel):
    """Request to resend confirmation email"""
    registration_id: UUID


# ==================== TICKET MANAGEMENT ROUTES ====================

@router.get("/registrations/{registration_id}/ticket", response_model=TicketViewResponse)
def view_ticket(
    registration_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """
    View digital ticket for a registration.
    
    Returns complete ticket information including QR code for display.
    """
    current_user = get_current_user(token, db)
    
    try:
        # Get ticket data
        ticket_data = ticket_service.get_ticket_data(
            db=db,
            registration_id=str(registration_id),
            user_id=str(current_user.id)
        )
        
        return TicketViewResponse(**ticket_data)
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve ticket: {str(e)}"
        )


@router.get("/registrations/{registration_id}/ticket/download")
def download_ticket(
    registration_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """
    Download ticket as JSON file.
    
    Returns ticket data as a downloadable JSON file.
    """
    current_user = get_current_user(token, db)
    
    try:
        # Get ticket data
        ticket_data = ticket_service.get_ticket_data(
            db=db,
            registration_id=str(registration_id),
            user_id=str(current_user.id)
        )
        
        # Create JSON response
        json_content = json.dumps(ticket_data, indent=2, default=str)
        
        # Create filename
        reg_number = ticket_data["registration"]["registration_number"]
        event_title = ticket_data["event"]["title"].replace(" ", "_")
        filename = f"ticket_{reg_number}_{event_title}.json"
        
        # Return as downloadable file
        response = Response(
            content=json_content,
            media_type="application/json",
            headers={
                "Content-Disposition": f"attachment; filename={filename}"
            }
        )
        
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to download ticket: {str(e)}"
        )


@router.get("/registrations/{registration_id}/check-in-code")
def get_check_in_code(
    registration_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """
    Get check-in code for a ticket.

    Returns check-in code and registration number.
    """
    current_user = get_current_user(token, db)

    try:
        # Get registration
        registration = db.query(EventRegistration).filter(
            EventRegistration.id == registration_id,
            EventRegistration.user_id == current_user.id
        ).first()

        if not registration:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Registration not found"
            )

        # Generate check-in code if not exists
        if not registration.check_in_code:
            registration = ticket_service.update_registration_codes(db, registration)

        return {
            "check_in_code": registration.check_in_code,
            "registration_number": registration.registration_number,
            "status": registration.status.value
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get check-in code: {str(e)}"
        )


@router.post("/verify-ticket", response_model=TicketVerificationResponse)
def verify_ticket_endpoint(
    verification_request: TicketVerificationRequest,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type(["institute", "teacher"]))
):
    """
    Verify a ticket using registration number or check-in code.

    Only institutes and teachers can verify tickets.
    """
    current_user = get_current_user(token, db)

    try:
        is_valid = False
        message = ""
        registration = None

        # Verify using registration number
        if verification_request.registration_number:
            is_valid, message, registration = ticket_service.verify_ticket_by_registration_number(
                db=db,
                registration_number=verification_request.registration_number
            )

        # Verify using check-in code
        elif verification_request.check_in_code:
            is_valid, message, registration = ticket_service.verify_check_in_code(
                db=db,
                check_in_code=verification_request.check_in_code
            )

        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Either registration_number or check_in_code must be provided"
            )
        
        # Prepare response
        response_data = {
            "is_valid": is_valid,
            "message": message
        }
        
        if registration:
            # Get event and user info
            event = db.query(Event).filter(Event.id == registration.event_id).first()
            user = db.query(User).filter(User.id == registration.user_id).first()
            
            response_data.update({
                "registration_id": str(registration.id),
                "event_title": event.title if event else "Unknown Event",
                "user_name": user.username if user else "Unknown User",
                "already_attended": registration.attended_at is not None
            })
        
        return TicketVerificationResponse(**response_data)
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to verify ticket: {str(e)}"
        )


@router.post("/registrations/{registration_id}/check-in")
def check_in_attendee(
    registration_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type(["institute", "teacher"]))
):
    """
    Mark attendee as checked in.
    
    Only institutes and teachers can check in attendees.
    """
    current_user = get_current_user(token, db)
    
    try:
        # Get registration
        registration = db.query(EventRegistration).filter(
            EventRegistration.id == registration_id
        ).first()
        
        if not registration:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Registration not found"
            )
        
        # Check if already attended
        if registration.attended_at:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Attendee already checked in"
            )
        
        # Mark attendance
        registration = ticket_service.mark_attendance(db, registration)
        
        # Get event and user info for response
        event = db.query(Event).filter(Event.id == registration.event_id).first()
        user = db.query(User).filter(User.id == registration.user_id).first()
        
        return {
            "message": "Attendee checked in successfully",
            "registration_id": str(registration.id),
            "registration_number": registration.registration_number,
            "event_title": event.title if event else "Unknown Event",
            "user_name": user.username if user else "Unknown User",
            "checked_in_at": registration.attended_at.isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to check in attendee: {str(e)}"
        )


@router.post("/registrations/{registration_id}/resend-confirmation")
def resend_confirmation_email(
    registration_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """
    Resend ticket confirmation email.
    
    Users can resend their own confirmation emails.
    """
    current_user = get_current_user(token, db)
    
    try:
        # Get registration
        registration = db.query(EventRegistration).filter(
            EventRegistration.id == registration_id,
            EventRegistration.user_id == current_user.id
        ).first()
        
        if not registration:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Registration not found"
            )
        
        # Get related data
        event = db.query(Event).filter(Event.id == registration.event_id).first()
        ticket = None
        if registration.ticket_id:
            ticket = db.query(EventTicket).filter(EventTicket.id == registration.ticket_id).first()
        
        # Send confirmation email
        email_sent = ticket_email_service.send_ticket_confirmation(
            db=db,
            registration=registration,
            user=current_user,
            event=event,
            ticket=ticket
        )
        
        if email_sent:
            return {
                "message": "Confirmation email sent successfully",
                "registration_number": registration.registration_number,
                "email": current_user.email
            }
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to send confirmation email"
            )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to resend confirmation: {str(e)}"
        )
