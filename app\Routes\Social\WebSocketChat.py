"""
WebSocket Chat Routes for EduFair Platform

This module provides WebSocket endpoints for real-time messaging functionality.
Supports message sending/receiving, typing indicators, and user presence.
"""

import json
import asyncio
from datetime import datetime, timezone
from uuid import uuid4
from typing import Dict, Any

from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Depends, HTTPException, status
from sqlalchemy.orm import Session
from jose import JWTError, jwt

from config.session import get_db
from config.config import settings
from config.deps import get_current_user_from_token
from config.logging import get_logger
from services.websocket_manager import connection_manager
from Cruds.Social.Chat import send_message, mark_message_as_read, check_follow_relationship
# User model is imported directly below
from Schemas.Social.Chat import ChatMessageCreate, ChatMessageResponse
from Schemas.Social.WebSocketChat import (
    IncomingChatMessage, IncomingTypingIndicator, IncomingHeartbeat,
    IncomingMessageRead, OutgoingChatMessage, OutgoingMessageStatus,
    OutgoingTypingIndicator, OutgoingError, OutgoingHeartbeatResponse,
    WebSocketConnectionStats, UserConnectionInfo
)
from Models.users import User

router = APIRouter()
logger = get_logger(__name__)


# ==================== WEBSOCKET AUTHENTICATION ====================

async def authenticate_websocket(token: str, db: Session) -> User:
    """
    Authenticate WebSocket connection using JWT token.
    
    Args:
        token: JWT authentication token
        db: Database session
        
    Returns:
        User: Authenticated user object
        
    Raises:
        HTTPException: If authentication fails
    """
    try:
        payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM])
        user_id: str = payload.get("sub")
        if user_id is None:
            raise HTTPException(status_code=401, detail="Invalid token")
        
        user = db.query(User).filter(User.id == user_id).first()
        if user is None:
            raise HTTPException(status_code=401, detail="User not found")
        
        return user
        
    except JWTError:
        raise HTTPException(status_code=401, detail="Invalid token")


# ==================== WEBSOCKET ENDPOINTS ====================

@router.websocket("/ws/chat")
async def websocket_chat_endpoint(
    websocket: WebSocket,
    token: str,
    db: Session = Depends(get_db)
):
    """
    WebSocket endpoint for real-time chat messaging.
    
    Features:
    - Real-time message sending and receiving
    - Typing indicators
    - User presence (online/offline status)
    - Message read receipts
    - Heartbeat for connection monitoring
    
    Connection URL: ws://localhost:8000/api/social/chat/ws/chat?token=<jwt_token>
    
    Message Types:
    - send_message: Send a chat message
    - typing_start/typing_stop: Typing indicators
    - message_read: Mark message as read
    - heartbeat: Keep connection alive
    
    Example Usage:
    ```javascript
    const ws = new WebSocket('ws://localhost:8000/api/social/chat/ws/chat?token=' + authToken);
    
    // Send a message
    ws.send(JSON.stringify({
        type: 'send_message',
        receiver_id: 'user-uuid',
        message: 'Hello there!'
    }));
    
    // Start typing indicator
    ws.send(JSON.stringify({
        type: 'typing_start',
        receiver_id: 'user-uuid'
    }));
    ```
    """
    connection_id = str(uuid4())
    user = None
    
    try:
        # Authenticate user
        user = await authenticate_websocket(token, db)
        user_id = str(user.id)
        
        # Establish connection
        success = await connection_manager.connect(websocket, user_id, connection_id)
        if not success:
            await websocket.close(code=status.WS_1008_POLICY_VIOLATION)
            return
        
        logger.info(f"WebSocket chat connection established for user {user.username} ({user_id})")
        
        # Send initial user status to other online users
        await connection_manager.broadcast_to_online_users({
            "type": "user_online",
            "user_id": user_id,
            "username": user.username,
            "timestamp": datetime.now(timezone.utc).isoformat()
        }, exclude_user=user_id)
        
        # Main message loop
        while True:
            try:
                # Wait for message with timeout for heartbeat
                data = await asyncio.wait_for(websocket.receive_text(), timeout=60.0)
                message_data = json.loads(data)
                
                await handle_websocket_message(message_data, user, db, websocket)
                
            except asyncio.TimeoutError:
                # Send heartbeat request
                await websocket.send_text(json.dumps({
                    "type": "heartbeat_request",
                    "timestamp": datetime.now(timezone.utc).isoformat()
                }))
                
            except json.JSONDecodeError:
                await send_error(websocket, "invalid_json", "Invalid JSON format")
                
            except Exception as e:
                logger.error(f"Error handling WebSocket message for user {user_id}: {e}")
                await send_error(websocket, "message_error", str(e))
    
    except HTTPException as e:
        logger.warning(f"WebSocket authentication failed: {e.detail}")
        await websocket.close(code=status.WS_1008_POLICY_VIOLATION)
        return
        
    except WebSocketDisconnect:
        logger.info(f"WebSocket disconnected for user {user.username if user else 'unknown'}")
        
    except Exception as e:
        logger.error(f"Unexpected error in WebSocket connection: {e}")
        
    finally:
        # Clean up connection
        if user:
            user_id = str(user.id)
            connection_manager.disconnect(user_id, connection_id)
            
            # Notify other users about offline status if no more connections
            if not connection_manager.is_user_online(user_id):
                await connection_manager.broadcast_to_online_users({
                    "type": "user_offline",
                    "user_id": user_id,
                    "username": user.username,
                    "timestamp": datetime.now(timezone.utc).isoformat()
                })


async def handle_websocket_message(message_data: Dict[str, Any], user: User, db: Session, websocket: WebSocket):
    """
    Handle incoming WebSocket messages.
    
    Args:
        message_data: Parsed message data
        user: Authenticated user
        db: Database session
        websocket: WebSocket connection
    """
    message_type = message_data.get("type")
    user_id = str(user.id)
    
    try:
        if message_type == "send_message":
            await handle_send_message(message_data, user, db)
            
        elif message_type == "typing_start":
            await handle_typing_indicator(message_data, user, True)
            
        elif message_type == "typing_stop":
            await handle_typing_indicator(message_data, user, False)
            
        elif message_type == "message_read":
            await handle_message_read(message_data, user, db)
            
        elif message_type == "heartbeat":
            await handle_heartbeat(websocket)
            
        else:
            await send_error(websocket, "unknown_message_type", f"Unknown message type: {message_type}")
            
    except Exception as e:
        logger.error(f"Error handling message type {message_type} for user {user_id}: {e}")
        await send_error(websocket, "handler_error", str(e))


async def handle_send_message(message_data: Dict[str, Any], user: User, db: Session):
    """Handle sending a chat message."""
    try:
        # Validate message data
        chat_message = IncomingChatMessage(**message_data)
        
        # Create message using existing CRUD
        message_create = ChatMessageCreate(
            receiver_id=chat_message.receiver_id,
            message=chat_message.message
        )
        
        # Send message through CRUD (includes follow relationship check)
        sent_message = send_message(db, user.id, message_create)
        
        # Get receiver info
        receiver = db.query(User).filter(User.id == chat_message.receiver_id).first()
        if not receiver:
            return
        
        # Create WebSocket message for receiver
        ws_message = {
            "type": "new_message",
            "message_id": str(sent_message.id),
            "sender_id": str(user.id),
            "sender_username": user.username,
            "sender_profile_picture": user.profile_picture,
            "message": sent_message.message,
            "sent_at": sent_message.sent_at.isoformat(),
            "conversation_id": f"{min(str(user.id), str(receiver.id))}_{max(str(user.id), str(receiver.id))}"
        }
        
        # Send to receiver and sender
        await connection_manager.send_message_to_conversation(
            ws_message, str(user.id), str(receiver.id)
        )
        
        logger.info(f"Message sent from {user.username} to {receiver.username}")
        
    except Exception as e:
        logger.error(f"Error sending message: {e}")
        raise


async def handle_typing_indicator(message_data: Dict[str, Any], user: User, is_typing: bool):
    """Handle typing indicator messages."""
    try:
        typing_data = IncomingTypingIndicator(**message_data)
        
        # Send typing indicator to receiver
        indicator_message = {
            "type": "typing_start" if is_typing else "typing_stop",
            "user_id": str(user.id),
            "username": user.username,
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
        
        await connection_manager.send_personal_message(indicator_message, str(typing_data.receiver_id))
        
    except Exception as e:
        logger.error(f"Error handling typing indicator: {e}")
        raise


async def handle_message_read(message_data: Dict[str, Any], user: User, db: Session):
    """Handle message read notifications."""
    try:
        read_data = IncomingMessageRead(**message_data)
        
        # Mark message as read using existing CRUD
        mark_message_as_read(db, read_data.message_id, user.id)
        
        # Send read receipt (implementation depends on requirements)
        # For now, we'll just log it
        logger.info(f"Message {read_data.message_id} marked as read by {user.username}")
        
    except Exception as e:
        logger.error(f"Error marking message as read: {e}")
        raise


async def handle_heartbeat(websocket: WebSocket):
    """Handle heartbeat messages."""
    response = {
        "type": "heartbeat_response",
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "server_time": datetime.now(timezone.utc).isoformat()
    }
    await websocket.send_text(json.dumps(response))


async def send_error(websocket: WebSocket, error_code: str, message: str):
    """Send error message to WebSocket client."""
    error_message = {
        "type": "error",
        "error_code": error_code,
        "message": message,
        "timestamp": datetime.now(timezone.utc).isoformat()
    }
    try:
        await websocket.send_text(json.dumps(error_message))
    except Exception as e:
        logger.error(f"Failed to send error message: {e}")


# ==================== WEBSOCKET STATISTICS ENDPOINTS ====================

@router.get("/ws/stats", response_model=WebSocketConnectionStats)
async def get_websocket_stats():
    """
    Get WebSocket connection statistics.
    
    Returns current connection counts and activity metrics.
    """
    return WebSocketConnectionStats(
        total_connections=connection_manager.get_connection_count(),
        online_users=len(connection_manager.get_online_users()),
        active_conversations=0,  # Could be calculated if needed
        messages_per_minute=0.0,  # Would need message tracking
        average_response_time=0.0  # Would need response time tracking
    )


@router.get("/ws/online-users")
async def get_online_users(db: Session = Depends(get_db)):
    """
    Get list of currently online users.
    
    Returns basic information about users with active WebSocket connections.
    """
    online_user_ids = connection_manager.get_online_users()
    
    users_info = []
    for user_id in online_user_ids:
        user = db.query(User).filter(User.id == user_id).first()
        if user:
            users_info.append(UserConnectionInfo(
                user_id=user.id,
                username=user.username,
                is_online=True,
                last_seen=connection_manager.get_user_last_seen(user_id),
                connection_count=connection_manager.get_connection_count(user_id)
            ))
    
    return users_info
