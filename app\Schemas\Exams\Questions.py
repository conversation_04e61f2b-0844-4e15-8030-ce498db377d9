from pydantic import BaseModel, Field
from typing import Optional, List
from uuid import UUID
from enum import Enum
from datetime import datetime

class QuestionType(str, Enum):
    MCQS = "MCQS"
    SHORT = "SHORT"
    LONG = "LONG"

class QuestionLevel(str, Enum):
    EASY = "EASY"
    MEDIUM = "MEDIUM"
    HARD = "HARD"



class McqOptionBase(BaseModel):
    option_text: str
    is_correct: bool = False

class McqOptionCreate(McqOptionBase):
    pass

class McqOptionOut(McqOptionBase):
    id: UUID
    class Config:
        from_attributes = True

class McqOptionUpdate(BaseModel):
    option_text: str

class QuestionBase(BaseModel):
    text: str
    answer: str
    Type: QuestionType
    Level: QuestionLevel
    imageUrl: Optional[str] = None
    class_id: UUID
    subject_id: UUID
    chapter_id: UUID
    topic_id: Optional[UUID] = None
    subtopic_id: Optional[UUID] = None
    marks: int = 1  # New field for marks per question

class QuestionCreate(QuestionBase):
    options: Optional[List[McqOptionCreate]] = None

class QuestionUpdate(BaseModel):
    text: Optional[str] = None
    answer: Optional[str] = None
    Type: Optional[QuestionType] = None
    Level: Optional[QuestionLevel] = None
    imageUrl: Optional[str] = None
    class_id: Optional[UUID] = None
    subject_id: Optional[UUID] = None
    chapter_id: Optional[UUID] = None
    topic_id: Optional[UUID] = None
    subtopic_id: Optional[UUID] = None
    options: Optional[List[McqOptionCreate]] = None
    marks: Optional[int] = None  # New field for marks per question

class RelatedObject(BaseModel):
    id: UUID
    ClassNo: str
    class Config:
        from_attributes = True

class TeacherProfileOut(BaseModel):
    id: UUID
    user_id: UUID
    class Config:
        from_attributes = True

class ClassObject(BaseModel):
    id: UUID
    ClassNo: str
    class Config:
        from_attributes = True

class NameObject(BaseModel):
    id: UUID
    name: str
    class Config:
        from_attributes = True

class QuestionOut(QuestionBase):
    id: UUID
    class_: Optional[ClassObject]
    subject: Optional[NameObject]
    chapter: Optional[NameObject]
    topic: Optional[NameObject]
    subtopic: Optional[NameObject]
    options: Optional[List[McqOptionOut]] = None
    teacher_profile: Optional[TeacherProfileOut] = None
    teacher_id: Optional[UUID] = None
    class Config:
        from_attributes = True

class QuestionStudentOut(BaseModel):
    id: UUID
    text: str
    Type: QuestionType
    Level: QuestionLevel
    imageUrl: Optional[str] = None
    class_id: UUID
    subject_id: UUID
    chapter_id: UUID
    topic_id: Optional[UUID] = None
    subtopic_id: Optional[UUID] = None
    marks: int = 1
    class_: Optional[ClassObject]
    subject: Optional[NameObject]
    chapter: Optional[NameObject]
    topic: Optional[NameObject]
    subtopic: Optional[NameObject]
    options: Optional[List[McqOptionOut]] = None
    teacher_profile: Optional[TeacherProfileOut] = None
    teacher_id: Optional[UUID] = None
    class Config:
        from_attributes = True

# AI Generation Schemas
class AIQuestionGenRequest(BaseModel):
    class_: str = Field(..., alias="class", description="Class number (e.g., '10', '11', '12')")
    subject: str = Field(..., description="Subject name (e.g., 'Mathematics', 'Physics')")
    chapter: str = Field(..., description="Chapter name (e.g., 'Algebra', 'Mechanics')")
    no_of_questions: int = Field(..., ge=1, le=20, description="Total number of questions to generate (1-20)")
    topic: Optional[str] = Field(None, description="Topic name (optional)")
    subtopic: Optional[str] = Field(None, description="Subtopic name (optional)")
    # Custom difficulty distribution (optional)
    no_of_easy: Optional[int] = Field(None, ge=0, description="Number of easy questions (only when custom difficulty)")
    no_of_medium: Optional[int] = Field(None, ge=0, description="Number of medium questions (only when custom difficulty)")
    no_of_hard: Optional[int] = Field(None, ge=0, description="Number of hard questions (only when custom difficulty)")

    class Config:
        allow_population_by_field_name = True

