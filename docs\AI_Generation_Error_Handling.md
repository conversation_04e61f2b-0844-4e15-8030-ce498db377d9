# AI Question Generation Error Handling
## Comprehensive Error Management for External API Failures

---

## 🎯 **Problem Solved**

The AI question generation endpoint was throwing unhandled 500 errors when the Gemini AI service returned HTTP error codes like 503 (Service Unavailable). This created a poor user experience with cryptic error messages.

### **Original Error**:
```
httpx.HTTPStatusError: Server error '503 Service Unavailable' for url 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent'
```

---

## 🔧 **Solution Implemented**

### **1. Enhanced HTTP Error Handling**

**File**: `app/Cruds/Exams/Questions.py`

#### **Before** (Problematic):
```python
async with httpx.AsyncClient(timeout=60.0) as client:
    response = await client.post(url, json=payload, headers={"Content-Type": "application/json"})
    response.raise_for_status()  # ❌ Throws unhandled exception
    gemini_result = response.json()
```

#### **After** (Robust):
```python
try:
    async with httpx.AsyncClient(timeout=60.0) as client:
        response = await client.post(url, json=payload, headers={"Content-Type": "application/json"})
        
        # Handle different HTTP status codes gracefully
        if response.status_code == 503:
            return {
                "status": "error",
                "message": "AI service is temporarily unavailable. Please try again in a few minutes.",
                "questions": [],
                "context_available": context_available,
                "error_code": "SERVICE_UNAVAILABLE"
            }
        elif response.status_code == 429:
            return {
                "status": "error", 
                "message": "AI service rate limit exceeded. Please try again later.",
                "questions": [],
                "context_available": context_available,
                "error_code": "RATE_LIMIT_EXCEEDED"
            }
        # ... more error handling
        
        gemini_result = response.json()
        
except httpx.TimeoutException:
    return {
        "status": "error",
        "message": "AI service request timed out. Please try again.",
        "questions": [],
        "context_available": context_available,
        "error_code": "TIMEOUT"
    }
except httpx.RequestError as e:
    return {
        "status": "error",
        "message": "Failed to connect to AI service. Please check your internet connection and try again.",
        "questions": [],
        "context_available": context_available,
        "error_code": "CONNECTION_ERROR"
    }
```

### **2. Route-Level Error Mapping**

**File**: `app/Routes/Exams/Questions.py`

#### **Enhanced Route Handler**:
```python
@router.post("/ai-generate")
async def ai_generate_questions(request: AIQuestionGenRequest, ...):
    try:
        result = await enhanced_ai_question_generation(...)
        
        # Check if the result indicates an error and return appropriate HTTP status
        if result.get("status") == "error":
            error_code = result.get("error_code", "UNKNOWN_ERROR")
            
            # Map error codes to HTTP status codes
            if error_code == "SERVICE_UNAVAILABLE":
                raise HTTPException(status_code=503, detail=result.get("message"))
            elif error_code == "RATE_LIMIT_EXCEEDED":
                raise HTTPException(status_code=429, detail=result.get("message"))
            elif error_code == "TIMEOUT":
                raise HTTPException(status_code=408, detail=result.get("message"))
            elif error_code in ["CONNECTION_ERROR", "API_ERROR"]:
                raise HTTPException(status_code=502, detail=result.get("message"))
            else:
                raise HTTPException(status_code=500, detail=result.get("message"))
        
        return result
        
    except HTTPException:
        raise  # Re-raise HTTP exceptions as-is
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Unexpected error: {str(e)}")
```

---

## 📊 **Error Code Mapping**

### **HTTP Status Code Mapping**:

| External API Error | Internal Error Code | HTTP Status | User Message |
|-------------------|-------------------|-------------|--------------|
| 503 Service Unavailable | `SERVICE_UNAVAILABLE` | 503 | "AI service is temporarily unavailable. Please try again in a few minutes." |
| 429 Rate Limit | `RATE_LIMIT_EXCEEDED` | 429 | "AI service rate limit exceeded. Please try again later." |
| Timeout | `TIMEOUT` | 408 | "AI service request timed out. Please try again." |
| Connection Error | `CONNECTION_ERROR` | 502 | "Failed to connect to AI service. Please check your internet connection and try again." |
| 4xx Client Error | `API_ERROR` | 502 | "AI service error (HTTP {status_code}). Please try again later." |
| Unknown Error | `UNKNOWN_ERROR` | 500 | "Unexpected error while calling AI service: {error}" |

---

## 🎨 **Frontend Error Handling**

### **Expected API Response Format**:

#### **Success Response**:
```json
{
  "status": "generated",
  "message": "Questions generated successfully",
  "questions": [
    {
      "text": "What is 2+2?",
      "answer": "4",
      "Type": "MCQS",
      "Level": "easy",
      "marks": 1,
      "options": ["2", "3", "4", "5"]
    }
  ],
  "context_available": true
}
```

#### **Error Response**:
```json
{
  "status": "error",
  "message": "AI service is temporarily unavailable. Please try again in a few minutes.",
  "questions": [],
  "context_available": false,
  "error_code": "SERVICE_UNAVAILABLE"
}
```

### **Frontend Error Handling Example**:
```javascript
try {
  const response = await fetch('/api/questions/ai-generate', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(requestData)
  });
  
  if (!response.ok) {
    const errorData = await response.json();
    
    // Handle specific error types
    switch (response.status) {
      case 503:
        showUserMessage('AI service is temporarily down. Please try again in a few minutes.', 'warning');
        break;
      case 429:
        showUserMessage('Too many requests. Please wait before trying again.', 'warning');
        break;
      case 408:
        showUserMessage('Request timed out. Please try again.', 'warning');
        break;
      case 502:
        showUserMessage('Connection issue with AI service. Please try again.', 'error');
        break;
      default:
        showUserMessage('An error occurred. Please try again later.', 'error');
    }
    return;
  }
  
  const result = await response.json();
  
  if (result.status === 'error') {
    showUserMessage(result.message, 'error');
    return;
  }
  
  // Handle successful generation
  displayGeneratedQuestions(result.questions);
  
} catch (error) {
  showUserMessage('Network error. Please check your connection.', 'error');
}
```

---

## 🔄 **Retry Logic Recommendations**

### **Automatic Retry Strategy**:
```javascript
async function generateQuestionsWithRetry(requestData, maxRetries = 3) {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      const response = await fetch('/api/questions/ai-generate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(requestData)
      });
      
      if (response.ok) {
        return await response.json();
      }
      
      // Retry on specific status codes
      if ([503, 408, 502].includes(response.status) && attempt < maxRetries) {
        const delay = Math.pow(2, attempt) * 1000; // Exponential backoff
        await new Promise(resolve => setTimeout(resolve, delay));
        continue;
      }
      
      // Don't retry on client errors (4xx) or rate limits
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      
    } catch (error) {
      if (attempt === maxRetries) {
        throw error;
      }
      
      // Wait before retry
      const delay = Math.pow(2, attempt) * 1000;
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
}
```

---

## 🚀 **Benefits**

### **For Users**:
- ✅ **Clear Error Messages**: No more cryptic HTTP errors
- ✅ **Actionable Feedback**: Users know what to do (wait, retry, check connection)
- ✅ **Better UX**: Graceful degradation instead of crashes

### **For Developers**:
- ✅ **Proper HTTP Status Codes**: RESTful error responses
- ✅ **Structured Error Handling**: Consistent error format
- ✅ **Debugging Information**: Error codes for troubleshooting
- ✅ **Resilient System**: Handles external service failures gracefully

### **For System**:
- ✅ **No More 500 Crashes**: External API failures don't crash the app
- ✅ **Better Monitoring**: Clear error categorization
- ✅ **Improved Reliability**: System continues working even when AI service is down

---

## 🔍 **Testing the Fix**

### **Test Scenarios**:

1. **Service Unavailable (503)**:
   - Gemini API returns 503
   - Should return user-friendly message
   - HTTP 503 status code

2. **Rate Limit (429)**:
   - Too many requests to Gemini
   - Should return rate limit message
   - HTTP 429 status code

3. **Timeout**:
   - Request takes longer than 60 seconds
   - Should return timeout message
   - HTTP 408 status code

4. **Connection Error**:
   - Network connectivity issues
   - Should return connection error message
   - HTTP 502 status code

5. **Success Case**:
   - Gemini API works normally
   - Should return generated questions
   - HTTP 200 status code

---

The AI question generation system is now much more robust and provides a better user experience when external services are unavailable! 🎉
