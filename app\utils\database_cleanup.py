"""
Database cleanup utilities for fixing data issues
"""
from sqlalchemy.orm import Session
from Models.users import User, <PERSON>torProfile
from config.session import get_db
import logging

logger = logging.getLogger(__name__)


def cleanup_data_urls_in_database(db: Session) -> dict:
    """
    Clean up any data URLs that were incorrectly stored in the database
    
    Returns:
        Dictionary with cleanup statistics
    """
    stats = {
        "users_fixed": 0,
        "mentor_profiles_fixed": 0,
        "total_issues_found": 0
    }
    
    try:
        # Fix user profile pictures
        users_with_data_urls = db.query(User).filter(
            User.profile_picture.like('data:%')
        ).all()
        
        for user in users_with_data_urls:
            logger.warning(f"Found data URL in user {user.id} profile_picture field")
            user.profile_picture = None  # Clear the invalid data URL
            stats["users_fixed"] += 1
            stats["total_issues_found"] += 1
        
        # Fix mentor profile images
        mentor_profiles_with_data_urls = db.query(MentorProfile).filter(
            MentorProfile.profile_image_url.like('data:%')
        ).all()
        
        for profile in mentor_profiles_with_data_urls:
            logger.warning(f"Found data URL in mentor profile {profile.id} profile_image_url field")
            profile.profile_image_url = None  # Clear the invalid data URL
            stats["mentor_profiles_fixed"] += 1
            stats["total_issues_found"] += 1
        
        # Commit changes
        if stats["total_issues_found"] > 0:
            db.commit()
            logger.info(f"Database cleanup completed: {stats}")
        else:
            logger.info("No data URL issues found in database")
            
    except Exception as e:
        db.rollback()
        logger.error(f"Error during database cleanup: {e}")
        stats["error"] = str(e)
    
    return stats


def validate_profile_picture_url(url: str) -> bool:
    """
    Validate that a profile picture URL is a valid file path, not a data URL
    
    Args:
        url: The URL to validate
        
    Returns:
        True if valid, False if it's a data URL or invalid
    """
    if not url:
        return True  # None/empty is valid
    
    # Reject data URLs
    if url.startswith('data:'):
        return False
    
    # Reject very long strings (likely data URLs)
    if len(url) > 255:
        return False
    
    # Should be a relative file path
    return True


def safe_set_profile_picture(user: User, profile_picture_url: str) -> bool:
    """
    Safely set a user's profile picture, validating the URL first
    
    Args:
        user: User object to update
        profile_picture_url: URL to set
        
    Returns:
        True if set successfully, False if URL was invalid
    """
    if validate_profile_picture_url(profile_picture_url):
        user.profile_picture = profile_picture_url
        return True
    else:
        logger.warning(f"Rejected invalid profile picture URL for user {user.id}: {profile_picture_url[:100]}...")
        return False


def safe_set_mentor_profile_image(mentor_profile: MentorProfile, image_url: str) -> bool:
    """
    Safely set a mentor's profile image, validating the URL first
    
    Args:
        mentor_profile: MentorProfile object to update
        image_url: URL to set
        
    Returns:
        True if set successfully, False if URL was invalid
    """
    if validate_profile_picture_url(image_url):
        mentor_profile.profile_image_url = image_url
        return True
    else:
        logger.warning(f"Rejected invalid profile image URL for mentor {mentor_profile.id}: {image_url[:100]}...")
        return False
