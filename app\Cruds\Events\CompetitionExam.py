"""
CRUD operations for Competition Exam Attempts

This module handles competition exam attempts, allowing students to attempt
exams linked to competition events.
"""

import uuid
from datetime import datetime, timezone, timedelta
from typing import Dict, Any, List, Optional, Tuple
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, or_, func, desc
from fastapi import HTTPException, status
from decimal import Decimal

from Models.Events import Event
from Models.Exam import Exam, StudentExamAttempt, StudentExamAnswer, StudentExamAssignment
from Models.Questions import Question
from Models.users import User
from Models.ExamSession import ExamSession
from Schemas.Events.CompetitionExam import (
    CompetitionExamInfo, CompetitionExamAttemptRequest, CompetitionExamAttemptResponse,
    CompetitionExamForStudent, CompetitionExamQuestions, CompetitionExamStatus,
    CompetitionLeaderboard, CompetitionResults
)
from Schemas.Exams.Questions import QuestionStudentOut


def _get_competition_exam_id(competition: Event) -> uuid.UUID:
    """Helper function to get exam ID from either competition_exam_id or exam_id field"""
    return competition.competition_exam_id or competition.exam_id


def get_competition_exam_info(db: Session, event_id: uuid.UUID, student_id: uuid.UUID) -> CompetitionExamForStudent:
    """Get competition exam information for a student"""

    # Get competition event with exam details - check both exam fields
    competition = db.query(Event).options(
        joinedload(Event.exam)
    ).filter(
        Event.id == event_id,
        Event.is_competition == True,
        or_(Event.competition_exam_id.isnot(None), Event.exam_id.isnot(None))
    ).first()
    
    if not competition:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Competition not found or not linked to an exam"
        )
    
    # Get the exam ID from whichever field has it
    exam_id = _get_competition_exam_id(competition)
    if not exam_id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Competition is not linked to an exam"
        )

    if not competition.exam:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Competition exam not found"
        )

    # Get current time
    now = datetime.now(timezone.utc)

    # Check if student is registered (has an exam attempt record)
    existing_attempt = db.query(StudentExamAttempt).filter(
        StudentExamAttempt.exam_id == exam_id,
        StudentExamAttempt.student_id == student_id
    ).first()

    # Get current participant count
    current_participants = db.query(StudentExamAttempt).filter(
        StudentExamAttempt.exam_id == exam_id
    ).count()

    # Get question count
    question_count = db.query(func.count(Question.id)).join(
        Exam.questions
    ).filter(Exam.id == exam_id).scalar() or 0
    
    # Determine registration and exam status
    registration_open = (
        competition.registration_start is None or now >= competition.registration_start
    ) and (
        competition.registration_end is None or now <= competition.registration_end
    )
    
    exam_active = now >= competition.start_datetime and now <= competition.end_datetime
    
    # Calculate time remaining if exam is active
    time_remaining = None
    if exam_active:
        time_remaining = int((competition.end_datetime - now).total_seconds() / 60)
    
    # Determine if student can attempt
    can_attempt = (
        existing_attempt is not None and
        exam_active and
        (existing_attempt.completed_at is None)
    )
    
    return CompetitionExamForStudent(
        event_id=competition.id,
        event_title=competition.title,
        event_description=competition.description,
        start_datetime=competition.start_datetime,
        end_datetime=competition.end_datetime,
        exam_id=competition.exam.id,
        exam_title=competition.exam.title,
        exam_description=competition.exam.description,
        total_marks=competition.exam.total_marks,
        total_duration=competition.exam.total_duration,
        total_questions=question_count,
        competition_rules=competition.competition_rules,
        prize_details=competition.prize_details,
        max_attendees=competition.max_attendees,
        current_participants=current_participants,
        is_registered=existing_attempt is not None,
        can_attempt=can_attempt,
        attempt_id=existing_attempt.id if existing_attempt else None,
        attempt_status=existing_attempt.status if existing_attempt else None,
        registration_open=registration_open,
        exam_active=exam_active,
        time_remaining=time_remaining
    )


def register_for_competition_exam(db: Session, event_id: uuid.UUID, student_id: uuid.UUID) -> Dict[str, Any]:
    """Register a student for a competition exam"""

    # Get competition details - check both exam fields
    competition = db.query(Event).filter(
        Event.id == event_id,
        Event.is_competition == True,
        or_(Event.competition_exam_id.isnot(None), Event.exam_id.isnot(None))
    ).first()

    if not competition:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Competition not found"
        )

    # Get the exam ID from whichever field has it
    exam_id = _get_competition_exam_id(competition)
    if not exam_id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Competition is not linked to an exam"
        )
    
    # Check if registration is open
    now = datetime.now(timezone.utc)
    if competition.registration_end and now > competition.registration_end:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Competition registration is closed"
        )
    
    if now >= competition.start_datetime:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Competition has already started"
        )
    
    # Check if student is already registered (check both assignment and attempt)
    existing_assignment = db.query(StudentExamAssignment).filter(
        StudentExamAssignment.exam_id == exam_id,
        StudentExamAssignment.student_id == student_id
    ).first()

    existing_attempt = db.query(StudentExamAttempt).filter(
        StudentExamAttempt.exam_id == exam_id,
        StudentExamAttempt.student_id == student_id
    ).first()

    if existing_assignment or existing_attempt:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Already registered for this competition"
        )

    # Check max attendees limit
    if competition.max_attendees:
        current_participants = db.query(StudentExamAssignment).filter(
            StudentExamAssignment.exam_id == exam_id
        ).count()

        if current_participants >= competition.max_attendees:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Competition is full"
            )
    
    try:
        # 1. Create exam assignment (this assigns the student to the exam)
        exam_assignment = StudentExamAssignment(
            exam_id=exam_id,
            student_id=student_id,
            assigned_at=datetime.now(timezone.utc),
            status="assigned",
            assignment_source="competition"
        )
        db.add(exam_assignment)

        # 2. Create exam attempt record for registration tracking
        exam_attempt = StudentExamAttempt(
            id=uuid.uuid4(),  # Generate UUID for the attempt
            exam_id=exam_id,
            student_id=student_id,
            started_at=None,  # Will be set when student starts
            completed_at=None,
            status="registered"
        )
        db.add(exam_attempt)

        # Commit both records
        db.commit()
        db.refresh(exam_assignment)
        db.refresh(exam_attempt)

        return {
            "success": True,
            "message": "Successfully registered for competition and assigned to exam",
            "competition_id": event_id,
            "exam_id": exam_id,
            "assignment_id": f"{exam_assignment.exam_id}_{exam_assignment.student_id}",
            "attempt_id": exam_attempt.id
        }
        
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error registering for competition: {str(e)}"
        )


def start_competition_exam_attempt(db: Session, event_id: uuid.UUID, student_id: uuid.UUID) -> CompetitionExamAttemptResponse:
    """Start a competition exam attempt"""

    # Get competition and exam details - check both exam fields
    competition = db.query(Event).options(
        joinedload(Event.exam)
    ).filter(
        Event.id == event_id,
        Event.is_competition == True,
        or_(Event.competition_exam_id.isnot(None), Event.exam_id.isnot(None))
    ).first()

    if not competition:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Competition not found"
        )

    # Get the exam ID from whichever field has it
    exam_id = _get_competition_exam_id(competition)
    if not exam_id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Competition is not linked to an exam"
        )
    
    # Check if competition is active
    now = datetime.now(timezone.utc)
    if now < competition.start_datetime:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Competition has not started yet"
        )
    
    if now > competition.end_datetime:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Competition has ended"
        )
    
    # Check if student is registered
    existing_attempt = db.query(StudentExamAttempt).filter(
        StudentExamAttempt.exam_id == exam_id,
        StudentExamAttempt.student_id == student_id
    ).first()
    
    if not existing_attempt:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Student not registered for this competition"
        )
    
    # Check if already completed
    if existing_attempt.completed_at:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Competition exam already completed"
        )
    
    try:
        # Update attempt to mark as started
        if not existing_attempt.started_at:
            existing_attempt.started_at = now
            existing_attempt.status = "in_progress"
            db.commit()
        
        # Create exam session for tracking
        session_id = str(uuid.uuid4())
        exam_session = ExamSession(
            session_id=session_id,
            student_id=student_id,
            exam_id=exam_id,
            status="active",
            start_time=now,
            duration=competition.exam.total_duration * 60,  # Convert to seconds
            strikes=0,
            last_heartbeat=now,
            session_data={
                "exam_title": competition.exam.title,
                "competition_id": str(event_id),
                "competition_title": competition.title,
                "answers": {}
            }
        )
        
        db.add(exam_session)
        db.commit()
        
        # Prepare exam info
        exam_info = CompetitionExamInfo(
            event_id=event_id,
            event_title=competition.title,
            exam_id=competition.exam.id,
            exam_title=competition.exam.title,
            exam_description=competition.exam.description,
            total_marks=competition.exam.total_marks,
            total_duration=competition.exam.total_duration,
            start_time=competition.start_datetime,
            end_time=competition.end_datetime,
            competition_rules=competition.competition_rules,
            prize_details=competition.prize_details
        )
        
        return CompetitionExamAttemptResponse(
            success=True,
            message="Competition exam attempt started successfully",
            attempt_id=existing_attempt.id,
            exam_info=exam_info,
            session_id=session_id
        )

    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error starting competition exam: {str(e)}"
        )


def get_competition_exam_questions(db: Session, event_id: uuid.UUID, student_id: uuid.UUID) -> CompetitionExamQuestions:
    """Get competition exam questions for student attempt"""

    # Get competition and exam details - check both exam fields
    competition = db.query(Event).options(
        joinedload(Event.exam).joinedload(Exam.questions)
    ).filter(
        Event.id == event_id,
        Event.is_competition == True,
        or_(Event.competition_exam_id.isnot(None), Event.exam_id.isnot(None))
    ).first()

    if not competition:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Competition not found"
        )

    # Get the exam ID from whichever field has it
    exam_id = _get_competition_exam_id(competition)
    if not exam_id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Competition is not linked to an exam"
        )

    # Check if student has an active attempt
    attempt = db.query(StudentExamAttempt).filter(
        StudentExamAttempt.exam_id == exam_id,
        StudentExamAttempt.student_id == student_id,
        StudentExamAttempt.started_at.isnot(None),
        StudentExamAttempt.completed_at.is_(None)
    ).first()

    if not attempt:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="No active exam attempt found"
        )

    # Get active exam session
    session = db.query(ExamSession).filter(
        ExamSession.student_id == student_id,
        ExamSession.exam_id == exam_id,
        ExamSession.status == "active"
    ).first()

    if not session:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="No active exam session found"
        )

    # Calculate time remaining
    now = datetime.now(timezone.utc)
    session_end_time = session.start_time + timedelta(seconds=session.duration)
    competition_end_time = competition.end_datetime

    # Use the earlier of session end time or competition end time
    effective_end_time = min(session_end_time, competition_end_time)
    time_remaining = max(0, int((effective_end_time - now).total_seconds() / 60))

    # Convert questions to student format
    questions = []
    for question in competition.exam.questions:
        question_student = QuestionStudentOut.from_orm(question)
        questions.append(question_student)

    # Prepare exam info
    exam_info = CompetitionExamInfo(
        event_id=event_id,
        event_title=competition.title,
        exam_id=competition.exam.id,
        exam_title=competition.exam.title,
        exam_description=competition.exam.description,
        total_marks=competition.exam.total_marks,
        total_duration=competition.exam.total_duration,
        start_time=competition.start_datetime,
        end_time=competition.end_datetime,
        competition_rules=competition.competition_rules,
        prize_details=competition.prize_details
    )

    return CompetitionExamQuestions(
        exam_info=exam_info,
        questions=questions,
        attempt_id=attempt.id,
        session_id=session.session_id,
        time_remaining=time_remaining
    )


def get_competition_exam_status(db: Session, event_id: uuid.UUID, student_id: uuid.UUID) -> CompetitionExamStatus:
    """Get competition exam attempt status for a student"""

    # Get competition details - check both exam fields
    competition = db.query(Event).filter(
        Event.id == event_id,
        Event.is_competition == True,
        or_(Event.competition_exam_id.isnot(None), Event.exam_id.isnot(None))
    ).first()

    if not competition:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Competition not found"
        )

    # Get the exam ID from whichever field has it
    exam_id = _get_competition_exam_id(competition)
    if not exam_id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Competition is not linked to an exam"
        )

    # Get student's attempt
    attempt = db.query(StudentExamAttempt).filter(
        StudentExamAttempt.exam_id == exam_id,
        StudentExamAttempt.student_id == student_id
    ).first()

    # Determine current status
    now = datetime.now(timezone.utc)
    is_registered = attempt is not None
    can_start = (
        is_registered and
        attempt.started_at is None and
        now >= competition.start_datetime and
        now <= competition.end_datetime
    )
    can_continue = (
        is_registered and
        attempt.started_at is not None and
        attempt.completed_at is None and
        now <= competition.end_datetime
    )

    # Calculate time remaining
    time_remaining = None
    if now >= competition.start_datetime and now <= competition.end_datetime:
        time_remaining = int((competition.end_datetime - now).total_seconds() / 60)

    # Get score if available
    score = None
    if attempt and attempt.completed_at:
        # Calculate score from AI or teacher results
        from Models.Exam import StudentExamAIResult, StudentExamTeacherResult

        # Try teacher result first, then AI result
        teacher_result = db.query(StudentExamTeacherResult).filter(
            StudentExamTeacherResult.attempt_id == attempt.id
        ).first()

        if teacher_result:
            score = teacher_result.total_score
        else:
            ai_result = db.query(StudentExamAIResult).filter(
                StudentExamAIResult.attempt_id == attempt.id
            ).first()
            if ai_result:
                score = ai_result.total_score

    return CompetitionExamStatus(
        event_id=event_id,
        exam_id=exam_id,
        attempt_id=attempt.id if attempt else None,
        status=attempt.status if attempt else "not_registered",
        is_registered=is_registered,
        can_start=can_start,
        can_continue=can_continue,
        started_at=attempt.started_at if attempt else None,
        completed_at=attempt.completed_at if attempt else None,
        time_remaining=time_remaining,
        score=score
    )


def get_competition_results(db: Session, event_id: uuid.UUID, student_id: Optional[uuid.UUID] = None, limit: int = 10) -> CompetitionResults:
    """Get competition results and leaderboard"""

    # Get competition details - check both exam fields
    competition = db.query(Event).filter(
        Event.id == event_id,
        Event.is_competition == True,
        or_(Event.competition_exam_id.isnot(None), Event.exam_id.isnot(None))
    ).first()

    if not competition:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Competition not found"
        )

    # Get the exam ID from whichever field has it
    exam_id = _get_competition_exam_id(competition)
    if not exam_id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Competition is not linked to an exam"
        )

    # Get all completed attempts with scores
    from Models.Exam import StudentExamAIResult, StudentExamTeacherResult

    # Query for attempts with scores (prefer teacher results over AI results)
    attempts_query = db.query(
        StudentExamAttempt,
        User.username,
        func.coalesce(StudentExamTeacherResult.total_score, StudentExamAIResult.total_score).label('score'),
        func.coalesce(StudentExamTeacherResult.total_marks, StudentExamAIResult.total_marks).label('total_marks')
    ).join(
        User, StudentExamAttempt.student_id == User.id
    ).outerjoin(
        StudentExamTeacherResult, StudentExamAttempt.id == StudentExamTeacherResult.attempt_id
    ).outerjoin(
        StudentExamAIResult, StudentExamAttempt.id == StudentExamAIResult.attempt_id
    ).filter(
        StudentExamAttempt.exam_id == exam_id,
        StudentExamAttempt.completed_at.isnot(None),
        or_(
            StudentExamTeacherResult.total_score.isnot(None),
            StudentExamAIResult.total_score.isnot(None)
        )
    ).order_by(
        desc(func.coalesce(StudentExamTeacherResult.total_score, StudentExamAIResult.total_score)),
        StudentExamAttempt.completed_at
    )

    all_results = attempts_query.all()

    # Calculate statistics
    total_participants = db.query(StudentExamAttempt).filter(
        StudentExamAttempt.exam_id == exam_id
    ).count()

    completed_attempts = len(all_results)

    scores = [float(result.score) for result in all_results if result.score is not None]
    average_score = Decimal(sum(scores) / len(scores)) if scores else None
    highest_score = Decimal(max(scores)) if scores else None

    # Build leaderboard
    leaderboard = []
    for rank, result in enumerate(all_results[:limit], 1):
        attempt, username, score, total_marks = result

        # Calculate time taken
        time_taken = None
        if attempt.started_at and attempt.completed_at:
            time_taken = int((attempt.completed_at - attempt.started_at).total_seconds() / 60)

        # Calculate percentage
        percentage = Decimal(0)
        if score and total_marks and total_marks > 0:
            percentage = Decimal(score) / Decimal(total_marks) * 100

        leaderboard.append(CompetitionLeaderboard(
            rank=rank,
            student_id=attempt.student_id,
            student_name=username,
            score=Decimal(score) if score else Decimal(0),
            total_marks=int(total_marks) if total_marks else 0,
            percentage=percentage,
            time_taken=time_taken,
            completed_at=attempt.completed_at
        ))

    # Find student's result if student_id provided
    student_result = None
    if student_id:
        for rank, result in enumerate(all_results, 1):
            attempt, username, score, total_marks = result
            if attempt.student_id == student_id:
                time_taken = None
                if attempt.started_at and attempt.completed_at:
                    time_taken = int((attempt.completed_at - attempt.started_at).total_seconds() / 60)

                percentage = Decimal(0)
                if score and total_marks and total_marks > 0:
                    percentage = Decimal(score) / Decimal(total_marks) * 100

                student_result = CompetitionLeaderboard(
                    rank=rank,
                    student_id=attempt.student_id,
                    student_name=username,
                    score=Decimal(score) if score else Decimal(0),
                    total_marks=int(total_marks) if total_marks else 0,
                    percentage=percentage,
                    time_taken=time_taken,
                    completed_at=attempt.completed_at
                )
                break

    return CompetitionResults(
        event_id=event_id,
        event_title=competition.title,
        total_participants=total_participants,
        completed_attempts=completed_attempts,
        average_score=average_score,
        highest_score=highest_score,
        leaderboard=leaderboard,
        student_result=student_result
    )
