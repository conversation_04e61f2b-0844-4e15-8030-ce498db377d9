"""
Class/Grade-wise Analytics CRUD Operations for EduFair Platform

This module contains CRUD operations for class and grade-level student analytics.
"""

import uuid
from datetime import datetime, timedelta, timezone
from typing import List, Dict, Any, Optional
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import func, desc, asc, and_, or_, case
from fastapi import HTTPException, status

# Import Models
from Models.users import User, UserTypeEnum, Subject
from Models.Classroom import Classroom, StudentClassroom
from Models.Tasks import Task, TaskStudents, TaskStatus
from Models.Exam import Exam, StudentExamAssignment, StudentExamAttempt, StudentExamAIResult, StudentExamTeacherResult
from Models.Questions import Question
from Models.Class import ClassNumber

# Import Schemas
from Schemas.StudentAnalytics import (
    ClassGradeAnalyticsRequest, ClassGradeAnalyticsResponse, ClassPerformanceMetrics,
    GradeAnalyticsDetail, AnalyticsTimeRange, RankingInfo
)

# Import caching utilities (temporarily disabled)
# from utils.AnalyticsCache import cache_analytics, get_cache_ttl


# @cache_analytics("class_grade_analytics", ttl_minutes=get_cache_ttl("class_grade_analytics"))
def get_class_grade_analytics(
    db: Session,
    student_id: uuid.UUID,
    request: ClassGradeAnalyticsRequest
) -> ClassGradeAnalyticsResponse:
    """
    Get comprehensive class and grade-level analytics for a student
    """
    # Verify student exists
    student = db.query(User).filter(
        User.id == student_id,
        User.user_type == UserTypeEnum.student
    ).first()
    
    if not student:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Student not found"
        )
    
    # Get student's classrooms
    classrooms = _get_student_classrooms(db, student_id, request.time_range)
    
    # Analyze performance in each classroom
    class_performances = []
    for classroom in classrooms:
        performance = _analyze_classroom_performance(
            db, student_id, classroom, request.time_range, request.include_peer_comparison
        )
        class_performances.append(performance)
    
    # Get grade-level analytics
    grade_analytics = _analyze_grade_performance(
        db, student_id, request.time_range
    )
    
    # Determine overall academic standing
    overall_standing = _determine_academic_standing(class_performances, grade_analytics)
    
    # Generate improvement recommendations
    recommendations = _generate_class_grade_recommendations(
        class_performances, grade_analytics
    )
    
    return ClassGradeAnalyticsResponse(
        student_id=student_id,
        time_range=request.time_range,
        class_performance=class_performances,
        grade_analytics=grade_analytics,
        overall_academic_standing=overall_standing,
        improvement_recommendations=recommendations,
        last_updated=datetime.now(timezone.utc)
    )


def _get_student_classrooms(
    db: Session, 
    student_id: uuid.UUID, 
    time_range: AnalyticsTimeRange
) -> List[Classroom]:
    """Get all classrooms the student is enrolled in"""
    
    classrooms = db.query(Classroom).join(
        StudentClassroom, Classroom.id == StudentClassroom.classroom_id
    ).filter(
        StudentClassroom.student_id == student_id,
        # Note: StudentClassroom doesn't have joined_at field, using created_at instead
        StudentClassroom.created_at >= time_range.start_date,
        StudentClassroom.created_at <= time_range.end_date
    ).all()
    
    return classrooms


def _analyze_classroom_performance(
    db: Session,
    student_id: uuid.UUID,
    classroom: Classroom,
    time_range: AnalyticsTimeRange,
    include_peer_comparison: bool
) -> ClassPerformanceMetrics:
    """Analyze student's performance in a specific classroom"""
    
    # Get all students in the classroom
    classroom_students = db.query(StudentClassroom).filter(
        StudentClassroom.classroom_id == classroom.id
    ).all()
    
    total_students = len(classroom_students)
    
    # Calculate student's performance in this classroom
    student_performance = _calculate_classroom_student_performance(
        db, student_id, classroom.id, time_range
    )
    
    # Calculate class average
    class_average = _calculate_classroom_average(
        db, classroom.id, time_range
    )
    
    # Calculate top performer score
    top_performer_score = _get_top_performer_score(
        db, classroom.id, time_range
    )
    
    # Calculate student's rank in classroom
    student_rank = _calculate_classroom_rank(
        db, student_id, classroom.id, time_range
    )
    
    # Get subject-wise rankings
    subject_rankings = _get_classroom_subject_rankings(
        db, student_id, classroom.id, time_range
    )
    
    # Peer comparison data
    students_above = student_rank - 1
    students_below = total_students - student_rank
    similar_performers = []
    
    if include_peer_comparison:
        similar_performers = _get_similar_performers(
            db, student_id, classroom.id, student_performance['average'], time_range
        )
    
    # Get rank history
    rank_history = _get_rank_history(
        db, student_id, classroom.id, time_range
    )
    
    # Determine rank trend
    rank_trend = _determine_rank_trend(rank_history)
    
    return ClassPerformanceMetrics(
        class_id=classroom.id,
        class_name=classroom.name,
        grade_level=_get_classroom_grade_level(db, classroom.id),
        student_rank=student_rank,
        total_students=total_students,
        percentile=(total_students - student_rank + 1) / total_students * 100,
        student_average=student_performance['average'],
        class_average=class_average,
        top_performer_score=top_performer_score,
        performance_gap=top_performer_score - student_performance['average'],
        subject_rankings=subject_rankings,
        students_above=students_above,
        students_below=students_below,
        similar_performers=similar_performers,
        rank_history=rank_history,
        rank_trend=rank_trend
    )


def _calculate_classroom_student_performance(
    db: Session,
    student_id: uuid.UUID,
    classroom_id: uuid.UUID,
    time_range: AnalyticsTimeRange
) -> Dict[str, float]:
    """Calculate student's overall performance in a classroom"""
    
    # Get exam scores in this classroom
    exam_scores = []
    exam_attempts = db.query(StudentExamAttempt).join(
        Exam, StudentExamAttempt.exam_id == Exam.id
    ).join(
        StudentExamAssignment, and_(
            StudentExamAssignment.exam_id == Exam.id,
            StudentExamAssignment.student_id == student_id
        )
    ).filter(
        StudentExamAttempt.student_id == student_id,
        StudentExamAttempt.started_at >= time_range.start_date,
        StudentExamAttempt.started_at <= time_range.end_date,
        StudentExamAttempt.completed_at.isnot(None)
    ).all()
    
    for attempt in exam_attempts:
        score = _get_attempt_score(db, attempt.id)
        if score is not None:
            exam_scores.append(score)
    
    # Get assignment scores in this classroom
    assignment_scores = []
    assignments = db.query(TaskStudents).join(
        Task, TaskStudents.task_id == Task.id
    ).filter(
        TaskStudents.student_id == student_id,
        Task.created_at >= time_range.start_date,
        Task.created_at <= time_range.end_date,
        TaskStudents.grade.isnot(None)
    ).all()
    
    for assignment in assignments:
        assignment_scores.append(assignment.grade)
    
    # Calculate weighted average (exams 70%, assignments 30%)
    exam_avg = sum(exam_scores) / len(exam_scores) if exam_scores else 0
    assignment_avg = sum(assignment_scores) / len(assignment_scores) if assignment_scores else 0
    
    if exam_scores and assignment_scores:
        overall_avg = exam_avg * 0.7 + assignment_avg * 0.3
    elif exam_scores:
        overall_avg = exam_avg
    elif assignment_scores:
        overall_avg = assignment_avg
    else:
        overall_avg = 0
    
    return {
        'average': overall_avg,
        'exam_average': exam_avg,
        'assignment_average': assignment_avg
    }


def _calculate_classroom_average(
    db: Session,
    classroom_id: uuid.UUID,
    time_range: AnalyticsTimeRange
) -> float:
    """Calculate the average performance of all students in a classroom"""
    
    # Get all students in classroom
    students = db.query(StudentClassroom.student_id).filter(
        StudentClassroom.classroom_id == classroom_id
    ).all()
    
    if not students:
        return 0.0
    
    total_scores = []
    for student_tuple in students:
        student_id = student_tuple[0]
        performance = _calculate_classroom_student_performance(
            db, student_id, classroom_id, time_range
        )
        if performance['average'] > 0:
            total_scores.append(performance['average'])
    
    return sum(total_scores) / len(total_scores) if total_scores else 0.0


def _get_top_performer_score(
    db: Session,
    classroom_id: uuid.UUID,
    time_range: AnalyticsTimeRange
) -> float:
    """Get the highest performance score in the classroom"""
    
    students = db.query(StudentClassroom.student_id).filter(
        StudentClassroom.classroom_id == classroom_id
    ).all()
    
    max_score = 0.0
    for student_tuple in students:
        student_id = student_tuple[0]
        performance = _calculate_classroom_student_performance(
            db, student_id, classroom_id, time_range
        )
        max_score = max(max_score, performance['average'])
    
    return max_score


def _calculate_classroom_rank(
    db: Session,
    student_id: uuid.UUID,
    classroom_id: uuid.UUID,
    time_range: AnalyticsTimeRange
) -> int:
    """Calculate student's rank in the classroom"""
    
    # Get all students' performances
    students = db.query(StudentClassroom.student_id).filter(
        StudentClassroom.classroom_id == classroom_id
    ).all()
    
    performances = []
    student_score = 0
    
    for student_tuple in students:
        sid = student_tuple[0]
        performance = _calculate_classroom_student_performance(
            db, sid, classroom_id, time_range
        )
        performances.append((sid, performance['average']))
        if sid == student_id:
            student_score = performance['average']
    
    # Sort by performance (descending)
    performances.sort(key=lambda x: x[1], reverse=True)
    
    # Find student's rank
    for rank, (sid, score) in enumerate(performances, 1):
        if sid == student_id:
            return rank
    
    return len(performances)  # Fallback


def _get_classroom_subject_rankings(
    db: Session,
    student_id: uuid.UUID,
    classroom_id: uuid.UUID,
    time_range: AnalyticsTimeRange
) -> Dict[str, RankingInfo]:
    """Get subject-wise rankings in the classroom"""
    
    # Since we don't have detailed subject-wise ranking implementation,
    # return empty dict for now
    return {}


def _get_similar_performers(
    db: Session,
    student_id: uuid.UUID,
    classroom_id: uuid.UUID,
    student_score: float,
    time_range: AnalyticsTimeRange
) -> List[Dict[str, Any]]:
    """Get students with similar performance levels"""

    # Get all students' performances
    students = db.query(StudentClassroom).join(
        User, StudentClassroom.student_id == User.id
    ).filter(
        StudentClassroom.classroom_id == classroom_id,
        StudentClassroom.student_id != student_id
    ).all()

    similar_performers = []
    score_range = 5.0  # ±5 points

    for student_classroom in students:
        performance = _calculate_classroom_student_performance(
            db, student_classroom.student_id, classroom_id, time_range
        )

        if abs(performance['average'] - student_score) <= score_range:
            similar_performers.append({
                'student_id': str(student_classroom.student_id),
                'student_name': student_classroom.student.username,
                'score': performance['average']
            })

    return similar_performers[:5]  # Return top 5 similar performers


def _get_rank_history(
    db: Session,
    student_id: uuid.UUID,
    classroom_id: uuid.UUID,
    time_range: AnalyticsTimeRange
) -> List[Dict[str, Any]]:
    """Get historical ranking data for the student"""

    # Since we don't have historical ranking tracking implemented,
    # return empty list for now
    return []


def _determine_rank_trend(rank_history: List[Dict[str, Any]]) -> str:
    """Determine the trend in ranking"""

    if len(rank_history) < 2:
        return "stable"

    recent_rank = rank_history[-1]['rank']
    previous_rank = rank_history[-2]['rank']

    if recent_rank < previous_rank:
        return "improving"
    elif recent_rank > previous_rank:
        return "declining"
    else:
        return "stable"


def _analyze_grade_performance(
    db: Session,
    student_id: uuid.UUID,
    time_range: AnalyticsTimeRange
) -> GradeAnalyticsDetail:
    """Analyze student's performance at grade level"""

    # Get student's grade level
    grade_level = _get_student_grade_level(db, student_id)

    # Get all students in the same grade
    grade_students = _get_students_in_grade(db, grade_level)
    total_students_in_grade = len(grade_students)

    # Calculate student's performance across all subjects
    student_gpa = _calculate_student_gpa(db, student_id, time_range)

    # Calculate grade average GPA
    grade_average_gpa = _calculate_grade_average_gpa(db, grade_level, time_range)

    # Get top GPA in grade
    top_gpa_in_grade = _get_top_gpa_in_grade(db, grade_level, time_range)

    # Calculate student's rank in grade
    student_rank_in_grade = _calculate_grade_rank(db, student_id, grade_level, time_range)

    # Calculate percentile
    grade_percentile = ((total_students_in_grade - student_rank_in_grade + 1) /
                       total_students_in_grade * 100) if total_students_in_grade > 0 else 0

    # Get subject averages across grade
    grade_subject_averages = _get_grade_subject_averages(db, grade_level, time_range)

    # Compare student's subject performance to grade averages
    student_vs_grade_subjects = _compare_student_to_grade_subjects(
        db, student_id, grade_subject_averages, time_range
    )

    # Get top performers in grade
    top_performers = _get_grade_top_performers(db, grade_level, time_range)

    # Determine achievement level
    achievement_level = _determine_achievement_level(grade_percentile)

    return GradeAnalyticsDetail(
        grade_level=grade_level,
        total_students_in_grade=total_students_in_grade,
        student_rank_in_grade=student_rank_in_grade,
        grade_percentile=grade_percentile,
        student_gpa=student_gpa,
        grade_average_gpa=grade_average_gpa,
        top_gpa_in_grade=top_gpa_in_grade,
        grade_subject_averages=grade_subject_averages,
        student_vs_grade_subjects=student_vs_grade_subjects,
        top_performers=top_performers,
        achievement_level=achievement_level
    )


def _get_student_grade_level(db: Session, student_id: uuid.UUID) -> str:
    """Get the student's current grade level"""

    # Get student's current classroom enrollments and determine grade level
    student_classroom = db.query(StudentClassroom).filter(
        StudentClassroom.student_id == student_id
    ).first()

    if student_classroom and student_classroom.classroom:
        # Try to extract grade from classroom name or use a default
        classroom_name = student_classroom.classroom.name
        # Simple pattern matching for grade level
        import re
        grade_match = re.search(r'grade\s*(\d+)', classroom_name.lower())
        if grade_match:
            return f"Grade {grade_match.group(1)}"

        # Check if there's a class_number relationship
        if hasattr(student_classroom.classroom, 'class_number'):
            class_num = student_classroom.classroom.class_number
            if class_num:
                return f"Grade {class_num.number}"

    # Default fallback
    return "Grade 10"


def _get_students_in_grade(db: Session, grade_level: str) -> List[uuid.UUID]:
    """Get all students in the same grade level"""

    # Extract grade number from grade_level string
    import re
    grade_match = re.search(r'(\d+)', grade_level)
    if not grade_match:
        return []

    grade_number = int(grade_match.group(1))

    # Find all students in classrooms that match this grade level
    students = db.query(User.id).join(
        StudentClassroom, StudentClassroom.student_id == User.id
    ).join(
        Classroom, StudentClassroom.classroom_id == Classroom.id
    ).filter(
        User.user_type == UserTypeEnum.student
    ).all()

    # Filter by grade level (this is a simplified approach)
    # In a real implementation, you'd have a proper grade level field
    grade_students = []
    for student_id_tuple in students:
        student_id = student_id_tuple[0]
        student_grade = _get_student_grade_level(db, student_id)
        if student_grade == grade_level:
            grade_students.append(student_id)

    return grade_students


def _calculate_student_gpa(
    db: Session,
    student_id: uuid.UUID,
    time_range: AnalyticsTimeRange
) -> float:
    """Calculate student's overall GPA"""

    # Get all exam attempts
    attempts = db.query(StudentExamAttempt).filter(
        StudentExamAttempt.student_id == student_id,
        StudentExamAttempt.started_at >= time_range.start_date,
        StudentExamAttempt.started_at <= time_range.end_date,
        StudentExamAttempt.completed_at.isnot(None)
    ).all()

    total_score = 0
    total_attempts = 0

    for attempt in attempts:
        score = _get_attempt_score(db, attempt.id)
        if score is not None:
            total_score += score
            total_attempts += 1

    return total_score / total_attempts if total_attempts > 0 else 0.0


def _get_attempt_score(db: Session, attempt_id: uuid.UUID) -> Optional[float]:
    """Get the score for an exam attempt"""

    # Check for teacher result first
    teacher_result = db.query(StudentExamTeacherResult).filter(
        StudentExamTeacherResult.attempt_id == attempt_id
    ).first()

    if teacher_result and teacher_result.total_score is not None:
        return (teacher_result.total_score / teacher_result.total_marks * 100
                if teacher_result.total_marks > 0 else 0)

    # Check for AI result
    ai_result = db.query(StudentExamAIResult).filter(
        StudentExamAIResult.attempt_id == attempt_id
    ).first()

    if ai_result and ai_result.total_score is not None:
        return (ai_result.total_score / ai_result.total_marks * 100
                if ai_result.total_marks > 0 else 0)

    return None


def _calculate_grade_average_gpa(
    db: Session,
    grade_level: str,
    time_range: AnalyticsTimeRange
) -> float:
    """Calculate average GPA for the grade level"""

    # Get all students in this grade level
    grade_students = _get_students_in_grade(db, grade_level)

    if not grade_students:
        return 0.0

    # Calculate GPA for each student and average them
    total_gpa = 0.0
    valid_students = 0

    for student_id in grade_students:
        student_gpa = _calculate_student_gpa(db, student_id, time_range)
        if student_gpa > 0:
            total_gpa += student_gpa
            valid_students += 1

    return total_gpa / valid_students if valid_students > 0 else 0.0


def _get_top_gpa_in_grade(
    db: Session,
    grade_level: str,
    time_range: AnalyticsTimeRange
) -> float:
    """Get the highest GPA in the grade level"""

    # Get all students in this grade level
    grade_students = _get_students_in_grade(db, grade_level)

    if not grade_students:
        return 0.0

    # Calculate GPA for each student and find the highest
    max_gpa = 0.0

    for student_id in grade_students:
        student_gpa = _calculate_student_gpa(db, student_id, time_range)
        if student_gpa > max_gpa:
            max_gpa = student_gpa

    return max_gpa


def _calculate_grade_rank(
    db: Session,
    student_id: uuid.UUID,
    grade_level: str,
    time_range: AnalyticsTimeRange
) -> int:
    """Calculate student's rank within the grade level"""

    # Get all students in this grade level
    grade_students = _get_students_in_grade(db, grade_level)

    if not grade_students or student_id not in grade_students:
        return 1  # Default rank if no data

    # Calculate GPA for all students and rank them
    student_gpas = []
    for sid in grade_students:
        gpa = _calculate_student_gpa(db, sid, time_range)
        student_gpas.append((sid, gpa))

    # Sort by GPA in descending order
    student_gpas.sort(key=lambda x: x[1], reverse=True)

    # Find the student's rank
    for rank, (sid, gpa) in enumerate(student_gpas, 1):
        if sid == student_id:
            return rank

    return len(grade_students)  # Last rank if not found


def _get_grade_subject_averages(
    db: Session,
    grade_level: str,
    time_range: AnalyticsTimeRange
) -> Dict[str, float]:
    """Get average scores for each subject at grade level"""

    # Get all students in this grade level
    grade_students = _get_students_in_grade(db, grade_level)

    if not grade_students:
        return {}

    # Get all subjects
    subjects = db.query(Subject).all()
    subject_averages = {}

    for subject in subjects:
        # Get exam attempts for this subject from all students in grade
        from Models.Exam import exam_question_association
        attempts = db.query(StudentExamAttempt).join(
            Exam, StudentExamAttempt.exam_id == Exam.id
        ).join(
            exam_question_association, exam_question_association.c.exam_id == Exam.id
        ).join(
            Question, exam_question_association.c.question_id == Question.id
        ).filter(
            Question.subject_id == subject.id,
            StudentExamAttempt.student_id.in_(grade_students),
            StudentExamAttempt.started_at >= time_range.start_date,
            StudentExamAttempt.started_at <= time_range.end_date,
            StudentExamAttempt.completed_at.isnot(None)
        ).all()

        if attempts:
            # Calculate average score for this subject
            total_score = 0
            total_possible = 0

            for attempt in attempts:
                # Get results (prefer teacher results over AI results)
                teacher_result = db.query(StudentExamTeacherResult).filter(
                    StudentExamTeacherResult.attempt_id == attempt.id
                ).first()

                if teacher_result and teacher_result.total_score is not None:
                    total_score += teacher_result.total_score
                    total_possible += teacher_result.total_marks or 100
                else:
                    ai_result = db.query(StudentExamAIResult).filter(
                        StudentExamAIResult.attempt_id == attempt.id
                    ).first()

                    if ai_result and ai_result.total_score is not None:
                        total_score += ai_result.total_score
                        total_possible += ai_result.total_marks or 100

            if total_possible > 0:
                subject_averages[subject.name] = (total_score / total_possible) * 100

    return subject_averages


def _compare_student_to_grade_subjects(
    db: Session,
    student_id: uuid.UUID,
    grade_subject_averages: Dict[str, float],
    time_range: AnalyticsTimeRange
) -> Dict[str, float]:
    """Compare student's subject performance to grade averages"""

    # Use the passed grade_subject_averages instead of recalculating
    grade_averages = grade_subject_averages

    # Get student's subject performance
    from Cruds.StudentAnalytics.SubjectAnalytics import get_subject_analytics
    from Schemas.StudentAnalytics import SubjectAnalyticsRequest

    subject_request = SubjectAnalyticsRequest(
        time_range=time_range,
        include_trends=False,
        include_comparisons=False,
        include_recommendations=False,
        include_chapter_breakdown=False
    )

    try:
        student_analytics = get_subject_analytics(db, student_id, subject_request)
        student_subjects = {s.subject_name: s.average_score for s in student_analytics.subjects}
    except:
        student_subjects = {}

    # Compare student performance to grade averages
    comparisons = {}
    for subject_name, grade_avg in grade_averages.items():
        if subject_name in student_subjects:
            student_avg = student_subjects[subject_name]
            comparisons[subject_name] = student_avg - grade_avg

    return comparisons


def _get_grade_top_performers(
    db: Session,
    grade_level: str,
    time_range: AnalyticsTimeRange
) -> List[Dict[str, Any]]:
    """Get top performers in the grade level"""

    # Get all students in this grade level
    grade_students = _get_students_in_grade(db, grade_level)

    if not grade_students:
        return []

    # Calculate GPA for each student
    student_gpas = []
    for student_id in grade_students:
        student = db.query(User).filter(User.id == student_id).first()
        if student:
            gpa = _calculate_student_gpa(db, student_id, time_range)
            if gpa > 0:
                student_gpas.append({
                    "name": student.username or f"Student {str(student_id)[:8]}",
                    "gpa": gpa,
                    "student_id": student_id
                })

    # Sort by GPA and assign ranks
    student_gpas.sort(key=lambda x: x["gpa"], reverse=True)

    # Return top 3 performers
    top_performers = []
    for i, student in enumerate(student_gpas[:3]):
        top_performers.append({
            "name": student["name"],
            "gpa": student["gpa"],
            "rank": i + 1
        })

    return top_performers


def _determine_achievement_level(percentile: float) -> str:
    """Determine achievement level based on percentile"""

    if percentile >= 90:
        return "excellent"
    elif percentile >= 75:
        return "good"
    elif percentile >= 50:
        return "average"
    else:
        return "needs_improvement"


def _get_classroom_grade_level(db: Session, classroom_id: uuid.UUID) -> str:
    """Get the grade level for a classroom"""

    # Get classroom details
    classroom = db.query(Classroom).filter(Classroom.id == classroom_id).first()

    if classroom:
        # Try to extract grade from classroom name
        import re
        grade_match = re.search(r'grade\s*(\d+)', classroom.name.lower())
        if grade_match:
            return f"Grade {grade_match.group(1)}"

        # Check if there's a class_number relationship
        if hasattr(classroom, 'class_number') and classroom.class_number:
            return f"Grade {classroom.class_number.number}"

    # Default fallback
    return "Grade 10"


def _determine_academic_standing(
    class_performances: List[ClassPerformanceMetrics],
    grade_analytics: GradeAnalyticsDetail
) -> str:
    """Determine overall academic standing"""

    if grade_analytics.achievement_level == "excellent":
        return "Outstanding Performance"
    elif grade_analytics.achievement_level == "good":
        return "Above Average Performance"
    elif grade_analytics.achievement_level == "average":
        return "Satisfactory Performance"
    else:
        return "Needs Improvement"


def _generate_class_grade_recommendations(
    class_performances: List[ClassPerformanceMetrics],
    grade_analytics: GradeAnalyticsDetail
) -> List[str]:
    """Generate improvement recommendations"""

    recommendations = []

    # Check grade performance
    if grade_analytics.grade_percentile < 50:
        recommendations.append("Focus on improving overall academic performance")

    # Check subject performance
    weak_subjects = [
        subject for subject, diff in grade_analytics.student_vs_grade_subjects.items()
        if diff < -5
    ]

    if weak_subjects:
        recommendations.append(f"Strengthen performance in: {', '.join(weak_subjects)}")

    # Check class rankings
    low_ranking_classes = [
        cp for cp in class_performances
        if cp.percentile < 50
    ]

    if low_ranking_classes:
        recommendations.append("Improve participation and performance in underperforming classes")

    return recommendations
