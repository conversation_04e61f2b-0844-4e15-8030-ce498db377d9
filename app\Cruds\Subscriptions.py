from typing import List, Optional, Dict, Any
import uuid
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, or_, func, desc, text
from fastapi import HTTPException
from datetime import datetime, timezone, timedelta
from decimal import Decimal
import json

# Import Models
from Models.users import (
    User, UserTypeEnum, SubscriptionPlan, UserSubscription, 
    TeacherProfile, Subject, teacher_subjects
)

# Import Schemas
from Schemas.Subscriptions import (
    SubscriptionPlanCreate, SubscriptionPlanUpdate, SubscriptionPlanOut,
    UserSubscriptionCreate, UserSubscriptionUpdate, UserSubscriptionOut,
    TeacherProfileUpdate, TeacherProfileOut, HomeTutoringSearchFilter,
    HomeTutorOut, HomeTutoringSearchResponse, SubscriptionStatsOut,
    UsageMetrics, SubscriptionUsageOut, PlanTypeEnum, SubscriptionStatusEnum
)


# Subscription Plan CRUD
def create_subscription_plan(
    db: Session,
    plan_data: SubscriptionPlanCreate
) -> SubscriptionPlanOut:
    """Create a new subscription plan"""
    
    # Check if plan name already exists for the same user type
    existing_plan = db.query(SubscriptionPlan).filter(
        SubscriptionPlan.name == plan_data.name,
        SubscriptionPlan.target_user_type == plan_data.target_user_type
    ).first()
    
    if existing_plan:
        raise HTTPException(
            status_code=400, 
            detail=f"Plan '{plan_data.name}' already exists for {plan_data.target_user_type.value} users"
        )
    
    plan = SubscriptionPlan(
        **plan_data.dict(),
        features=json.dumps(plan_data.features) if plan_data.features else None
    )
    
    db.add(plan)
    db.commit()
    db.refresh(plan)
    
    return SubscriptionPlanOut.model_validate(plan)


def get_subscription_plans(
    db: Session,
    user_type: Optional[UserTypeEnum] = None,
    is_active: bool = True,
    skip: int = 0,
    limit: int = 100
) -> List[SubscriptionPlanOut]:
    """Get subscription plans"""
    
    query = db.query(SubscriptionPlan).filter(
        SubscriptionPlan.is_active == is_active
    )
    
    if user_type:
        query = query.filter(SubscriptionPlan.target_user_type == user_type)
    
    plans = query.order_by(SubscriptionPlan.price).offset(skip).limit(limit).all()
    
    return [SubscriptionPlanOut.model_validate(plan) for plan in plans]


def update_subscription_plan(
    db: Session,
    plan_id: uuid.UUID,
    plan_update: SubscriptionPlanUpdate
) -> SubscriptionPlanOut:
    """Update a subscription plan"""
    
    plan = db.query(SubscriptionPlan).filter(SubscriptionPlan.id == plan_id).first()
    if not plan:
        raise HTTPException(status_code=404, detail="Subscription plan not found")
    
    update_data = plan_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        if field == "features" and value:
            setattr(plan, field, json.dumps(value))
        else:
            setattr(plan, field, value)
    
    db.commit()
    db.refresh(plan)
    
    return SubscriptionPlanOut.model_validate(plan)


# User Subscription CRUD
def create_user_subscription(
    db: Session,
    subscription_data: UserSubscriptionCreate
) -> UserSubscriptionOut:
    """Create or update user subscription"""
    
    # Check if user exists
    user = db.query(User).filter(User.id == subscription_data.user_id).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    
    # Check if plan exists and is for the correct user type
    if subscription_data.plan_id:
        plan = db.query(SubscriptionPlan).filter(
            SubscriptionPlan.id == subscription_data.plan_id
        ).first()
        if not plan:
            raise HTTPException(status_code=404, detail="Subscription plan not found")
        
        if plan.target_user_type != user.user_type:
            raise HTTPException(
                status_code=400, 
                detail=f"Plan is not available for {user.user_type.value} users"
            )
    
    # Check if user already has a subscription
    existing_subscription = db.query(UserSubscription).filter(
        UserSubscription.user_id == subscription_data.user_id
    ).first()
    
    if existing_subscription:
        # Update existing subscription
        for field, value in subscription_data.dict(exclude_unset=True).items():
            if field != "user_id":
                setattr(existing_subscription, field, value)
        
        # Update dates
        existing_subscription.start_date = datetime.now(timezone.utc)
        if subscription_data.plan_id:
            plan = db.query(SubscriptionPlan).filter(
                SubscriptionPlan.id == subscription_data.plan_id
            ).first()
            existing_subscription.end_date = existing_subscription.start_date + timedelta(days=plan.duration_days)
        
        db.commit()
        db.refresh(existing_subscription)
        return UserSubscriptionOut.model_validate(existing_subscription)
    
    # Create new subscription
    start_date = datetime.now(timezone.utc)
    end_date = None
    
    if subscription_data.plan_id:
        plan = db.query(SubscriptionPlan).filter(
            SubscriptionPlan.id == subscription_data.plan_id
        ).first()
        end_date = start_date + timedelta(days=plan.duration_days)
    
    subscription = UserSubscription(
        **subscription_data.dict(),
        start_date=start_date,
        end_date=end_date,
        current_usage=json.dumps(UsageMetrics().dict())
    )
    
    db.add(subscription)
    db.commit()
    db.refresh(subscription)
    
    return UserSubscriptionOut.model_validate(subscription)


def get_user_subscription(
    db: Session,
    user_id: uuid.UUID
) -> Optional[UserSubscriptionOut]:
    """Get user's current subscription"""
    
    subscription = db.query(UserSubscription).options(
        joinedload(UserSubscription.plan)
    ).filter(UserSubscription.user_id == user_id).first()
    
    if not subscription:
        return None
    
    return UserSubscriptionOut.model_validate(subscription)


def auto_assign_basic_subscription(
    db: Session,
    user_id: uuid.UUID,
    user_type: UserTypeEnum
) -> UserSubscriptionOut:
    """Auto-assign basic subscription to new users using hardcoded plans"""

    from config.subscription_plans import get_default_plan, PlanType

    # Get default plan configuration from hardcoded plans
    default_plan_config = get_default_plan(user_type)
    if not default_plan_config:
        raise HTTPException(
            status_code=500,
            detail=f"No default plan configured for {user_type.value} users"
        )

    # Find or create the plan in database
    plan_name = default_plan_config["name"]
    basic_plan = db.query(SubscriptionPlan).filter(
        SubscriptionPlan.name == plan_name,
        SubscriptionPlan.target_user_type == user_type,
        SubscriptionPlan.is_active == True
    ).first()

    if not basic_plan:
        # Create the plan from hardcoded configuration
        features = default_plan_config.get("features", [])
        limits = default_plan_config.get("limits", {})

        basic_plan = SubscriptionPlan(
            name=default_plan_config["name"],
            description=default_plan_config["description"],
            price=default_plan_config["price"],
            duration_days=default_plan_config["duration_days"],
            features=json.dumps([f.value for f in features]),
            is_active=True,
            plan_type=PlanType.BASIC.value,
            target_user_type=user_type,

            # Set limits from configuration
            max_classrooms=limits.get("max_classrooms"),
            max_students_per_classroom=limits.get("max_students_per_classroom"),
            max_exams_per_month=limits.get("max_exams_per_month"),
            max_questions_per_exam=limits.get("max_questions_per_exam"),

            # Set feature flags
            allows_home_tutoring="home_tutoring" in [f.value for f in features],
            allows_ai_question_generation="ai_question_generation" in [f.value for f in features],
            allows_advanced_analytics="advanced_analytics" in [f.value for f in features],
            priority_support="priority_support" in [f.value for f in features]
        )

        db.add(basic_plan)
        db.commit()
        db.refresh(basic_plan)

    # Create subscription
    subscription_data = UserSubscriptionCreate(
        user_id=user_id,
        plan_id=basic_plan.id,
        status=SubscriptionStatusEnum.ACTIVE
    )

    return create_user_subscription(db, subscription_data)


# Teacher Profile Enhanced CRUD
def update_teacher_profile_with_subjects(
    db: Session,
    teacher_id: uuid.UUID,
    profile_update: TeacherProfileUpdate
) -> TeacherProfileOut:
    """Update teacher profile including subject assignments"""
    
    # Get teacher profile
    teacher_profile = db.query(TeacherProfile).filter(
        TeacherProfile.teacher_id == teacher_id
    ).first()
    
    if not teacher_profile:
        raise HTTPException(status_code=404, detail="Teacher profile not found")
    
    # Update profile fields
    update_data = profile_update.dict(exclude_unset=True, exclude={"subject_ids"})
    
    # Handle JSON fields
    if "available_days" in update_data and update_data["available_days"]:
        update_data["available_days"] = json.dumps(update_data["available_days"])
    
    if "available_hours" in update_data and update_data["available_hours"]:
        update_data["available_hours"] = json.dumps(update_data["available_hours"])
    
    for field, value in update_data.items():
        if hasattr(teacher_profile, field):
            setattr(teacher_profile, field, value)
    
    # Update subject assignments
    if profile_update.subject_ids is not None:
        # Remove existing subject assignments
        db.execute(
            teacher_subjects.delete().where(
                teacher_subjects.c.teacher_profile_id == teacher_profile.id
            )
        )
        
        # Add new subject assignments
        for subject_id in profile_update.subject_ids:
            # Verify subject exists
            subject = db.query(Subject).filter(Subject.id == subject_id).first()
            if subject:
                db.execute(
                    teacher_subjects.insert().values(
                        teacher_profile_id=teacher_profile.id,
                        subject_id=subject_id
                    )
                )
    
    db.commit()
    db.refresh(teacher_profile)
    
    return get_teacher_profile_with_subjects(db, teacher_id)


def get_teacher_profile_with_subjects(
    db: Session,
    teacher_id: uuid.UUID
) -> TeacherProfileOut:
    """Get teacher profile with subjects"""
    
    teacher_profile = db.query(TeacherProfile).options(
        joinedload(TeacherProfile.subjects)
    ).filter(TeacherProfile.teacher_id == teacher_id).first()
    
    if not teacher_profile:
        raise HTTPException(status_code=404, detail="Teacher profile not found")
    
    # Convert to output schema
    profile_dict = {
        "id": teacher_profile.id,
        "teacher_id": teacher_profile.teacher_id,
        "bio": teacher_profile.bio,
        "experience_years": teacher_profile.experience_years,
        "specialization": teacher_profile.specialization,
        "website": teacher_profile.website,
        "office_hours": teacher_profile.office_hours,
        "rating": teacher_profile.rating,
        "offers_home_tutoring": teacher_profile.offers_home_tutoring,
        "home_address": teacher_profile.home_address,
        "latitude": teacher_profile.latitude,
        "longitude": teacher_profile.longitude,
        "tutoring_radius_km": teacher_profile.tutoring_radius_km,
        "hourly_rate_home": teacher_profile.hourly_rate_home,
        "hourly_rate_online": teacher_profile.hourly_rate_online,
        "preferred_contact_method": teacher_profile.preferred_contact_method,
        "whatsapp_number": teacher_profile.whatsapp_number,
        "available_days": json.loads(teacher_profile.available_days) if teacher_profile.available_days else None,
        "available_hours": json.loads(teacher_profile.available_hours) if teacher_profile.available_hours else None,
        "subjects": [
            {"subject_id": subject.id, "subject_name": subject.name}
            for subject in teacher_profile.subjects
        ],
        "created_at": teacher_profile.created_at,
        "updated_at": teacher_profile.updated_at
    }
    
    return TeacherProfileOut(**profile_dict)


# Home Tutoring Search
def search_home_tutors(
    db: Session,
    search_filter: HomeTutoringSearchFilter,
    skip: int = 0,
    limit: int = 20
) -> HomeTutoringSearchResponse:
    """Search for home tutors based on location and criteria"""

    # Base query for teachers offering home tutoring
    query = db.query(TeacherProfile).options(
        joinedload(TeacherProfile.user),
        joinedload(TeacherProfile.subjects)
    ).filter(
        TeacherProfile.offers_home_tutoring == True,
        TeacherProfile.latitude.isnot(None),
        TeacherProfile.longitude.isnot(None)
    )

    # Filter by subjects
    if search_filter.subject_ids:
        query = query.join(TeacherProfile.subjects).filter(
            Subject.id.in_(search_filter.subject_ids)
        )

    # Filter by hourly rate
    if search_filter.max_hourly_rate:
        query = query.filter(
            or_(
                TeacherProfile.hourly_rate_home <= search_filter.max_hourly_rate,
                TeacherProfile.hourly_rate_home.is_(None)
            )
        )

    # Filter by rating
    if search_filter.min_rating:
        query = query.filter(TeacherProfile.rating >= search_filter.min_rating)

    # Get all matching teachers
    teachers = query.all()

    # Calculate distances and filter by radius
    tutors_with_distance = []
    search_lat = search_filter.latitude
    search_lng = search_filter.longitude
    search_radius = search_filter.radius_km or 10  # Default 10km

    for teacher in teachers:
        if search_lat and search_lng:
            # Calculate distance using Haversine formula
            distance = calculate_distance(
                search_lat, search_lng,
                teacher.latitude, teacher.longitude
            )

            if distance <= search_radius:
                tutors_with_distance.append((teacher, distance))
        else:
            tutors_with_distance.append((teacher, None))

    # Sort by distance
    tutors_with_distance.sort(key=lambda x: x[1] if x[1] is not None else float('inf'))

    # Apply pagination
    paginated_tutors = tutors_with_distance[skip:skip + limit]

    # Convert to output format
    tutor_list = []
    for teacher, distance in paginated_tutors:
        # Filter by available days if specified
        if search_filter.available_days:
            teacher_available_days = json.loads(teacher.available_days) if teacher.available_days else []
            if not any(day in teacher_available_days for day in search_filter.available_days):
                continue

        tutor_list.append(HomeTutorOut(
            teacher_id=teacher.teacher_id,
            username=teacher.user.username,
            full_name=teacher.user.username,  # You might want to add a full_name field
            bio=teacher.bio,
            experience_years=teacher.experience_years,
            rating=teacher.rating,
            hourly_rate_home=teacher.hourly_rate_home,
            hourly_rate_online=teacher.hourly_rate_online,
            subjects=[
                {"subject_id": subject.id, "subject_name": subject.name}
                for subject in teacher.subjects
            ],
            distance_km=distance,
            available_days=json.loads(teacher.available_days) if teacher.available_days else None,
            preferred_contact_method=teacher.preferred_contact_method,
            whatsapp_number=teacher.whatsapp_number
        ))

    return HomeTutoringSearchResponse(
        tutors=tutor_list,
        total=len(tutors_with_distance),
        search_radius_km=search_radius,
        center_latitude=search_lat or Decimal('0'),
        center_longitude=search_lng or Decimal('0')
    )


def calculate_distance(lat1: Decimal, lon1: Decimal, lat2: Decimal, lon2: Decimal) -> float:
    """Calculate distance between two points using Haversine formula"""
    from math import radians, cos, sin, asin, sqrt

    # Convert to float for calculation
    lat1, lon1, lat2, lon2 = map(float, [lat1, lon1, lat2, lon2])

    # Convert decimal degrees to radians
    lat1, lon1, lat2, lon2 = map(radians, [lat1, lon1, lat2, lon2])

    # Haversine formula
    dlat = lat2 - lat1
    dlon = lon2 - lon1
    a = sin(dlat/2)**2 + cos(lat1) * cos(lat2) * sin(dlon/2)**2
    c = 2 * asin(sqrt(a))

    # Radius of earth in kilometers
    r = 6371

    return c * r


# Subscription Analytics
def get_subscription_statistics(db: Session) -> SubscriptionStatsOut:
    """Get subscription statistics"""

    # Basic counts
    total_subscriptions = db.query(UserSubscription).count()
    active_subscriptions = db.query(UserSubscription).filter(
        UserSubscription.status == SubscriptionStatusEnum.ACTIVE
    ).count()
    trial_subscriptions = db.query(UserSubscription).filter(
        UserSubscription.status == SubscriptionStatusEnum.TRIAL
    ).count()
    expired_subscriptions = db.query(UserSubscription).filter(
        UserSubscription.status == SubscriptionStatusEnum.EXPIRED
    ).count()

    # Revenue calculations (for paid plans only)
    revenue_query = db.query(
        func.sum(SubscriptionPlan.price)
    ).join(UserSubscription).filter(
        UserSubscription.status == SubscriptionStatusEnum.ACTIVE,
        SubscriptionPlan.price > 0
    )

    revenue_total = revenue_query.scalar() or 0

    # This month's revenue
    from datetime import datetime, timezone
    current_month_start = datetime.now(timezone.utc).replace(day=1, hour=0, minute=0, second=0, microsecond=0)
    revenue_this_month = db.query(
        func.sum(SubscriptionPlan.price)
    ).join(UserSubscription).filter(
        UserSubscription.status == SubscriptionStatusEnum.ACTIVE,
        UserSubscription.start_date >= current_month_start,
        SubscriptionPlan.price > 0
    ).scalar() or 0

    # Subscriptions by plan
    subscriptions_by_plan = {}
    plan_counts = db.query(
        SubscriptionPlan.name,
        func.count(UserSubscription.id)
    ).join(UserSubscription).group_by(SubscriptionPlan.name).all()

    for plan_name, count in plan_counts:
        subscriptions_by_plan[plan_name] = count

    # Subscriptions by user type
    subscriptions_by_user_type = {}
    user_type_counts = db.query(
        SubscriptionPlan.target_user_type,
        func.count(UserSubscription.id)
    ).join(UserSubscription).group_by(SubscriptionPlan.target_user_type).all()

    for user_type, count in user_type_counts:
        subscriptions_by_user_type[user_type.value] = count

    return SubscriptionStatsOut(
        total_subscriptions=total_subscriptions,
        active_subscriptions=active_subscriptions,
        trial_subscriptions=trial_subscriptions,
        expired_subscriptions=expired_subscriptions,
        revenue_this_month=Decimal(revenue_this_month) / 100,  # Convert from cents
        revenue_total=Decimal(revenue_total) / 100,
        subscriptions_by_plan=subscriptions_by_plan,
        subscriptions_by_user_type=subscriptions_by_user_type
    )


# Usage Tracking
def update_usage_metrics(
    db: Session,
    user_id: uuid.UUID,
    metric_name: str,
    increment: int = 1
) -> bool:
    """Update usage metrics for a user"""

    subscription = db.query(UserSubscription).filter(
        UserSubscription.user_id == user_id
    ).first()

    if not subscription:
        return False

    # Get current usage
    current_usage = json.loads(subscription.current_usage) if subscription.current_usage else {}

    # Update metric
    if metric_name in current_usage:
        current_usage[metric_name] += increment
    else:
        current_usage[metric_name] = increment

    # Save updated usage
    subscription.current_usage = json.dumps(current_usage)
    db.commit()

    return True


def get_subscription_usage(
    db: Session,
    user_id: uuid.UUID
) -> Optional[SubscriptionUsageOut]:
    """Get subscription usage for a user"""

    subscription = db.query(UserSubscription).options(
        joinedload(UserSubscription.plan)
    ).filter(UserSubscription.user_id == user_id).first()

    if not subscription or not subscription.plan:
        return None

    # Parse current usage
    current_usage_dict = json.loads(subscription.current_usage) if subscription.current_usage else {}
    current_usage = UsageMetrics(**current_usage_dict)

    # Get plan limits
    plan = subscription.plan
    plan_limits = {
        "max_classrooms": plan.max_classrooms,
        "max_students_per_classroom": plan.max_students_per_classroom,
        "max_exams_per_month": plan.max_exams_per_month,
        "max_questions_per_exam": plan.max_questions_per_exam
    }

    # Calculate usage percentages
    usage_percentage = {}
    is_over_limit = False
    warnings = []

    for metric, limit in plan_limits.items():
        if limit is not None:
            usage_key = metric.replace("max_", "").replace("_per_month", "_created").replace("_per_exam", "_generated")
            current_value = getattr(current_usage, usage_key, 0)

            percentage = (current_value / limit) * 100 if limit > 0 else 0
            usage_percentage[metric] = percentage

            if current_value >= limit:
                is_over_limit = True
                warnings.append(f"Limit exceeded for {metric}: {current_value}/{limit}")
            elif percentage >= 80:
                warnings.append(f"Approaching limit for {metric}: {current_value}/{limit} ({percentage:.1f}%)")

    return SubscriptionUsageOut(
        user_id=user_id,
        plan_name=plan.name,
        current_usage=current_usage,
        plan_limits=plan_limits,
        usage_percentage=usage_percentage,
        is_over_limit=is_over_limit,
        warnings=warnings
    )
