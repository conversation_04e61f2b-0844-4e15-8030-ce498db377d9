from pydantic import BaseModel, Field, validator, EmailStr
from uuid import UUID
from typing import List, Optional, Dict, Any
from datetime import datetime
from decimal import Decimal
from enum import Enum



class MentorProfileUpdate(BaseModel):
    # Basic profile information
    bio: Optional[str] = Field(None, max_length=2000)
    experience_years: Optional[int] = Field(None, ge=0, le=50)
    hourly_rate: Optional[Decimal] = Field(None, ge=0)
    profile_image_url: Optional[str] = None

    # Extended profile information
    full_name: Optional[str] = Field(None, max_length=255)
    phone: Optional[str] = Field(None, max_length=20)
    linkedin_url: Optional[str] = Field(None, max_length=500)
    website: Optional[str] = Field(None, max_length=500)
    current_position: Optional[str] = Field(None, max_length=255)
    current_organization: Optional[str] = Field(None, max_length=255)
    education: Optional[str] = Field(None, max_length=2000)
    certifications: Optional[str] = Field(None, max_length=2000)
    portfolio_url: Optional[str] = Field(None, max_length=500)
    resume_url: Optional[str] = Field(None, max_length=500)

    # Subject relationships (UUIDs)
    expertise_subject_ids: Optional[List[UUID]] = None
    preferred_subject_ids: Optional[List[UUID]] = None

    # Languages and availability
    languages: Optional[List[str]] = None
    availability_hours: Optional[Dict[str, Any]] = None

    # Validation
    @validator('phone')
    def validate_phone(cls, v):
        if v and not v.replace('+', '').replace('-', '').replace(' ', '').isdigit():
            raise ValueError('Phone number must contain only digits, +, -, and spaces')
        return v

    @validator('linkedin_url', 'website', 'portfolio_url', 'resume_url')
    def validate_urls(cls, v):
        if v and not (v.startswith('http://') or v.startswith('https://')):
            raise ValueError('URL must start with http:// or https://')
        return v




# Output Schemas
class MentorProfileOut(BaseModel):
    id: UUID
    user_id: UUID

    # Basic profile information
    bio: Optional[str]
    experience_years: Optional[int]
    hourly_rate: Optional[Decimal]
    profile_image_url: Optional[str]
    profile_image: Optional[Dict[str, Any]] = None  # Base64 image data with metadata

    # Extended profile information
    full_name: Optional[str] = None
    phone: Optional[str] = None
    linkedin_url: Optional[str] = None
    website: Optional[str] = None
    current_position: Optional[str] = None
    current_organization: Optional[str] = None
    education: Optional[str] = None
    certifications: Optional[str] = None
    portfolio_url: Optional[str] = None
    resume_url: Optional[str] = None

    # JSON fields
    languages: Optional[List[str]] = None
    availability_hours: Optional[Dict[str, Any]] = None

    # Verification and rating
    is_verified: Optional[bool] = False
    verification_status: Optional[str] = "pending"
    rating: Optional[Decimal] = None
    total_reviews: Optional[int] = 0

    # Subject relationships (will be populated from relationships)
    expertise_subjects: Optional[List[Dict[str, Any]]] = None
    preferred_subjects: Optional[List[Dict[str, Any]]] = None

    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class MentorUserOut(BaseModel):
    id: UUID
    username: str
    email: str
    mobile: str
    country: Optional[str]
    profile_picture: Optional[str]
    profile_image: Optional[Dict[str, Any]] = None  # Base64 image data with metadata
    user_type: str
    is_email_verified: bool
    is_mobile_verified: bool
    created_at: datetime
    mentor_profile: Optional[MentorProfileOut]

    class Config:
        from_attributes = True


class MentorDetailedOut(BaseModel):
    user: MentorUserOut
    profile: MentorProfileOut
    total_competitions: int = 0
    active_institutes: int = 0
    average_rating: Optional[Decimal] = None
    verification_status: str

    class Config:
        from_attributes = True


class MentorListOut(BaseModel):
    id: UUID
    username: str
    full_name: str
    email: str
    mobile: str
    country: Optional[str]
    bio: Optional[str]
    expertise_areas: Optional[List[str]]
    experience_years: Optional[int]
    current_position: Optional[str]
    hourly_rate: Optional[Decimal]
    languages: Optional[List[str]]
    rating: Optional[Decimal]
    profile_image_url: Optional[str]
    profile_image: Optional[Dict[str, Any]] = None  # Base64 image data with metadata
    created_at: datetime

    class Config:
        from_attributes = True


class MentorListResponse(BaseModel):
    mentors: List[MentorListOut]
    total: int
    page: int
    size: int
    has_next: bool
    has_prev: bool
