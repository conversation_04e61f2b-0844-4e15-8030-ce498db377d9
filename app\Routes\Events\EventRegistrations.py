"""
Event Registrations Routes for EduFair Platform

This module contains all API routes for Event Registration management including:
- Registration CRUD operations
- Registration status management
- Registration analytics
"""

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session
from typing import List
from uuid import UUID

# Import dependencies
from config.session import get_db
from config.deps import oauth2_scheme, get_current_user
from config.permission import require_type

# Import CRUD operations
from Cruds.Events.EventRegistrations import (
    create_event_registration, get_registration_by_id, get_user_registrations,
    get_event_registrations, update_registration_status, cancel_registration,
    get_registration_statistics
)

# Import models for payment
from Models.Events import EventPayment, PaymentStatusEnum

# Import schemas
from Schemas.Events.Events import (
    EventRegistrationCreate, EventRegistrationOut
)
from Models.Events import RegistrationStatusEnum

# Import additional schemas for enhanced response
from pydantic import BaseModel
from typing import Optional
from decimal import Decimal
from datetime import datetime

router = APIRouter()


# ==================== ENHANCED SCHEMAS ====================

class MyRegistrationEventInfo(BaseModel):
    """Essential event information for my registrations"""
    id: str
    title: str
    description: str
    start_datetime: datetime
    end_datetime: Optional[datetime]
    location: Optional[str]
    status: str
    banner_image_url: Optional[str]


class MyRegistrationTicketInfo(BaseModel):
    """Essential ticket information for my registrations"""
    id: str
    name: str
    price: Decimal
    description: Optional[str]


class MyRegistrationOut(BaseModel):
    """User-friendly registration response with essential event details"""
    # Registration details
    registration_id: str
    registration_number: str
    status: str
    quantity: int
    total_amount: Decimal
    currency: str
    registered_at: datetime
    confirmed_at: Optional[datetime]

    # Event details (what users actually need)
    event: MyRegistrationEventInfo
    ticket: Optional[MyRegistrationTicketInfo]

    # Payment info
    payment_status: str
    payment_reference: Optional[str]

    # Additional info
    attendee_info: Optional[dict]
    special_requirements: Optional[str]


# ==================== EVENT REGISTRATION ROUTES ====================

@router.post("/", response_model=EventRegistrationOut)
def create_event_registration_endpoint(
    registration: EventRegistrationCreate,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("student"))
):
    """
    Create a new event registration.
    
    Only students can register for events.
    """
    current_user = get_current_user(token, db)
    return create_event_registration(db, registration, current_user.id)


@router.get("/my-registrations", response_model=List[MyRegistrationOut])
def get_my_registrations_endpoint(
    skip: int = Query(0, ge=0, description="Number of registrations to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Number of registrations to return"),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("student"))
):
    """
    Get all registrations for the current user with essential event details.

    Returns user-friendly data including event name, date, location, etc.
    Only students can access their own registrations.
    """
    current_user = get_current_user(token, db)

    try:
        # Get registrations with event and ticket details
        from Models.Events import EventRegistration, Event, EventTicket
        from sqlalchemy.orm import joinedload

        registrations = db.query(EventRegistration).options(
            joinedload(EventRegistration.event),
            joinedload(EventRegistration.ticket)
        ).filter(
            EventRegistration.user_id == current_user.id
        ).order_by(EventRegistration.created_at.desc()).offset(skip).limit(limit).all()

        # Transform to user-friendly format
        result = []
        for reg in registrations:
            event_info = None
            if reg.event:
                event_info = MyRegistrationEventInfo(
                    id=str(reg.event.id),
                    title=reg.event.title,
                    description=reg.event.description or "",
                    start_datetime=reg.event.start_datetime,
                    end_datetime=reg.event.end_datetime,
                    location=reg.event.location or "TBA",
                    status=reg.event.status.value if reg.event.status else "unknown",
                    banner_image_url=reg.event.banner_image_url
                )

            ticket_info = None
            if reg.ticket:
                ticket_info = MyRegistrationTicketInfo(
                    id=str(reg.ticket.id),
                    name=reg.ticket.name,
                    price=reg.ticket.price,
                    description=reg.ticket.description
                )

            result.append(MyRegistrationOut(
                registration_id=str(reg.id),
                registration_number=reg.registration_number,
                status=reg.status.value,
                quantity=reg.quantity,
                total_amount=reg.total_amount,
                currency=reg.currency,
                registered_at=reg.registered_at or reg.created_at,
                confirmed_at=reg.confirmed_at,
                event=event_info,
                ticket=ticket_info,
                payment_status=reg.payment_status.value if reg.payment_status else "unknown",
                payment_reference=reg.payment_reference,
                attendee_info=reg.attendee_info,
                special_requirements=reg.special_requirements
            ))

        return result

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get registrations: {str(e)}"
        )


@router.get("/events/{event_id}/registrations", response_model=List[EventRegistrationOut])
def get_event_registrations_endpoint(
    event_id: UUID,
    skip: int = Query(0, ge=0, description="Number of registrations to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Number of registrations to return"),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """
    Get all registrations for a specific event.
    
    Only event organizers (institutes) can view event registrations.
    """
    current_user = get_current_user(token, db)
    return get_event_registrations(db, event_id, current_user.id, skip, limit)


@router.get("/{registration_id}", response_model=EventRegistrationOut)
def get_registration_by_id_endpoint(
    registration_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """
    Get registration by ID.
    
    Users can view their own registrations, organizers can view registrations for their events.
    """
    current_user = get_current_user(token, db)
    return get_registration_by_id(db, registration_id, current_user.id)


@router.patch("/{registration_id}/status")
def update_registration_status_endpoint(
    registration_id: UUID,
    status: RegistrationStatusEnum,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """
    Update registration status.
    
    Only event organizers (institutes) can update registration status.
    """
    current_user = get_current_user(token, db)
    registration = update_registration_status(db, registration_id, status, current_user.id)
    return {
        "registration_id": registration_id,
        "status": status,
        "message": f"Registration status updated to {status.value}"
    }


@router.delete("/{registration_id}", status_code=status.HTTP_204_NO_CONTENT)
def cancel_registration_endpoint(
    registration_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """
    Cancel a registration.

    Users can cancel their own registrations, organizers can cancel any registration for their events.
    """
    current_user = get_current_user(token, db)
    cancel_registration(db, registration_id, current_user.id)
    return None


# PayFast payment route removed - demo mode auto-confirms all registrations


@router.get("/events/{event_id}/statistics")
def get_registration_statistics_endpoint(
    event_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """
    Get registration statistics for an event.
    
    Only event organizers (institutes) can view registration statistics.
    """
    current_user = get_current_user(token, db)
    return get_registration_statistics(db, event_id, current_user.id)


# ==================== BULK OPERATIONS ====================

@router.patch("/events/{event_id}/registrations/bulk-approve")
def bulk_approve_registrations_endpoint(
    event_id: UUID,
    registration_ids: List[UUID],
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """
    Bulk approve multiple registrations for an event.
    
    Only event organizers (institutes) can bulk approve registrations.
    """
    current_user = get_current_user(token, db)
    
    approved_count = 0
    failed_count = 0
    errors = []
    
    for registration_id in registration_ids:
        try:
            update_registration_status(db, registration_id, RegistrationStatusEnum.CONFIRMED, current_user.id)
            approved_count += 1
        except Exception as e:
            failed_count += 1
            errors.append({
                "registration_id": registration_id,
                "error": str(e)
            })
    
    return {
        "event_id": event_id,
        "total_processed": len(registration_ids),
        "approved_count": approved_count,
        "failed_count": failed_count,
        "errors": errors
    }


@router.patch("/events/{event_id}/registrations/mark-attended")
def mark_attendees_endpoint(
    event_id: UUID,
    registration_ids: List[UUID],
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """
    Mark multiple registrations as attended.
    
    Only event organizers (institutes) can mark attendance.
    """
    current_user = get_current_user(token, db)
    
    attended_count = 0
    failed_count = 0
    errors = []
    
    for registration_id in registration_ids:
        try:
            update_registration_status(db, registration_id, RegistrationStatusEnum.ATTENDED, current_user.id)
            attended_count += 1
        except Exception as e:
            failed_count += 1
            errors.append({
                "registration_id": registration_id,
                "error": str(e)
            })
    
    return {
        "event_id": event_id,
        "total_processed": len(registration_ids),
        "attended_count": attended_count,
        "failed_count": failed_count,
        "errors": errors
    }
