"""
Schemas for Competition Exam Attempts

This module contains Pydantic schemas for handling competition exam attempts,
allowing students to attempt exams linked to competition events.
"""

from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from uuid import UUID
from datetime import datetime
from decimal import Decimal

from Schemas.Exams.Questions import QuestionStudentOut
from Schemas.Exams.Exam import ExamStudentOut


class CompetitionExamInfo(BaseModel):
    """Basic competition exam information"""
    event_id: UUID = Field(..., description="Competition event ID")
    event_title: str = Field(..., description="Competition title")
    exam_id: UUID = Field(..., description="Linked exam ID")
    exam_title: str = Field(..., description="Exam title")
    exam_description: Optional[str] = Field(None, description="Exam description")
    total_marks: int = Field(..., description="Total marks for the exam")
    total_duration: int = Field(..., description="Exam duration in minutes")
    start_time: Optional[datetime] = Field(None, description="Exam start time")
    end_time: Optional[datetime] = Field(None, description="Exam end time")
    competition_rules: Optional[str] = Field(None, description="Competition rules")
    prize_details: Optional[Dict[str, Any]] = Field(None, description="Prize information")
    
    class Config:
        from_attributes = True


class CompetitionExamAttemptRequest(BaseModel):
    """Request to start a competition exam attempt"""
    event_id: UUID = Field(..., description="Competition event ID")


class CompetitionExamAttemptResponse(BaseModel):
    """Response when starting a competition exam attempt"""
    success: bool = Field(..., description="Whether the attempt was created successfully")
    message: str = Field(..., description="Response message")
    attempt_id: Optional[UUID] = Field(None, description="Created exam attempt ID")
    exam_info: Optional[CompetitionExamInfo] = Field(None, description="Exam information")
    session_id: Optional[str] = Field(None, description="Exam session ID for tracking")
    
    class Config:
        from_attributes = True


class CompetitionExamForStudent(BaseModel):
    """Competition exam details for student view"""
    event_id: UUID = Field(..., description="Competition event ID")
    event_title: str = Field(..., description="Competition title")
    event_description: Optional[str] = Field(None, description="Competition description")
    start_datetime: datetime = Field(..., description="Competition start time")
    end_datetime: datetime = Field(..., description="Competition end time")
    
    # Exam details
    exam_id: UUID = Field(..., description="Linked exam ID")
    exam_title: str = Field(..., description="Exam title")
    exam_description: Optional[str] = Field(None, description="Exam description")
    total_marks: int = Field(..., description="Total marks")
    total_duration: int = Field(..., description="Duration in minutes")
    total_questions: int = Field(..., description="Number of questions")
    
    # Competition specific
    competition_rules: Optional[str] = Field(None, description="Competition rules")
    prize_details: Optional[Dict[str, Any]] = Field(None, description="Prize information")
    max_attendees: Optional[int] = Field(None, description="Maximum participants")
    current_participants: int = Field(0, description="Current number of participants")
    
    # Student status
    is_registered: bool = Field(False, description="Whether student is registered")
    can_attempt: bool = Field(False, description="Whether student can attempt now")
    attempt_id: Optional[UUID] = Field(None, description="Existing attempt ID if any")
    attempt_status: Optional[str] = Field(None, description="Current attempt status")
    
    # Timing information
    registration_open: bool = Field(False, description="Whether registration is open")
    exam_active: bool = Field(False, description="Whether exam is currently active")
    time_remaining: Optional[int] = Field(None, description="Minutes remaining if exam is active")
    
    class Config:
        from_attributes = True


class CompetitionExamQuestions(BaseModel):
    """Competition exam with questions for student attempt"""
    exam_info: CompetitionExamInfo = Field(..., description="Basic exam information")
    questions: List[QuestionStudentOut] = Field(..., description="Exam questions")
    attempt_id: UUID = Field(..., description="Student's attempt ID")
    session_id: str = Field(..., description="Exam session ID")
    time_remaining: Optional[int] = Field(None, description="Minutes remaining")
    
    class Config:
        from_attributes = True


class CompetitionExamStatus(BaseModel):
    """Competition exam attempt status"""
    event_id: UUID = Field(..., description="Competition event ID")
    exam_id: UUID = Field(..., description="Exam ID")
    attempt_id: Optional[UUID] = Field(None, description="Attempt ID")
    status: str = Field(..., description="Attempt status")
    is_registered: bool = Field(..., description="Whether student is registered")
    can_start: bool = Field(..., description="Whether student can start the exam")
    can_continue: bool = Field(..., description="Whether student can continue existing attempt")
    started_at: Optional[datetime] = Field(None, description="When attempt was started")
    completed_at: Optional[datetime] = Field(None, description="When attempt was completed")
    time_remaining: Optional[int] = Field(None, description="Minutes remaining")
    score: Optional[Decimal] = Field(None, description="Current score if available")
    
    class Config:
        from_attributes = True


class CompetitionLeaderboard(BaseModel):
    """Competition leaderboard entry"""
    rank: int = Field(..., description="Student's rank")
    student_id: UUID = Field(..., description="Student ID")
    student_name: str = Field(..., description="Student name")
    score: Decimal = Field(..., description="Student's score")
    total_marks: int = Field(..., description="Total possible marks")
    percentage: Decimal = Field(..., description="Score percentage")
    time_taken: Optional[int] = Field(None, description="Time taken in minutes")
    completed_at: Optional[datetime] = Field(None, description="Completion time")
    
    class Config:
        from_attributes = True


class CompetitionResults(BaseModel):
    """Competition results and statistics"""
    event_id: UUID = Field(..., description="Competition event ID")
    event_title: str = Field(..., description="Competition title")
    total_participants: int = Field(..., description="Total number of participants")
    completed_attempts: int = Field(..., description="Number of completed attempts")
    average_score: Optional[Decimal] = Field(None, description="Average score")
    highest_score: Optional[Decimal] = Field(None, description="Highest score")
    leaderboard: List[CompetitionLeaderboard] = Field(..., description="Top performers")
    student_result: Optional[CompetitionLeaderboard] = Field(None, description="Current student's result")
    
    class Config:
        from_attributes = True
