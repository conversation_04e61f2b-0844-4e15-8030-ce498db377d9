"""
Migration to create missing Event-related tables.

This migration creates all Event-related tables that might be missing:
- event_speakers
- event_tickets
- event_registrations
- event_payments
- event_feedback
- event_calendar
- event_analytics
"""

from sqlalchemy import text
from config.session import engine


def create_missing_event_tables():
    """
    Execute the migration to create missing Event-related tables.
    """
    
    with engine.connect() as connection:
        # Start transaction
        trans = connection.begin()
        
        try:
            print("Starting migration to create missing Event tables...")
            
            # Create event_speakers table
            print("Creating event_speakers table...")
            connection.execute(text("""
                CREATE TABLE IF NOT EXISTS event_speakers (
                    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                    name VARCHAR(200) NOT NULL,
                    title VARCHAR(200),
                    bio TEXT,
                    profile_image_url VARCHAR(500),
                    company VARCHAR(200),
                    website VARCHAR(500),
                    linkedin_url VARCHAR(500),
                    twitter_url VARCHAR(500),
                    email VARCHAR(255),
                    phone VARCHAR(20),
                    expertise_areas JSON,
                    certifications JSON,
                    speaking_topics JSO<PERSON>,
                    languages JSON,
                    is_featured BOOLEAN DEFAULT FALSE,
                    is_keynote_speaker <PERSON><PERSON><PERSON><PERSON><PERSON> DEFAULT FALSE,
                    speaker_fee NUMERIC(10, 2),
                    travel_required BOOLEAN DEFAULT FALSE,
                    rating NUMERIC(3, 2),
                    total_events INTEGER DEFAULT 0,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
                );
            """))
            
            # Create event_speaker_association table
            print("Creating event_speaker_association table...")
            connection.execute(text("""
                CREATE TABLE IF NOT EXISTS event_speaker_association (
                    event_id UUID REFERENCES events(id) ON DELETE CASCADE,
                    speaker_id UUID REFERENCES event_speakers(id) ON DELETE CASCADE,
                    PRIMARY KEY (event_id, speaker_id)
                );
            """))
            
            # Create event_tickets table
            print("Creating event_tickets table...")
            connection.execute(text("""
                CREATE TABLE IF NOT EXISTS event_tickets (
                    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                    event_id UUID NOT NULL REFERENCES events(id) ON DELETE CASCADE,
                    name VARCHAR(200) NOT NULL,
                    description TEXT,
                    price NUMERIC(10, 2) NOT NULL DEFAULT 0,
                    currency VARCHAR(3) DEFAULT 'USD',
                    total_quantity INTEGER,
                    sold_quantity INTEGER DEFAULT 0,
                    status VARCHAR(20) DEFAULT 'active',
                    sale_start TIMESTAMP WITH TIME ZONE,
                    sale_end TIMESTAMP WITH TIME ZONE,
                    min_quantity_per_order INTEGER DEFAULT 1,
                    max_quantity_per_order INTEGER,
                    requires_approval BOOLEAN DEFAULT FALSE,
                    is_transferable BOOLEAN DEFAULT TRUE,
                    is_refundable BOOLEAN DEFAULT FALSE,
                    refund_policy TEXT,
                    terms_and_conditions TEXT,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
                );
            """))
            
            # Create event_registrations table
            print("Creating event_registrations table...")
            connection.execute(text("""
                CREATE TABLE IF NOT EXISTS event_registrations (
                    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                    event_id UUID NOT NULL REFERENCES events(id) ON DELETE CASCADE,
                    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
                    ticket_id UUID NOT NULL REFERENCES event_tickets(id) ON DELETE CASCADE,
                    quantity INTEGER NOT NULL DEFAULT 1,
                    total_amount NUMERIC(10, 2) NOT NULL DEFAULT 0,
                    status VARCHAR(20) DEFAULT 'pending',
                    registration_data JSON,
                    special_requirements TEXT,
                    dietary_preferences TEXT,
                    emergency_contact JSON,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(event_id, user_id)
                );
            """))
            
            # Create event_payments table
            print("Creating event_payments table...")
            connection.execute(text("""
                CREATE TABLE IF NOT EXISTS event_payments (
                    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                    event_id UUID NOT NULL REFERENCES events(id) ON DELETE CASCADE,
                    registration_id UUID NOT NULL REFERENCES event_registrations(id) ON DELETE CASCADE,
                    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
                    amount NUMERIC(10, 2) NOT NULL,
                    currency VARCHAR(3) DEFAULT 'USD',
                    status VARCHAR(20) DEFAULT 'pending',
                    payment_method VARCHAR(50),
                    payment_gateway VARCHAR(50),
                    transaction_id VARCHAR(200),
                    gateway_response JSON,
                    refund_amount NUMERIC(10, 2) DEFAULT 0,
                    refund_reason TEXT,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
                );
            """))
            
            # Create event_feedback table
            print("Creating event_feedback table...")
            connection.execute(text("""
                CREATE TABLE IF NOT EXISTS event_feedback (
                    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                    event_id UUID NOT NULL REFERENCES events(id) ON DELETE CASCADE,
                    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
                    registration_id UUID REFERENCES event_registrations(id) ON DELETE CASCADE,
                    rating INTEGER CHECK (rating >= 1 AND rating <= 5),
                    review TEXT,
                    feedback_data JSON,
                    is_anonymous BOOLEAN DEFAULT FALSE,
                    is_approved BOOLEAN DEFAULT TRUE,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(event_id, user_id)
                );
            """))
            
            # Create event_calendar table
            print("Creating event_calendar table...")
            connection.execute(text("""
                CREATE TABLE IF NOT EXISTS event_calendar (
                    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                    event_id UUID NOT NULL REFERENCES events(id) ON DELETE CASCADE,
                    calendar_date TIMESTAMP WITH TIME ZONE NOT NULL,
                    is_start_date BOOLEAN DEFAULT TRUE,
                    is_end_date BOOLEAN DEFAULT FALSE,
                    is_registration_deadline BOOLEAN DEFAULT FALSE,
                    display_color VARCHAR(7),
                    display_priority INTEGER DEFAULT 0,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
                );
            """))
            
            # Create event_analytics table
            print("Creating event_analytics table...")
            connection.execute(text("""
                CREATE TABLE IF NOT EXISTS event_analytics (
                    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                    event_id UUID NOT NULL REFERENCES events(id) ON DELETE CASCADE,
                    date DATE NOT NULL,
                    page_views INTEGER DEFAULT 0,
                    unique_visitors INTEGER DEFAULT 0,
                    registrations INTEGER DEFAULT 0,
                    cancellations INTEGER DEFAULT 0,
                    revenue NUMERIC(10, 2) DEFAULT 0,
                    conversion_rate NUMERIC(5, 4) DEFAULT 0,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(event_id, date)
                );
            """))
            
            # Add missing columns to events table if needed
            print("Checking events table for missing columns...")
            
            # Get existing columns in events table
            existing_columns = connection.execute(text("""
                SELECT column_name 
                FROM information_schema.columns 
                WHERE table_name = 'events'
            """)).fetchall()
            
            existing_column_names = [row[0] for row in existing_columns]
            
            # Define additional columns that might be missing
            additional_columns = {
                'total_registrations': 'INTEGER DEFAULT 0',
                'total_attendees': 'INTEGER DEFAULT 0',
                'average_rating': 'NUMERIC(3, 2) DEFAULT 0',
                'total_reviews': 'INTEGER DEFAULT 0'
            }
            
            for column_name, column_def in additional_columns.items():
                if column_name not in existing_column_names:
                    print(f"Adding missing column to events table: {column_name}")
                    connection.execute(text(f"""
                        ALTER TABLE events 
                        ADD COLUMN IF NOT EXISTS {column_name} {column_def};
                    """))
                else:
                    print(f"Column {column_name} already exists in events table")
            
            # Commit transaction
            trans.commit()
            print("Migration completed successfully!")
            
        except Exception as e:
            # Rollback on error
            trans.rollback()
            print(f"Migration failed: {e}")
            raise


if __name__ == "__main__":
    create_missing_event_tables()
