"""
Migration: Add exam_id field to events table for competitions
Date: 2024-01-10
Description: Adds optional exam_id foreign key to events table to support competitions with exams
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from sqlalchemy import text
from sqlalchemy.orm import Session
from config.session import get_db

def run_migration():
    """Add exam_id column to events table"""
    db = next(get_db())
    
    try:
        print("🔄 Starting migration: Add exam_id to events table...")
        
        # Check if column already exists
        result = db.execute(text("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'events' 
            AND column_name = 'exam_id'
        """))
        
        if result.fetchone():
            print("✅ Column exam_id already exists in events table")
            return
        
        # Add exam_id column
        print("📝 Adding exam_id column to events table...")
        db.execute(text("""
            ALTER TABLE events 
            ADD COLUMN exam_id UUID REFERENCES exams(id)
        """))
        
        # Create index for better performance
        print("📝 Creating index on exam_id column...")
        db.execute(text("""
            CREATE INDEX IF NOT EXISTS idx_events_exam_id 
            ON events(exam_id)
        """))
        
        db.commit()
        print("✅ Migration completed successfully!")
        print("   - Added exam_id column to events table")
        print("   - Added foreign key constraint to exams table")
        print("   - Created index for performance")
        
    except Exception as e:
        db.rollback()
        print(f"❌ Migration failed: {str(e)}")
        raise
    finally:
        db.close()

def rollback_migration():
    """Remove exam_id column from events table"""
    db = next(get_db())
    
    try:
        print("🔄 Starting rollback: Remove exam_id from events table...")
        
        # Drop index first
        print("📝 Dropping index on exam_id column...")
        db.execute(text("""
            DROP INDEX IF EXISTS idx_events_exam_id
        """))
        
        # Remove column
        print("📝 Removing exam_id column from events table...")
        db.execute(text("""
            ALTER TABLE events 
            DROP COLUMN IF EXISTS exam_id
        """))
        
        db.commit()
        print("✅ Rollback completed successfully!")
        
    except Exception as e:
        db.rollback()
        print(f"❌ Rollback failed: {str(e)}")
        raise
    finally:
        db.close()

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "rollback":
        rollback_migration()
    else:
        run_migration()
