"""
CRUD operations for Mentor-Institute Collaborations
Handles both mentor-initiated and institute-initiated invitations and collaborations
"""

from typing import List, Optional
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, or_, func, desc
from fastapi import HTTPException
from datetime import datetime, timezone
import uuid

# Import Models
from Models.users import (
    User, UserTypeEnum, MentorProfile, InstituteProfile,
    MentorInstituteInvite, MentorInstituteAssociation, InviteReceivedByEnum
)

# Import Schemas
from Schemas.Mentors.MentorInstitutes import (
    MentorInstituteInvite as MentorInstituteInviteSchema,
    MentorInstituteInviteOut, InvitationListResponse,
    CollaborationCreate, CollaborationUpdate, CollaborationDetails,
    CollaborationListResponse, Mentor, Institute
)


# === COLLABORATION CRUD OPERATIONS ===

def create_collaboration(
    db: Session,
    collaboration_data: CollaborationCreate
) -> CollaborationDetails:
    """Create a new collaboration between mentor and institute"""
    
    # Verify mentor and institute exist
    mentor = db.query(User).filter(
        User.id == collaboration_data.mentor_id,
        User.user_type == UserTypeEnum.mentor
    ).first()
    
    if not mentor:
        raise HTTPException(status_code=404, detail="Mentor not found")
    
    institute = db.query(User).filter(
        User.id == collaboration_data.institute_id,
        User.user_type == UserTypeEnum.institute
    ).first()
    
    if not institute:
        raise HTTPException(status_code=404, detail="Institute not found")
    
    # Check if collaboration already exists
    existing = db.query(MentorInstituteAssociation).filter(
        MentorInstituteAssociation.mentor_id == collaboration_data.mentor_id,
        MentorInstituteAssociation.institute_id == collaboration_data.institute_id
    ).first()
    
    if existing:
        raise HTTPException(status_code=400, detail="Collaboration already exists")
    
    # Create collaboration
    collaboration = MentorInstituteAssociation(
        mentor_id=collaboration_data.mentor_id,
        institute_id=collaboration_data.institute_id,
        status="active",
        hourly_rate=collaboration_data.hourly_rate,
        hours_per_week=collaboration_data.hours_per_week,
        contract_terms=collaboration_data.contract_terms,
        start_date=collaboration_data.start_date or datetime.now(timezone.utc)
    )
    
    db.add(collaboration)
    db.commit()
    db.refresh(collaboration)
    
    return _format_collaboration_details(db, collaboration)


def get_collaboration_by_id(
    db: Session,
    collaboration_id: uuid.UUID
) -> Optional[CollaborationDetails]:
    """Get collaboration by ID"""
    
    collaboration = db.query(MentorInstituteAssociation).filter(
        MentorInstituteAssociation.id == collaboration_id
    ).first()
    
    if not collaboration:
        return None
    
    return _format_collaboration_details(db, collaboration)


def update_collaboration(
    db: Session,
    collaboration_id: uuid.UUID,
    update_data: CollaborationUpdate
) -> CollaborationDetails:
    """Update collaboration details"""
    
    collaboration = db.query(MentorInstituteAssociation).filter(
        MentorInstituteAssociation.id == collaboration_id
    ).first()
    
    if not collaboration:
        raise HTTPException(status_code=404, detail="Collaboration not found")
    
    # Update fields
    for field, value in update_data.dict(exclude_unset=True).items():
        setattr(collaboration, field, value)
    
    collaboration.updated_at = datetime.now(timezone.utc)
    db.commit()
    db.refresh(collaboration)
    
    return _format_collaboration_details(db, collaboration)


def delete_collaboration(
    db: Session,
    collaboration_id: uuid.UUID
) -> bool:
    """Delete collaboration"""
    
    collaboration = db.query(MentorInstituteAssociation).filter(
        MentorInstituteAssociation.id == collaboration_id
    ).first()
    
    if not collaboration:
        raise HTTPException(status_code=404, detail="Collaboration not found")
    
    db.delete(collaboration)
    db.commit()
    return True


def list_collaborations(
    db: Session,
    user_id: Optional[uuid.UUID] = None,
    user_type: Optional[str] = None,
    status_filter: Optional[str] = None,
    page: int = 1,
    size: int = 20
) -> CollaborationListResponse:
    """List collaborations with filtering"""
    
    skip = (page - 1) * size
    
    query = db.query(MentorInstituteAssociation)
    
    # Filter by user
    if user_id and user_type:
        if user_type == "mentor":
            query = query.filter(MentorInstituteAssociation.mentor_id == user_id)
        elif user_type == "institute":
            query = query.filter(MentorInstituteAssociation.institute_id == user_id)
    
    # Filter by status
    if status_filter:
        query = query.filter(MentorInstituteAssociation.status == status_filter)
    
    # Get total count
    total = query.count()
    
    # Get paginated results
    collaborations = query.order_by(desc(MentorInstituteAssociation.created_at)).offset(skip).limit(size).all()
    
    # Format results
    collaboration_details = [_format_collaboration_details(db, collab) for collab in collaborations]
    
    return CollaborationListResponse(
        collaborations=collaboration_details,
        total=total,
        page=page,
        size=size,
        has_next=(skip + size) < total,
        has_prev=page > 1
    )


def _format_collaboration_details(db: Session, collaboration: MentorInstituteAssociation) -> CollaborationDetails:
    """Helper function to format collaboration details"""
    
    # Get mentor details
    mentor_user = db.query(User).options(joinedload(User.mentor_profile)).filter(
        User.id == collaboration.mentor_id
    ).first()
    
    # Get institute details  
    institute_user = db.query(User).options(joinedload(User.institute_profile)).filter(
        User.id == collaboration.institute_id
    ).first()
    
    # Format mentor
    mentor = Mentor(
        id=mentor_user.id,
        username=mentor_user.username,
        email=mentor_user.email,
        full_name=f"{mentor_user.first_name} {mentor_user.last_name}" if hasattr(mentor_user, 'first_name') else mentor_user.username,
        bio=mentor_user.mentor_profile.bio if mentor_user.mentor_profile else None,
        experience_years=mentor_user.mentor_profile.experience_years if mentor_user.mentor_profile else None,
        hourly_rate=float(mentor_user.mentor_profile.hourly_rate) if mentor_user.mentor_profile and mentor_user.mentor_profile.hourly_rate else None
    )
    
    # Format institute
    institute = Institute(
        id=institute_user.id,
        username=institute_user.username,
        email=institute_user.email,
        institute_name=institute_user.institute_profile.institute_name if institute_user.institute_profile else None,
        description=institute_user.institute_profile.description if institute_user.institute_profile else None,
        website=institute_user.institute_profile.website if institute_user.institute_profile else None
    )
    
    return CollaborationDetails(
        id=collaboration.id,
        mentor=mentor,
        institute=institute,
        status=collaboration.status,
        hourly_rate=float(collaboration.hourly_rate) if collaboration.hourly_rate else None,
        hours_per_week=collaboration.hours_per_week,
        contract_terms=collaboration.contract_terms,
        start_date=collaboration.start_date,
        end_date=collaboration.end_date,
        created_at=collaboration.created_at,
        updated_at=collaboration.updated_at
    )
