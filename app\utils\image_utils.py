"""
Utility functions for handling image data in API responses
"""
import base64
import os
from typing import Dict, Optional, Any
from functools import lru_cache


@lru_cache(maxsize=128)
def _get_cached_image_data(image_path: str, file_size: int, file_mtime: float) -> Optional[Dict[str, Any]]:
    """
    Cached version of image processing to avoid redundant file reads
    Cache key includes file size and modification time to handle file changes
    """
    return _process_image_file(image_path)


def _process_image_file(image_path: str) -> Optional[Dict[str, Any]]:
    """Process image file and return base64 data with metadata"""
    try:
        # Handle both absolute and relative paths
        if image_path.startswith('/static/'):
            # Remove /static/ prefix and look in uploads directory
            file_path = os.path.join('uploads', image_path.replace('/static/', ''))
        elif image_path.startswith('profile_pictures/'):
            # Direct path to uploads
            file_path = os.path.join('uploads', image_path)
        elif image_path.startswith('uploads/'):
            # Already has uploads prefix
            file_path = image_path
        else:
            # Assume it's already a full path or relative to uploads
            file_path = os.path.join('uploads', image_path) if not os.path.isabs(image_path) else image_path

        if not os.path.exists(file_path):
            return {
                "data": None,
                "content_type": None,
                "filename": None,
                "size": None,
                "error": f"File not found: {file_path}",
                "url": image_path
            }

        # Read file and convert to base64
        with open(file_path, 'rb') as image_file:
            image_data = image_file.read()
            base64_data = base64.b64encode(image_data).decode('utf-8')

        # Determine content type based on file extension
        file_extension = os.path.splitext(file_path)[1].lower()
        content_type_map = {
            '.jpg': 'image/jpeg',
            '.jpeg': 'image/jpeg',
            '.png': 'image/png',
            '.gif': 'image/gif',
            '.webp': 'image/webp',
            '.bmp': 'image/bmp',
            '.svg': 'image/svg+xml'
        }
        content_type = content_type_map.get(file_extension, 'image/jpeg')

        return {
            "data": base64_data,
            "content_type": content_type,
            "filename": os.path.basename(file_path),
            "size": len(image_data),
            "url": image_path  # Keep original URL for reference
        }

    except Exception as e:
        return {
            "data": None,
            "content_type": None,
            "filename": None,
            "size": None,
            "error": str(e),
            "url": image_path
        }


def get_image_as_base64(image_path: str) -> Optional[Dict[str, Any]]:
    """
    Convert image file to base64 data with metadata

    Args:
        image_path: Path to the image file (can be relative or absolute)

    Returns:
        Dictionary containing base64 data and metadata, or None if no image
    """
    if not image_path:
        return None

    # Check if it's already a data URL (base64 encoded)
    if image_path.startswith('data:'):
        try:
            # Parse data URL: data:image/jpeg;base64,/9j/4AAQ...
            header, data = image_path.split(',', 1)
            content_type = header.split(':')[1].split(';')[0]

            return {
                "data": data,
                "content_type": content_type,
                "filename": f"image.{content_type.split('/')[-1]}",
                "size": len(data) * 3 // 4,  # Approximate size from base64
                "url": image_path
            }
        except Exception as e:
            return {
                "data": None,
                "content_type": None,
                "filename": None,
                "size": None,
                "error": f"Invalid data URL: {str(e)}",
                "url": image_path
            }

    try:
        # Additional validation to prevent data URLs from being treated as file paths
        if len(image_path) > 255:  # Typical max filename length
            return {
                "data": None,
                "content_type": None,
                "filename": None,
                "size": None,
                "error": f"Path too long (possibly a data URL): {len(image_path)} characters",
                "url": image_path[:100] + "..." if len(image_path) > 100 else image_path
            }

        # Resolve file path
        if image_path.startswith('/static/'):
            file_path = os.path.join('uploads', image_path.replace('/static/', ''))
        elif image_path.startswith('profile_pictures/'):
            file_path = os.path.join('uploads', image_path)
        elif image_path.startswith('uploads/'):
            file_path = image_path
        else:
            file_path = os.path.join('uploads', image_path) if not os.path.isabs(image_path) else image_path

        # Check if file exists
        if not os.path.exists(file_path):
            return {
                "data": None,
                "content_type": None,
                "filename": None,
                "size": None,
                "error": f"File not found: {file_path}",
                "url": image_path
            }

        # Get file stats for caching
        stat = os.stat(file_path)
        file_size = stat.st_size
        file_mtime = stat.st_mtime

        # Use cached version if available
        return _get_cached_image_data(image_path, file_size, file_mtime)

    except Exception as e:
        return {
            "data": None,
            "content_type": None,
            "filename": None,
            "size": None,
            "error": str(e),
            "url": image_path
        }


def add_image_data_to_response(response_data: Dict[str, Any], image_field: str = "profile_picture") -> Dict[str, Any]:
    """
    Add image data to a response dictionary
    
    Args:
        response_data: The response dictionary to modify
        image_field: The field name containing the image URL
        
    Returns:
        Modified response dictionary with image data added
    """
    if image_field in response_data and response_data[image_field]:
        image_data = get_image_as_base64(response_data[image_field])
        response_data[f"{image_field}_data"] = image_data
    
    return response_data


def get_profile_image_data(user_profile_picture: Optional[str], profile_image_url: Optional[str] = None) -> Optional[Dict[str, Any]]:
    """
    Get profile image data, preferring profile-specific image over user image
    
    Args:
        user_profile_picture: User's main profile picture
        profile_image_url: Profile-specific image URL (e.g., mentor profile image)
        
    Returns:
        Image data dictionary or None
    """
    # Prefer profile-specific image over user image
    image_url = profile_image_url or user_profile_picture
    return get_image_as_base64(image_url) if image_url else None
