"""
Event Booking Routes for EduFair Platform (Demo Mode)

This module provides simplified APIs for users to:
- Register for events
- Purchase tickets (auto-confirmed for demo)
- Handle booking confirmations
"""

from typing import Dict, Any, Optional, List
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from uuid import UUID
from datetime import datetime, timezone
from decimal import Decimal
from pydantic import BaseModel, Field

# Import dependencies
from config.session import get_db
from config.deps import oauth2_scheme, get_current_user
from config.permission import require_type

# Import CRUD operations
from Cruds.Events.Events import get_event_by_id
from Cruds.Events.EventTickets import get_event_ticket_by_id, check_ticket_availability
from Cruds.Events.EventRegistrations import create_event_registration, get_registration_by_id

# Import models
from Models.Events import Event, EventTicket, EventRegistration, EventPayment, PaymentStatusEnum, RegistrationStatusEnum
from Models.users import User

# Import schemas
from Schemas.Events.EventManagement import EventRegistrationCreate

router = APIRouter()


# ==================== DEMO BOOKING SCHEMAS ====================

class DemoEventBookingRequest(BaseModel):
    """Request to book an event ticket (demo mode)"""
    
    event_id: UUID = Field(..., description="Event ID")
    ticket_id: Optional[UUID] = Field(None, description="Ticket ID (optional for free events)")
    quantity: int = Field(1, ge=1, le=10, description="Number of tickets")
    
    # Attendee information
    attendee_info: Optional[Dict[str, Any]] = Field(None, description="Attendee information")
    special_requirements: Optional[str] = Field(None, description="Special requirements")
    emergency_contact: Optional[Dict[str, str]] = Field(None, description="Emergency contact info")
    dietary_preferences: Optional[str] = Field(None, description="Dietary preferences")
    accessibility_needs: Optional[str] = Field(None, description="Accessibility needs")


class DemoEventBookingResponse(BaseModel):
    """Response from event booking request (demo mode)"""
    
    registration_id: UUID = Field(..., description="Registration ID")
    event_id: UUID = Field(..., description="Event ID")
    ticket_id: Optional[UUID] = Field(None, description="Ticket ID")
    quantity: int = Field(..., description="Number of tickets")
    total_amount: Decimal = Field(..., description="Total amount")
    currency: str = Field(..., description="Currency")
    status: str = Field(..., description="Registration status")
    
    # Demo mode - all bookings auto-confirmed
    payment_completed: bool = Field(True, description="Payment status (demo: always completed)")
    registration_number: str = Field(..., description="Registration confirmation number")
    
    message: str = Field(..., description="Booking status message")


# ==================== DEMO BOOKING ROUTES ====================

@router.post("/book", response_model=DemoEventBookingResponse)
def book_event_demo(
    booking_request: DemoEventBookingRequest,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("student"))
):
    """
    Book an event with automatic confirmation (demo mode).
    
    This endpoint handles the complete booking flow:
    1. Validates event and ticket availability
    2. Creates registration record
    3. Auto-confirms booking (no payment processing)
    4. Returns booking confirmation
    """
    current_user = get_current_user(token, db)
    
    try:
        # 1. Validate event
        event = get_event_by_id(db, booking_request.event_id)
        if not event:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Event not found"
            )
        
        # 2. Validate ticket (if specified)
        ticket = None
        total_amount = Decimal('0.00')
        currency = 'PKR'
        
        if booking_request.ticket_id:
            ticket = get_event_ticket_by_id(db, booking_request.ticket_id)
            if not ticket or ticket.event_id != booking_request.event_id:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Ticket not found for this event"
                )
            
            # Check availability
            if not check_ticket_availability(db, booking_request.ticket_id, booking_request.quantity):
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Not enough tickets available"
                )
            
            total_amount = ticket.price * booking_request.quantity
            currency = ticket.currency
        
        # 3. Create registration
        registration_data = EventRegistrationCreate(
            event_id=booking_request.event_id,
            ticket_id=booking_request.ticket_id,
            quantity=booking_request.quantity,
            attendee_info=booking_request.attendee_info,
            special_requirements=booking_request.special_requirements,
            emergency_contact=booking_request.emergency_contact,
            dietary_preferences=booking_request.dietary_preferences,
            accessibility_needs=booking_request.accessibility_needs
        )
        
        registration = create_event_registration(db, registration_data, current_user.id)
        
        # 4. Demo mode - all bookings are auto-confirmed
        message = "Event booked successfully! (Demo mode - no payment required)"
        if total_amount > 0:
            message = f"Event booked successfully! Payment of {currency} {total_amount} auto-completed in demo mode."
        
        return DemoEventBookingResponse(
            registration_id=registration.id,
            event_id=booking_request.event_id,
            ticket_id=booking_request.ticket_id,
            quantity=booking_request.quantity,
            total_amount=total_amount,
            currency=currency,
            status="confirmed",
            payment_completed=True,
            registration_number=registration.registration_number,
            message=message
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to book event: {str(e)}"
        )


@router.get("/registrations/{registration_id}")
def get_booking_details(
    registration_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """Get booking details for a registration"""
    current_user = get_current_user(token, db)
    
    registration = get_registration_by_id(db, registration_id, current_user.id)
    if not registration:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Registration not found"
        )
    
    # Check if user owns this registration
    if registration.user_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied"
        )
    
    return {
        "registration_id": registration.id,
        "event_id": registration.event_id,
        "ticket_id": registration.ticket_id,
        "quantity": registration.quantity,
        "total_amount": registration.total_amount,
        "status": registration.status.value,
        "registration_number": registration.registration_number,
        "payment_status": registration.payment_status.value if registration.payment_status else None,
        "confirmed_at": registration.confirmed_at.isoformat() if registration.confirmed_at else None,
        "created_at": registration.created_at.isoformat(),
        "demo_mode": True
    }


@router.delete("/registrations/{registration_id}")
def cancel_booking(
    registration_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """Cancel a booking (demo mode)"""
    current_user = get_current_user(token, db)
    
    registration = get_registration_by_id(db, registration_id, current_user.id)
    if not registration:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Registration not found"
        )
    
    # Check if user owns this registration
    if registration.user_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied"
        )
    
    # Update registration status
    registration.status = RegistrationStatusEnum.CANCELLED
    registration.cancelled_at = datetime.now(timezone.utc)
    
    db.commit()
    
    return {
        "message": "Booking cancelled successfully",
        "registration_id": registration_id,
        "status": "cancelled",
        "demo_mode": True
    }
