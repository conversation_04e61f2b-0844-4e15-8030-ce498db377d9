"""
File Administration Routes

This module provides administrative routes for file management including
storage statistics, cleanup operations, and maintenance tasks.
"""

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import Dict, List
from datetime import datetime

from config.session import get_db
from config.security import oauth2_scheme
from config.permission import require_type
from services.file_management import file_manager
from config.logging import get_logger

logger = get_logger(__name__)
router = APIRouter()

# ===== STORAGE STATISTICS =====

@router.get("/storage/stats")
async def get_storage_stats(
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _=Depends(require_type("admin"))
) -> Dict:
    """
    Get storage usage statistics.
    
    Returns detailed information about file storage usage including
    total files, sizes, and breakdown by category.
    """
    try:
        stats = file_manager.get_storage_stats()
        logger.info("Storage statistics retrieved")
        return stats
    except Exception as e:
        logger.error(f"Failed to get storage stats: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to retrieve storage statistics")

# ===== ORPHANED FILES MANAGEMENT =====

@router.get("/orphaned-files")
async def find_orphaned_files(
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _=Depends(require_type("admin"))
) -> List[Dict]:
    """
    Find files that exist on disk but are not referenced in the database.
    
    These files are candidates for cleanup as they are no longer needed.
    """
    try:
        orphaned_files = file_manager.find_orphaned_files(db)
        logger.info(f"Found {len(orphaned_files)} orphaned files")
        return orphaned_files
    except Exception as e:
        logger.error(f"Failed to find orphaned files: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to find orphaned files")

@router.delete("/orphaned-files")
async def cleanup_orphaned_files(
    dry_run: bool = Query(True, description="If true, only simulate cleanup without deleting files"),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _=Depends(require_type("admin"))
) -> Dict:
    """
    Clean up orphaned files.
    
    Args:
        dry_run: If True, only simulate the cleanup without actually deleting files
    
    Returns:
        Dictionary with cleanup results including number of files deleted and space freed
    """
    try:
        result = file_manager.cleanup_orphaned_files(db, dry_run=dry_run)
        
        if dry_run:
            logger.info(f"DRY RUN: Would delete {result['deleted']} orphaned files")
        else:
            logger.info(f"Deleted {result['deleted']} orphaned files, freed {result['total_size_freed_mb']:.2f} MB")
        
        return result
    except Exception as e:
        logger.error(f"Failed to cleanup orphaned files: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to cleanup orphaned files")

# ===== OLD FILES CLEANUP =====

@router.delete("/old-files")
async def cleanup_old_files(
    days_old: int = Query(30, description="Delete files older than this many days"),
    dry_run: bool = Query(True, description="If true, only simulate cleanup without deleting files"),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _=Depends(require_type("admin"))
) -> Dict:
    """
    Clean up old files that are no longer referenced.
    
    Args:
        days_old: Delete files older than this many days
        dry_run: If True, only simulate the cleanup without actually deleting files
    
    Returns:
        Dictionary with cleanup results
    """
    try:
        if days_old < 1:
            raise HTTPException(status_code=400, detail="days_old must be at least 1")
        
        result = file_manager.cleanup_old_files(days_old=days_old, dry_run=dry_run)
        
        if dry_run:
            logger.info(f"DRY RUN: Would delete {result['deleted']} old files")
        else:
            logger.info(f"Deleted {result['deleted']} old files, freed {result['total_size_freed_mb']:.2f} MB")
        
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to cleanup old files: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to cleanup old files")

# ===== FILE INTEGRITY VALIDATION =====

@router.get("/integrity-check")
async def validate_file_integrity(
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _=Depends(require_type("admin"))
) -> Dict:
    """
    Validate that all database file references point to existing files.
    
    This helps identify broken file references that need to be cleaned up.
    """
    try:
        result = file_manager.validate_file_integrity(db)
        logger.info(f"File integrity check: {result['invalid_references']} missing files out of {result['total_checked']} checked")
        return result
    except Exception as e:
        logger.error(f"Failed to validate file integrity: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to validate file integrity")

# ===== BACKUP OPERATIONS =====

@router.post("/backup")
async def create_backup(
    backup_dir: str = Query(..., description="Directory where backup should be created"),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _=Depends(require_type("admin"))
) -> Dict:
    """
    Create a backup of all uploaded files.
    
    Args:
        backup_dir: Directory where the backup should be created
    
    Returns:
        Dictionary with backup results including path and statistics
    """
    try:
        result = file_manager.create_backup(backup_dir)
        
        if result.get("success"):
            logger.info(f"Backup created successfully: {result['backup_path']}")
        else:
            logger.error(f"Backup failed: {result.get('error', 'Unknown error')}")
        
        return result
    except Exception as e:
        logger.error(f"Failed to create backup: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to create backup")

# ===== MAINTENANCE OPERATIONS =====

@router.post("/maintenance/full-cleanup")
async def full_maintenance_cleanup(
    dry_run: bool = Query(True, description="If true, only simulate cleanup without deleting files"),
    days_old: int = Query(30, description="Delete orphaned files older than this many days"),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _=Depends(require_type("admin"))
) -> Dict:
    """
    Perform a comprehensive maintenance cleanup.
    
    This combines orphaned file cleanup and old file cleanup in a single operation.
    
    Args:
        dry_run: If True, only simulate the cleanup without actually deleting files
        days_old: Delete files older than this many days
    
    Returns:
        Dictionary with comprehensive cleanup results
    """
    try:
        # Get initial stats
        initial_stats = file_manager.get_storage_stats()
        
        # Cleanup orphaned files
        orphaned_result = file_manager.cleanup_orphaned_files(db, dry_run=dry_run)
        
        # Cleanup old files
        old_files_result = file_manager.cleanup_old_files(days_old=days_old, dry_run=dry_run)
        
        # Get final stats
        final_stats = file_manager.get_storage_stats()
        
        result = {
            "dry_run": dry_run,
            "initial_stats": initial_stats,
            "final_stats": final_stats,
            "orphaned_files_cleanup": orphaned_result,
            "old_files_cleanup": old_files_result,
            "total_files_deleted": orphaned_result["deleted"] + old_files_result["deleted"],
            "total_space_freed_mb": orphaned_result["total_size_freed_mb"] + old_files_result["total_size_freed_mb"]
        }
        
        if dry_run:
            logger.info(f"DRY RUN: Full maintenance would delete {result['total_files_deleted']} files, freeing {result['total_space_freed_mb']:.2f} MB")
        else:
            logger.info(f"Full maintenance completed: deleted {result['total_files_deleted']} files, freed {result['total_space_freed_mb']:.2f} MB")
        
        return result
        
    except Exception as e:
        logger.error(f"Failed to perform full maintenance cleanup: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to perform maintenance cleanup")

# ===== HEALTH CHECK =====

@router.get("/health")
async def file_system_health(
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _=Depends(require_type("admin"))
) -> Dict:
    """
    Get file system health information.
    
    Returns comprehensive information about file system status including
    storage stats, integrity check, and orphaned files count.
    """
    try:
        # Get storage stats
        storage_stats = file_manager.get_storage_stats()
        
        # Check file integrity
        integrity_result = file_manager.validate_file_integrity(db)
        
        # Find orphaned files
        orphaned_files = file_manager.find_orphaned_files(db)
        
        health_status = {
            "status": "healthy",
            "timestamp": datetime.utcnow().isoformat(),
            "storage_stats": storage_stats,
            "integrity_check": {
                "total_checked": integrity_result["total_checked"],
                "missing_files": len(integrity_result["missing_files"]),
                "integrity_percentage": round(
                    ((integrity_result["total_checked"] - integrity_result["invalid_references"]) / 
                     max(integrity_result["total_checked"], 1)) * 100, 2
                )
            },
            "orphaned_files": {
                "count": len(orphaned_files),
                "total_size_mb": round(sum(f["size_mb"] for f in orphaned_files), 2)
            }
        }
        
        # Determine overall health status
        if integrity_result["invalid_references"] > 0:
            health_status["status"] = "warning"
            health_status["issues"] = [f"{integrity_result['invalid_references']} missing file references"]
        
        if len(orphaned_files) > 10:  # Threshold for concern
            health_status["status"] = "warning"
            if "issues" not in health_status:
                health_status["issues"] = []
            health_status["issues"].append(f"{len(orphaned_files)} orphaned files found")
        
        return health_status
        
    except Exception as e:
        logger.error(f"Failed to get file system health: {str(e)}")
        return {
            "status": "error",
            "timestamp": datetime.utcnow().isoformat(),
            "error": str(e)
        }
