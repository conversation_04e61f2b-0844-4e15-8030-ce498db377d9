"""
Chat Routes for EduFair Platform

This module provides APIs for chat functionality between users.
"""

from fastapi import APIRouter, Depends, HTTPException, status, Request
from sqlalchemy.orm import Session
from uuid import UUID
from typing import List

# Import dependencies
from config.session import get_db
from config.deps import oauth2_scheme, get_current_user

# Import CRUD operations
from Cruds.Social.Chat import (
    send_message, get_message, mark_message_as_read, delete_message,
    get_conversations, get_message_history, get_chat_stats,
    mark_conversation_as_read
)
from Cruds.Notifications import create_message_notification
from Cruds.AdminLog import log_user_action

# Import schemas
from Schemas.Social.Chat import (
    ChatMessageCreate, ChatMessageResponse, ChatMessageWithUsers,
    ConversationListResponse, MessageHistoryResponse, ChatStats,
    BulkMessageRead, BulkMessageDelete, BulkOperationResponse
)
from Schemas.AdminLog import LogAction, ResourceType

router = APIRouter()


# ==================== MESSAGE OPERATIONS ====================

@router.post("/send", response_model=ChatMessageResponse)
def send_chat_message(
    message_data: ChatMessageCreate,
    request: Request,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """
    Send a chat message to another user.
    
    Users can only message each other if they follow each other.
    """
    current_user = get_current_user(token, db)
    
    try:
        # Send message
        message = send_message(db, current_user.id, message_data)
        
        # Create notification for receiver
        try:
            create_message_notification(
                db, current_user.id, message_data.receiver_id, message_data.message
            )
        except Exception as e:
            print(f"Failed to create message notification: {e}")
        
        # Log the action
        log_user_action(
            db=db,
            action=LogAction.MESSAGE_SEND,
            resource_type=ResourceType.MESSAGE,
            user_id=current_user.id,
            resource_id=message.id,
            details={
                "sender_username": current_user.username,
                "receiver_id": str(message_data.receiver_id),
                "message_length": len(message_data.message)
            },
            request=request
        )
        
        return message
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to send message: {str(e)}"
        )


@router.get("/message/{message_id}", response_model=ChatMessageWithUsers)
def get_chat_message(
    message_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """
    Get a specific chat message with user details.
    
    Users can only view messages they sent or received.
    """
    current_user = get_current_user(token, db)
    
    try:
        message = get_message(db, message_id, current_user.id)
        return message
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get message: {str(e)}"
        )


@router.put("/message/{message_id}/read")
def mark_message_read(
    message_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """
    Mark a message as read.
    
    Only the receiver can mark a message as read.
    """
    current_user = get_current_user(token, db)
    
    try:
        success = mark_message_as_read(db, message_id, current_user.id)
        
        if success:
            return {"message": "Message marked as read", "success": True}
        else:
            return {"message": "Message not found or already read", "success": False}
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to mark message as read: {str(e)}"
        )


@router.delete("/message/{message_id}")
def delete_chat_message(
    message_id: UUID,
    request: Request,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """
    Delete a chat message.
    
    Only the sender can delete their own messages.
    """
    current_user = get_current_user(token, db)
    
    try:
        success = delete_message(db, message_id, current_user.id)
        
        # Log the action
        log_user_action(
            db=db,
            action=LogAction.MESSAGE_DELETE,
            resource_type=ResourceType.MESSAGE,
            user_id=current_user.id,
            resource_id=message_id,
            details={
                "sender_username": current_user.username,
                "action": "soft_delete"
            },
            request=request
        )
        
        return {"message": "Message deleted successfully", "success": success}
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete message: {str(e)}"
        )


# ==================== CONVERSATION OPERATIONS ====================

@router.get("/conversations", response_model=ConversationListResponse)
def get_user_conversations(
    page: int = 1,
    page_size: int = 20,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """
    Get list of conversations for the current user.
    
    Returns conversations ordered by last activity.
    """
    current_user = get_current_user(token, db)
    
    try:
        conversations = get_conversations(db, current_user.id, page, page_size)
        return conversations
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get conversations: {str(e)}"
        )


@router.get("/conversation/{user_id}/messages", response_model=MessageHistoryResponse)
def get_conversation_messages(
    user_id: UUID,
    page: int = 1,
    page_size: int = 50,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """
    Get message history with a specific user.
    
    Returns messages ordered by sent time (newest first).
    """
    current_user = get_current_user(token, db)
    
    try:
        history = get_message_history(db, current_user.id, user_id, page, page_size)
        return history
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get message history: {str(e)}"
        )


@router.put("/conversation/{user_id}/read")
def mark_conversation_read(
    user_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """
    Mark all messages in a conversation as read.
    
    Marks all unread messages from the specified user as read.
    """
    current_user = get_current_user(token, db)
    
    try:
        count = mark_conversation_as_read(db, current_user.id, user_id)
        
        return {
            "message": f"Marked {count} messages as read",
            "messages_marked": count
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to mark conversation as read: {str(e)}"
        )


# ==================== CHAT STATISTICS ====================

@router.get("/stats", response_model=ChatStats)
def get_user_chat_stats(
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """
    Get chat statistics for the current user.
    
    Returns conversation count, message counts, and unread count.
    """
    current_user = get_current_user(token, db)
    
    try:
        stats = get_chat_stats(db, current_user.id)
        return stats
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get chat stats: {str(e)}"
        )


# ==================== BULK OPERATIONS ====================

@router.post("/bulk/read", response_model=BulkOperationResponse)
def bulk_mark_messages_read(
    bulk_request: BulkMessageRead,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """
    Mark multiple messages as read.
    
    Allows marking up to 100 messages as read in a single request.
    """
    current_user = get_current_user(token, db)
    
    try:
        successful_operations = []
        failed_operations = []
        
        for message_id in bulk_request.message_ids:
            try:
                success = mark_message_as_read(db, message_id, current_user.id)
                if success:
                    successful_operations.append(message_id)
                else:
                    failed_operations.append({
                        "message_id": message_id,
                        "error": "Message not found or already read"
                    })
            except Exception as e:
                failed_operations.append({
                    "message_id": message_id,
                    "error": str(e)
                })
        
        return BulkOperationResponse(
            successful_operations=successful_operations,
            failed_operations=failed_operations,
            total_processed=len(bulk_request.message_ids),
            success_count=len(successful_operations),
            failure_count=len(failed_operations)
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to bulk mark messages as read: {str(e)}"
        )


@router.post("/bulk/delete", response_model=BulkOperationResponse)
def bulk_delete_messages(
    bulk_request: BulkMessageDelete,
    request: Request,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """
    Delete multiple messages.
    
    Allows deleting up to 100 messages in a single request.
    Only the sender can delete their own messages.
    """
    current_user = get_current_user(token, db)
    
    try:
        successful_operations = []
        failed_operations = []
        
        for message_id in bulk_request.message_ids:
            try:
                success = delete_message(db, message_id, current_user.id)
                if success:
                    successful_operations.append(message_id)
                else:
                    failed_operations.append({
                        "message_id": message_id,
                        "error": "Message not found or cannot be deleted"
                    })
            except Exception as e:
                failed_operations.append({
                    "message_id": message_id,
                    "error": str(e)
                })
        
        # Log the bulk action
        log_user_action(
            db=db,
            action=LogAction.MESSAGE_DELETE,
            resource_type=ResourceType.MESSAGE,
            user_id=current_user.id,
            details={
                "action_type": "bulk_delete",
                "message_count": len(bulk_request.message_ids),
                "success_count": len(successful_operations),
                "failure_count": len(failed_operations),
                "permanent": bulk_request.permanent
            },
            request=request
        )
        
        return BulkOperationResponse(
            successful_operations=successful_operations,
            failed_operations=failed_operations,
            total_processed=len(bulk_request.message_ids),
            success_count=len(successful_operations),
            failure_count=len(failed_operations)
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to bulk delete messages: {str(e)}"
        )
